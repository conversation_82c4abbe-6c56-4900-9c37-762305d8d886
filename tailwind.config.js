const { progress } = require('framer-motion');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
        outfit: ['Outfit', 'sans-serif'],
        obviously: ['Obviously', 'sans-serif'],
      },

      colors: {
        primaryBorder: 'var(--primary-border)',
        sidebarBgColor: 'var(--sidebar-bg-color)',
        inputBgColor: 'var(--input-bg-color)',
        placeholderColor: 'var(-placeholder-color)',
        secondaryBtnBg: 'var(--secondary-btn-bg-color)',
        heroBannerBorder: 'var(--hero-border-color)',
        avatarBorder:'var(--avatar-border)',
        gameCardBorder:'var(--game-card-border)',
        gameTabBorder:'var(--game-tab-border)',
        barBg:'var(--bar-bg)',

        primary: {
          500: 'var(--primary-500)',
          700: 'var(--primary-700)',
          800: 'var(--primary-800)',
          900: 'var(--primary-900)',
          1000: 'var(--primary-1000)',
        },



        cetaceanBlue: {
          1000: 'var(--cetaceanBlue-1000)',
          2000: 'var(--color-1000)',
          3000: 'var(--color-2000)',
          4000: 'var(--steelTeal-200)'
        },

        cetaceanBlue2: {
          2000: 'var(--cetaceanBlue-2000)',
        },

        maastrichtBlue: {
          200: 'var(--maastrichtBlue-200)',
          500: 'var(--maastrichtBlue-500)',
          1000: 'var(--maastrichtBlue-1000)',
        },

        oxfordBlue: {
          900: 'var(--oxfordBlue-900)',
          1000: 'var(--oxfordBlue-1000)',
        },

        steelTeal: {
          100: 'var(--steelTeal-100)',
          200: 'var(--steelTeal-200)',
          300: 'var(--steelTeal-200)',
          310: 'var(--steelTeal-310)',
          500: 'var(--steelTeal-500)',
          600: 'var(--steelTeal-600)',
          620: 'var(--steelTeal-620)',
          800: 'var(--steelTeal-800)',
          1000: 'var(--steelTeal-1000)',
        },

        richBlack: {
          200: 'var(--richBlack-200)',
          300: 'var(--richBlack-300)',
          400: 'var(--richBlack-400)',
          500: 'var(--richBlack-500)',
          600: 'var(--richBlack-600)',
          610: 'var(--richBlack-610)',
          620: 'var(--richBlack-620)',
          700: 'var(--richBlack-700)',
          800: 'var(--richBlack-700)',
          900: 'var(--richBlack-900)',
          1000: 'var(--richBlack-1000)',
        
        },

        fluorescentBlue: {
          1000: 'var(--fluorescentBlue-1000)',
        },

        green: {
          100: 'var(--green-100)',
          200: 'var(--green-200)',
          500: 'var(--green-500)',
          600: 'var(--green-600)',
          900: 'var(--green-900)',
          1000: 'var(--green-1000)',
        },

        orange: {
          800: 'var(--orange-800)',
          1000: 'var(--orange-1000)',
        },

        black: {
          200: 'var(--black-200)',
          500: 'var(--black-500)',
          850: 'var(--black-850)',
          900: 'var(--black-900)',
          1000: 'var(--black-1000)',
        },

        white: {
          100: 'var(--white-100)',
          200: 'var(--white-200)',
          300: 'var(--white-300)',
          350: 'var(--white-350)',
          360: 'var(--white-360)',
          370: 'var(--white-370)',
          400: 'var(--white-400)',
          450: 'var(--white-450)',
          460: 'var(--white-460)',
          500: 'var(--white-500)',
          700: 'var(--white-700)',
          750: 'var(--white-750)',
          1000: 'var(--white-1000)',
        },

        tiber: {
          1000: 'var(--tiber-1000)',
        },

        scarlet: {
          100: 'var(--scarlet-100)',
          200: 'var(--scarlet-200)',
          700: 'var(--scarlet-700)',
          900: 'var(--scarlet-900)',
        },

        slateGray: {
          400: 'var(--slate-gray-400)',
          500: 'var(--slate-gray-500)',
          600: 'var(--slate-gray-600)',
          650: 'var(--slate-gray-650)',
          700: 'var(--slate-gray-700)',
          800: 'var(--slate-gray-800)',
          900: 'var(--slate-gray-900)',
          1000:'var(--slate-gray-1000)',
        },
        erieBlack: {
          100: 'var(--erie-black-100)',
          200: 'var(--erie-black-200)',
          300: 'var(--erie-black-300)',
          400: 'var(--erie-black-400)',
        },

        red: {
          1000: 'var(--red-1000)',
        },

        darkBlue: {
          800: 'var(--dark-blue-800)',
        },

        golden: {
          500: 'var(--golden-500)',
          550: 'var(--golden-550)',
          600: 'var(--golden-600)',
        },

        borderColor: {
          100: 'var(--border-color-100)',
        },
      },

      spacing: {
        sidebarWidth: 'var(--sidebar-wdith)',
        headerHeight: 'var(--header-height)',
        containerWidth: 'var(--container-width)',
        chatWidth: 'var(--chat-width)',
        menuFooterHeight: 'var(--menu-footer-height)',
      },

      borderRadius: {
        px_10: '.625rem',
      },

      backgroundImage: {
        // GRADIENT COLOR
        progressBg:
          'linear-gradient(0deg, #BB7733 0%, #C17F37 7%, #D19844 20.01%, #EABB58 33.01%, #EECE6C 47.02%, #F2DD7B 61.02%, #ECC764 82.03%, #EABB58 99.03%)',
        'nav-gradient':
          'linear-gradient(90deg, rgba(235, 190, 91, 0) 46.57%, rgba(192, 126, 54, 0.4) 100%)',
        'profile-nav-gradient':
          'linear-gradient((90deg,_rgba(182,_14,_1,_0.2)_46.57%,_rgba(182,_14,_1,_0.4)_100%),_#002235)',
        'select-hover':
          'linear-gradient(90deg, rgba(182, 14, 1, 0.2) 46.57%, rgba(182, 14, 1, 0.4) 100%)',
        vipClub:
          'linear-gradient(270deg, rgba(11, 29, 65, 0) 0%, #083082 100%)',
        snipWheelBackground:
          'linear-gradient(52deg, rgba(182, 14, 1, 1) 0%, rgba(0, 0, 0, 0.7847514005602241) 35%, rgba(182, 14, 1, 1) 100%)',
        walletBg:
          ' linear-gradient(90deg, rgba(235, 190, 91, 0) -7.77%, rgba(192, 126, 54, 0.5) 100%)',
        TintGoldGradient:
          'linear-gradient(180deg, #E2BD68 0%, #ECD782 50%, #B57F44 100%)',
          BadgeGradientBg:'linear-gradient(180deg, rgba(235, 208, 125, 0.24) 0%, rgba(213, 185, 99, 0.24) 100%)',
          cardBorderGradient:'linear-gradient(to bottom, #f8e48b, #e0a84a, #b6732d)',
          gameTabsBg:'linear-gradient(180deg, rgba(230, 209, 136, 0.24) 0%, rgba(208, 186, 111, 0.24) 100%)',
          gradientIcon: 'linear-gradient(180deg, #E9BB58 0%, #F0D674 50%, #BF7C37 100%)',
          reverseGradientBg: 'linear-gradient(180deg, #BF7C37 0%, #F0D674 50%, #E9BB58 100%)',
          textGradient: 'linear-gradient(180deg, #E9BB58 0%, #F0D674 50%, #BF7C37 100%)' ,
          progressBarGradient:'linear-gradient(90deg, #FD0009 0%, #F4CE5A 100%)',
          tabBottomBorder:'linear-gradient(270deg, rgba(233, 187, 88, 0.1) -7.12%, #BF7C37 54.84%, rgba(233, 187, 88, 0.1) 116.79%)',          
          accordionGradient:'linear-gradient(180deg, #E9BB58 60.71%, #F0D674 80.36%, #BF7C37 100%)',
          vipTextGradient:'linear-gradient(180deg, #FFEEB2 0%, #F7D56A 100%)',
          tabTextGradient:'linear-gradient(180deg, color(display-p3 1.000 0.937 0.725) 0%, color(display-p3 0.945 0.839 0.478) 100%)',


        // BACKGROUND IMAGE
        'auth-bg-desktop': 'url("/assets/demo-image/auth-bg-desktop.jpg")',
        'auth-bg-mobile':
          'url("../assets/images/stock-images/auth-bg-mobile.png")',
        'header-mask-green':
          'url("../assets/images/svg-images/header-mask-green.svg")',
        'header-mask-orange':
          'url("../assets/images/svg-images/header-mask-orange.svg")',
        'checkbox-check': 'url("/check-icon.svg")',
        'profile-sidebar':
          'url("../assets/images/stock-images/profile-sidebar.png")',
        'vip-tier': 'url("/assets/image/png/vipTierBg.png")',
        'vip-Container-bg': 'url("/assets/image/png/vipContainerBg.png")',
        'heroBannerBg': 'url("/assets/webp/hero-img.webp")',
        'heroMobBannerBg': 'url("/assets/webp/hero-mob-banner.webp")',        
        'groupBanner': 'url("/assets/webp/group-banner.webp")',
        'vipBanner': 'url("/assets/webp/vip-banner.webp")',
        'vipMobBanner': 'url("/assets/webp/vip-mob-banner.webp")',
        'rewardBg': 'url("/assets/webp/reward-bg.webp")',
        'promotionBanner': 'url("/assets/webp/promotion-web-banner.webp")',
        'promotionMobBanner': 'url("/assets/webp/promotion-mob-banner.webp")',
      },

     


      keyframes: {
        heroOneText: {
          '0%': { transform: 'translateY(100%) scale(0.8)' },
          '10%': { transform: 'translateY(0%) scale(1)' },
          '25%': { transform: 'translateY(0%) scale(1)' },
          '40%': { transform: 'translateY(0%) scale(1)' },
          '47.5%': { transform: 'translateY(100%) scale(0.8)' },
        },

        heroTwoText: {
          '50%': { transform: 'translateY(100%) scale(0.8)' },
          '60%': { transform: 'translateY(0%) scale(1)' },
          '75%': { transform: 'translateY(0%) scale(1)' },
          '90%': { transform: 'translateY(0%) scale(1)' },
          '100%': { transform: 'translateY(100%) scale(0.8)' },
        },

        whiteShadow: {
          '0%': { right: ' -2.5rem ' },
          '49%': { right: '110%' },
          '100%': { right: '-2.5rem' },
        },
      },

      animation: {
        heroOneText: 'heroOneText 3s linear 0s infinite ',
        heroTwoText: 'heroTwoText 3s linear 0s infinite',
        whiteShadow: 'whiteShadow 3s ease-out 1.2s infinite',
      },

      boxShadow: {
        header: '0px 10px 25px var(--primary-750)',
        container: '0px 4px 4px var(--black-250)',
        'chat-header': '0px 4px 4px var(--black-250)',
        'bottom-menu': '0px 4px 4px var(--black-250)',
        vipLevelShadow:
          ' rgba(182, 14, 1, 1.17) 0px -3px 49px 1px inset, rgba(182, 14, 1, 0.15) 0px 15px 16px 0px inset, rgba(182, 14, 1, 0.1) 1px -59px 8px 0px inset, rgba(182, 14, 1, 0.06) 0px 2px 1px, rgba(182, 14, 1, 0.09) 0px 4px 2px, rgba(182, 14, 1, 0.09) 0px 0px 0px, rgba(182, 14, 1, 0.09) 0px 0px 0px, rgba(182, 14, 1, 0.09) 0px 0px 0px',
        inputInsetShadow: 'inset 0 0 7px 1px rgb(0 0 0)',
        vipCardShasow: 'inset 0 0 35px 1px #E52227',
        vipCardShasowdisable: 'inset 0 0 35px 1px #5C5C5C',
        tabsShadow:' 0px 1px 0px 0px hsla(50, 3%, 20%, 1) inset',
        chatInputShadow:'0px -4px 10px 0px rgba(0, 12, 26, 1)',
        chatHeaderShadow:'0px 4px 4px 0px #00000040'
      },

      dropShadow: {
        vipCoinShadow: '0 0 0.75rem var(--scarlet-900)',
      },
    },
    screens: {
      xxs: '450px',
      xs: '475px',
      sm: '640px',
      md: '768px',
      lmd: '991px',
      lg: '1024px',
      lgx: '1340px',
      xl: '1280px',
     
      xxl: '1600px',
      '2xl' :"1480px",
      '3xl': "1780",
      // '3xl': '1600px',


      'max-3xl': { max: '1780px' },
      // => @media (max-width: 1779.98px) { ... }
      'max-xxl': { max: '1600px' },
      // => @media (max-width: 1535.98px) { ... }
      'max-2xl': { max: '1480px' },
      // => @media (max-width: 1479.98px) { ... }
      'max-xl': { max: '1279.98px' },
      // => @media (max-width: 1279.98px) { ... }

      'max-lg': { max: '1023.98px' },
      // => @media (max-width: 1023.98px) { ... }
      'max-lmd': { max: '991.98px' },
      // => @media (max-width: 991.98px) { ... }
      'max-md': { max: '767.98px' },
      // => @media (max-width: 767.98px) { ... }

      'max-sm': { max: '639.98px' },
      // => @media (max-width: 639.98px) { ... }

      'max-xs': { max: '474.98px' },
      // => @media (max-width: 474.98px) { ... }

      'max-xxs': { max: '449.98px' },
      // => @media (max-width: 474.98px) { ... }

      tabletView: { 'min': '1280px', 'max': '1520px' },

      desktop: { min: '1341px', max: '1400.98px' },

      uppertab: { min: '1280px', max: '1340.98px' },
    },
  

    transform: {
      scale3d: 'scale3d(1.2, 1.5, 1.5)', // Default value, you can customize it
    },
    // maxWidth: {
    //   profileSidebar: '275px',
    //   fullwidth:'100%',
    //   providerWidth:'94px',

    // },
  },
  plugins: [
  // require('@tailwindcss/line-clamp'),
],
};
