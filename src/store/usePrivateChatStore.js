import { create } from 'zustand';
// import { persist } from 'zustand/middleware';

const usePrivateChatStore = create(
  // persist(
  (set) => ({
    privateChat: [],              // right-side recent chat items
    privateChatForPage: [],       // center screen messages

    userId: null,
    isPrivateChatOpen: false,
    recipientUser: null,
    isCallActive: false,
    searchUserName: '',
    showSearchInput: false,
    callId: null,
    channelName: '',
    selectedRecentChatTab: false,

    toggleSearchInput: () =>
      set((state) => ({ showSearchInput: !state.showSearchInput })),
    setShowSearchInput: (value) => set({ showSearchInput: value }),
    setSearchUserName: (data) => set(() => ({ searchUserName: data })),
    setIsCallActive: (data) => set(() => ({ isCallActive: data })),
    setCallId: (data) => set(() => ({ callId: data })),
    setChannelName: (data) => set(() => ({ channelName: data })),

    setIsPrivateChatOpen: (data) => {
      if (data) {
        set(() => ({ isPrivateChatOpen: data }));
      } else {
        set(() => ({
          privateChat: [],
          // userId: null,
          isPrivateChatOpen: data,
          recipientUser: null,
        }));
      }
    },

    setUserId: (data) => set(() => ({ userId: data })),
    setRecipientUser: (data) => set(() => ({ recipientUser: data })),

    // Right side chat items
    setPrivateChat: (data) => {
      set(() => ({ privateChat: data?.slice().reverse() }));
    },
    appendPrivateChat: (message) => {
      set((state) => ({ privateChat: [...state.privateChat, message] }));
    },

    // Center screen chat
    setPrivateChatForPage: (data) => {
      set(() => ({ privateChatForPage: data?.slice().reverse() }));
    },
    appendPrivateChatForPage: (message) => {
      set((state) => ({
        privateChatForPage: [...state.privateChatForPage, message],
      }));
    },

    setSelectedRecentChatTab: (data) =>
      set(() => ({ selectedRecentChatTab: data })),
  }),
);

export default usePrivateChatStore;
