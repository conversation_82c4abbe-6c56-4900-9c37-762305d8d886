import { create } from 'zustand';
import useAuthStore from './useAuthStore';
// import { persist } from 'zustand/middleware';

const getInitialSelectedTab = () => {
  try {
    const { isAuthenticated } = useAuthStore.getState();
    return isAuthenticated ? 'MYGroups' : 'PublicGroups';
  } catch (error) {
    return 'PublicGroups';
  }
};

const useGroupChatStore = create(
  (set) => ({
    groupChat: [],
    groupChatForPage: [],

    groupId: null,
    userId: null,
    groupName: '',
    isGroupChatOpen: false,
    livePlayersCountInGroup: 0,
    recipientUser: null,
    isCallActive: false,
    groupCallDetails: {},

    selectedGroupTab: getInitialSelectedTab(),
    callInitialized: { groupId: null },

    setLivePlayersCountInGroup: (data) => {
      set(() => ({ livePlayersCountInGroup: data }));
    },
    setIsCallActive: (data) => {
      set(() => ({ isCallActive: data }));
    },
    setIsGroupChatOpen: (data) => {
      console.log(data, ':::::::::::::::::::data group');
      if (data) {
        set(() => ({ isGroupChatOpen: data }));
      } else {
        set(() => ({
          groupChat: [],
          userId: null,
          groupId: null,
          isGroupChatOpen: data,
          recipientUser: null,
        }));
      }
    },
    setUserId: (data) => {
      set(() => ({ userId: data }));
    },
    setGroupId: (data) => {
      set(() => ({ groupId: data }));
    },
    setGroupName: (data) => {
      set(() => ({ groupName: data }));
    },
    setRecipientUser: (data) => {
      set(() => ({ recipientUser: data }));
    },
    setGroupChat: (data) => {
      set(() => ({ groupChat: data?.slice().reverse() }));
    },
    setGroupChatForPage: (data) => {
      set(() => ({ groupChatForPage: data?.slice().reverse() }));
    },
    appendGroupChat: (message) => {
      set((state) => ({ groupChat: [...state.groupChat, message] }));
    },
    appendGroupChatForPage: (message) => {
      set((state) => ({
        groupChatForPage: [...state.groupChatForPage, message],
      }));
    },
    setGroupCallDetails: (data) => {
      set(() => ({ groupCallDetails: data }));
    },
    setSelectedGroupTab: (data) => {
      set((state) => ({ selectedGroupTab: data }));
    },
    setCallInitialized: (data) => {
      set(() => ({ callInitialized: data }));
    },
  }),
  //   {
  //     name: 'useGroupChatStore',
  //   },
  // ),
);

export default useGroupChatStore;
