import { create } from "zustand";

const useDraggableStore = create((set) => ({
  component: null,    // JSX content
  gameName: null,     // game identifier
  isOpen: false,      // open/close state

  openDraggablePopup: (component, name) =>
    set({ component, gameName: name, isOpen: true }),

  updateDraggableContent: (component, name) =>
    set((state) =>
      state.isOpen
        ? { component, gameName: name } 
        : state
    ),

  closeDraggablePopup: () =>
    set({ component: null, gameName: null, isOpen: false }),
}));

export default useDraggableStore;
