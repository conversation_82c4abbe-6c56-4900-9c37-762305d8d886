import { create } from 'zustand';

const useGeneralStore = create((set) => ({
  openChat: true,
  activeMenu: '/',
  openLobbyMenu:false,
  activePreviousState:"",
  setOpenChat: (data) => {
    set(() => ({ openChat: data }));
  },
  openMenu: false,
  setOpenMenu: (data) => {
    set(() => ({ openMenu: data }));
  },
  setActiveMenu: (data) => {
    set(() => ({ activeMenu: data }));
  },
  setOpenLobby: (data) => {
    set(() => ({ openLobbyMenu: data }));
  },
  setActivePreviousState: (data) => {
    set(() => ({ activePreviousState: data }));
  },
}));

export default useGeneralStore;
