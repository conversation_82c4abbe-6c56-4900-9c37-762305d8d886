'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAuthStore = create(
persist(
  (set) => ({
    isAuthenticated: false,
    coin: 'AC',
    userDetails: null,
    userWallet: null,
    hasRehydrated: false,
    setUserDetails: (data) => {
      set(() => ({
        userDetails: data,
        userWallet: { amount: 0 },
      }));
    },
    setUserWallet: (data) => {
      set(() => ({
        userWallet: { amount: data?.amount },
      }));
    },
    setIsAuthenticated: (data) => {
      set(() => ({ isAuthenticated: data }));
    },
    logout: () => {
      set({ isAuthenticated: false, userDetails: null });
    },
    setCoin: (data) => {
      set(() => ({ coin: data }));
    },
    setHasRehydrated: (data) => {
      set(() => ({ hasRehydrated: data }));
    },
  }),
  {
    name: 'useAuthStore',
    partialize: (state) => ({
      // isAuthenticated: state.isAuthenticated, // ✅ keep auth state
      coin: state.coin,
      userDetails: state.userDetails,
      userWallet: state.userWallet,
      hasRehydrated: state.hasRehydrated,
    }),
    onRehydrateStorage: () => (state) => {
      state.setHasRehydrated(true);
    },
  }
)

);

export default useAuthStore;
