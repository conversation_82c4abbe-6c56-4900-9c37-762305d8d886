import { create } from 'zustand';

const useActiveGroupStore = create((set) => ({
  myGroups: [],
  publicGroups: [],
  myGroupsLoading: true,
  publicGroupsLoading: true,
  activeTab: 0,
  connectedPlay: {},
  isMuted:true, 
  setMyGroups: (myGroups) => set({ myGroups }),
  setMyGroupsLoading: (data) => set({ myGroupsLoading: data }),
  setPublicGroups: (publicGroups) => set({ publicGroups }),
  setPublicGroupsLoading: (data) => set({ publicGroupsLoading: data }),
  setActiveTab: (data) => set({ activeTab: data }),
  setConnectedPlay: (data) => set({ connectedPlay: data }),
  setIsMuted: (data) => set({ isMuted: data }),
}));

export default useActiveGroupStore;
