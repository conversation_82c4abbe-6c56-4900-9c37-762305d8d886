/* Digitalt Font Start Here */
@font-face {
  font-family: 'Digitalt';
  src: url('../assets/fonts/Digitalt.eot');
  src: url('../assets/fonts/Digitalt.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Digitalt.woff2') format('woff2'),
    url('../assets/fonts/Digitalt.woff') format('woff'),
    url('../assets/fonts/Digitalt.ttf') format('truetype'),
    url('../assets/fonts/Digitalt.svg#Digitalt') format('svg');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

.font-digitalt {
  font-family: 'Digitalt' !important;
}


/* Nunito Font Start Here */
@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-ExtraLight.eot');
  src: url('../assets/fonts/Nunito-ExtraLight.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-ExtraLight.woff2') format('woff2'),
    url('../assets/fonts/Nunito-ExtraLight.woff') format('woff'),
    url('../assets/fonts/Nunito-ExtraLight.ttf') format('truetype'),
    url('../assets/fonts/Nunito-ExtraLight.svg#Nunito-ExtraLight') format('svg');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Light.eot');
  src: url('../assets/fonts/Nunito-Light.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Light.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Light.woff') format('woff'),
    url('../assets/fonts/Nunito-Light.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Light.svg#Nunito-Light') format('svg');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Regular.eot');
  src: url('../assets/fonts/Nunito-Regular.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Regular.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Regular.woff') format('woff'),
    url('../assets/fonts/Nunito-Regular.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Regular.svg#Nunito-Regular') format('svg');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Medium.eot');
  src: url('../assets/fonts/Nunito-Medium.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Medium.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Medium.woff') format('woff'),
    url('../assets/fonts/Nunito-Medium.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Medium.svg#Nunito-Medium') format('svg');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-SemiBold.eot');
  src: url('../assets/fonts/Nunito-SemiBold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-SemiBold.woff2') format('woff2'),
    url('../assets/fonts/Nunito-SemiBold.woff') format('woff'),
    url('../assets/fonts/Nunito-SemiBold.ttf') format('truetype'),
    url('../assets/fonts/Nunito-SemiBold.svg#Nunito-SemiBold') format('svg');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Bold.eot');
  src: url('../assets/fonts/Nunito-Bold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Bold.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Bold.woff') format('woff'),
    url('../assets/fonts/Nunito-Bold.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Bold.svg#Nunito-Bold') format('svg');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-ExtraBold.eot');
  src: url('../assets/fonts/Nunito-ExtraBold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-ExtraBold.woff2') format('woff2'),
    url('../assets/fonts/Nunito-ExtraBold.woff') format('woff'),
    url('../assets/fonts/Nunito-ExtraBold.ttf') format('truetype'),
    url('../assets/fonts/Nunito-ExtraBold.svg#Nunito-ExtraBold') format('svg');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Black.eot');
  src: url('../assets/fonts/Nunito-Black.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Black.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Black.woff') format('woff'),
    url('../assets/fonts/Nunito-Black.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Black.svg#Nunito-Black') format('svg');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

.font-nunito {
  font-family: 'Nunito' !important;
}


/* inter font Start Here */

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  src: url(../assets/fonts/inter/Inter_28pt-Black.ttf) format('truetype');
  font-weight: 900;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  src: url(../assets/fonts/inter/Inter_28pt-ExtraBold.ttf) format('truetype');
  font-weight: 800;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  src: url(../assets/fonts/inter/Inter_28pt-Bold.ttf) format('truetype');
  font-weight: 700;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  src: url(../assets/fonts/inter/Inter_28pt-SemiBold.ttf) format('truetype');
  font-weight: 600;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  src: url(../assets/fonts/inter/Inter_28pt-Medium.ttf) format('truetype');
  font-weight: 500;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-display: swap;
  src: url(../assets/fonts/inter/Inter_28pt-Regular.ttf) format('truetype');
  font-weight: 400;
}

@font-face {
  font-family: 'Obviously';
  font-style: normal;
  font-display: swap;
  src: url(../../public/fonts/obviously/fonnts.com-Obviously_Bold.otf) format('opentype');
  font-weight: 700;
}

/* inter font end Here */
