'use client';
import React, { useState } from 'react';
import HelpSidebar from './HelpSidebar';
import HelpRIghtContent from './HelpRIghtContent';
import Payment from './HelpRIghtContent/Payment';
import BookIcon from '@/assets/images/svg-images/book.svg';
import Image from 'next/image';
import HelpCenterTab from './HelpRIghtContent/HelpCenterTab';

function HelpPage() {
  const [selected, setSelected] = useState('Payments');

  const contentMap = {
    Payments: <Payment />,
    'Help Center': <HelpCenterTab />,
    'Terms & Conditions': (
      <p className="text-white-700">Terms & Conditions go here.</p>
    ),
    'Responsible Gaming': (
      <p className="text-white-700">Responsible Gaming content.</p>
    ),
    'Privacy Policy': <p className="text-white-700">Privacy Policy content.</p>,
    'Cookie policy': <p className="text-white-700">Cookie policy details.</p>,
    Complaints: (
      <p className="text-white-700">Complaint form or details here.</p>
    ),
    FAQ: <p className="text-white-700">Frequently asked questions content.</p>,
    'About Us': <p className="text-white-700">About the company.</p>,
    Support: <p className="text-white-700">Support contact details here.</p>,
  };

  return (
    <div className="mx-auto max-w-[80rem] py-10 lg:px-[0.625rem]">
      <div className="inner-heading mb-6 flex gap-1">
        <Image
          src={BookIcon}
          alt="BookIcon"
          height={20}
          width={20}
          className=""
        />
        <h4 className="bg-TintGoldGradient bg-clip-text text-sm font-semibold uppercase text-transparent">
          Knowledge Base
        </h4>
      </div>
      <div className="gap-4 lg:flex">
        {/* Pass state + setter to sidebar */}
        <HelpSidebar active={selected} onSelect={setSelected} />

        {/* Pass selected to RightBar */}
        <HelpRIghtContent heading={selected}>
          {contentMap[selected] ?? (
            <p className="text-white-700">No content found.</p>
          )}
        </HelpRIghtContent>
      </div>
    </div>
  );
}

export default HelpPage;
