'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

function HelpRightContent({ heading, children }) {
  return (
    <div className="flex-1 rounded-lg bg-steelTeal-800 p-4 lg:max-w-[calc(100%-230px)] max-lmd:mt-4  max-sm:w-full">
      {/* Heading */}
      <AnimatePresence mode="wait">
        {heading && (
          <motion.div
            key={heading}
            initial={{ opacity: 0, y: 12 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 12 }}
            transition={{ duration: 0.12, ease: 'easeOut' }}
            className="mb-4 hidden border-b border-white-300 md:block"
          >
            <h4 className="pb-1 text-xl font-medium text-white-1000">
              {heading}
            </h4>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={heading + '-content'}
          initial={{ opacity: 0, y: 12 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 12 }}
          transition={{ duration: 0.15, ease: 'easeOut' }}
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

export default HelpRightContent;
