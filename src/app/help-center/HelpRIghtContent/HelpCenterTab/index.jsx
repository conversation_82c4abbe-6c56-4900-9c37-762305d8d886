import React from 'react';

function HelpCenterTab() {
  return (
    <div>
      <p className="mb-5 text-sm">
        We're here to make sure your experience is smooth, secure, and full of
        fun. Browse our most frequently asked questions, or get in touch with
        our support team for personalized help.
      </p>
      <p className="mb-5 text-sm">
        Whether you have questions about your account, deposits and withdrawals,
        bonuses, or how to play your favorite games, you’ll find all the
        information you need right here. Our team is available 24/7 to assist
        you with any issues or concerns, so don’t hesitate to reach out. From
        technical support to responsible gaming guidance, we're committed to
        providing quick, friendly, and reliable help whenever you need it.
      </p>
      <p className="mb-5 text-sm">
        If you're unsure how bonuses work, need help verifying your identity, or
        have questions about withdrawal timelines, our support articles and team
        are ready to guide you through every step. We also take responsible
        gaming seriously and offer tools to help you stay in control of your
        play. If you can't find what you're looking for, our customer support
        team is available 24/7 via live chat or email to provide fast and
        friendly assistance. Your satisfaction and safety are our top
        priorities, so let us help you get back to the fun—worry-free.
      </p>
    </div>
  );
}

export default HelpCenterTab;
