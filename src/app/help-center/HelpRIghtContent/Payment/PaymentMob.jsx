import ThemeTable from '@/app/account/ThemeTable';
import React from 'react';
import payment1 from '@/assets/webp/payment-1.webp';
import payment2 from '@/assets/webp/payment-2.webp';
import payment3 from '@/assets/webp/payment-3.webp';
import payment4 from '@/assets/webp/payment-4.webp';
import payment5 from '@/assets/webp/payment-5.webp';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
const columns = [
  {
    header: 'METHOD',
    accessor: 'method',
    render: (game) => (
      <div className="flex items-center gap-2">
        <div className="h-9 w-[2.625rem] overflow-hidden rounded-[.25rem]">
          <Image
            src={payment1}
            alt="Game"
            className="h-full w-full object-cover"
          />
        </div>
        <p className="text-sm font-medium text-white-1000">Visa</p>
      </div>
    ),
  },
  {
    header: 'TYPE',
    accessor: 'type',
  },
  {
    header: 'MULTIPLIER',
    accessor: 'multiplier',
  },

  {
    header: 'COMMISSION',
    accessor: 'commission',
  },
  {
    header: 'PROCESSING TIME',
    accessor: 'processingTime',
  },
  {
    header: 'MINIMUM',
    accessor: 'minimum',
  },
  {
    header: '   ',
    accessor: 'withdrawal',
    render: () => <PrimaryButton variant="secondary">Withdraw</PrimaryButton>,
  },
];

const data = [
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
];

function PaymentMob() {
  return (
    <>
      <div className="mb-3 flex flex-col gap-2.5 border-b border-white-370 pb-2.5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-9 w-[2.625rem] overflow-hidden rounded-[.25rem]">
              <Image
                src={payment1}
                alt="Game"
                className="h-full w-full object-cover"
              />
            </div>
            <p className="text-sm font-medium text-white-1000">Visa</p>
          </div>
          <PrimaryButton
            variant="secondary"
            className="!h-8 !w-auto rounded-[0.625rem] "
          >
            Deposit
          </PrimaryButton>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold capitalize text-steelTeal-200">
            Type
          </span>
          <p className="text-sm font-medium  text-white-1000">
            Credit/Debit Card
          </p>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold capitalize text-steelTeal-200">
            processing time
          </span>
          <p className="text-sm font-medium  text-white-1000">Instant</p>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold capitalize text-steelTeal-200">
            commission
          </span>
          <p className="text-sm font-medium  text-white-1000">Free</p>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold text-steelTeal-200">
            Minimum Deposit
          </span>
          <p className="text-sm font-medium  text-white-1000">20 EUR</p>
        </div>
      </div>
      <div className="mb-0 flex flex-col gap-2.5 border-b border-white-370 pb-2.5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="h-9 w-[2.625rem] overflow-hidden rounded-[.25rem]">
              <Image
                src={payment1}
                alt="Game"
                className="h-full w-full object-cover"
              />
            </div>
            <p className="text-sm font-medium text-white-1000">Visa</p>
          </div>
          <PrimaryButton
            variant="secondary"
            className="!h-8 !w-auto rounded-[0.625rem] "
          >
            Deposit
          </PrimaryButton>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold capitalize text-steelTeal-200">
            Type
          </span>
          <p className="text-sm font-medium  text-white-1000">
            Credit/Debit Card
          </p>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold capitalize text-steelTeal-200">
            processing time
          </span>
          <p className="text-sm font-medium  text-white-1000">Instant</p>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold capitalize text-steelTeal-200">
            commission
          </span>
          <p className="text-sm font-medium  text-white-1000">Free</p>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs font-semibold text-steelTeal-200">
            Minimum Deposit
          </span>
          <p className="text-sm font-medium  text-white-1000">20 EUR</p>
        </div>
      </div>
    </>
  );
}

export default PaymentMob;
