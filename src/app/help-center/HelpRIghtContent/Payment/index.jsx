import ThemeTable from '@/app/account/ThemeTable';
import React from 'react';
import payment1 from '@/assets/webp/payment-1.webp';
import payment2 from '@/assets/webp/payment-2.webp';
import payment3 from '@/assets/webp/payment-3.webp';
import payment4 from '@/assets/webp/payment-4.webp';
import payment5 from '@/assets/webp/payment-5.webp';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import PaymentMob from './PaymentMob';
const columns = [
  {
    header: 'METHOD',
    accessor: 'method',
    render: (game) => (
      <div className="flex items-center gap-2">
        <div className="h-9 w-[2.625rem] overflow-hidden rounded-[.25rem]">
          <Image
            src={payment1}
            alt="Game"
            className="h-full w-full object-cover"
          />
        </div>
        <p className="text-sm font-medium text-white-1000">Visa</p>
      </div>
    ),
  },
  {
    header: 'TYPE',
    accessor: 'type',
  },
  {
    header: 'MULTIPLIER',
    accessor: 'multiplier',
  },

  {
    header: 'COMMISSION',
    accessor: 'commission',
  },
  {
    header: 'PROCESSING TIME',
    accessor: 'processingTime',
  },
  {
    header: 'MINIMUM',
    accessor: 'minimum',
  },
  {
    header: '   ',
    accessor: 'withdrawal',
    render: () => <PrimaryButton variant="secondary">Withdraw</PrimaryButton>,
  },
];

const data = [
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
  {
    method: '',
    type: '9,027.40',
    multiplier: 'X12.36',
    commission: '9,027.40',
    processingTime: 'Instant',
    minimum: '20 EUR',
    withdrawal: '',
  },
];

function Payment() {
  return (
    <div>
      <div className="my-2.5 flex items-center gap-10 text-sm font-bold md:px-4 max-sm:mb-4">
        <p className="cursor-pointer bg-tabTextGradient bg-clip-text text-transparent">
          Deposit
        </p>
        <p className="cursor-pointer text-steelTeal-200">Withdrawal</p>
      </div>
      <div className="hidden md:block">
        <ThemeTable columns={columns} data={data} tableBg="bg-inputBgColor" />
      </div>
      <div className="block md:hidden">
        <PaymentMob />
      </div>
    </div>
  );
}

export default Payment;
