'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

function HelpSidebar({ active, onSelect }) {
  const [isOpen, setIsOpen] = useState(false);

  const menuItems = [
    'Payments',
    'Help Center',
    'Terms & Conditions',
    'Responsible Gaming',
    'Privacy Policy',
    'Cookie policy',
    'Complaints',
    'FAQ',
    'About Us',
    'Support',
  ];

  return (
    <>
      {/* Desktop Sidebar */}
      <nav className="hidden min-w-[14.375rem] flex-col gap-1 rounded-lg bg-steelTeal-800 p-4 lg:flex">
        {menuItems.map((item) => (
          <div
            key={item}
            className={`rounded-[.25rem] pl-[1px] ${
              active === item
                ? 'bg-textGradient'
                : 'bg-transparent pl-0 hover:bg-textGradient'
            }`}
          >
            <button
              onClick={() => onSelect(item)}
              className={`w-full rounded-[.25rem] px-3 py-2 text-left text-sm font-semibold ${
                active === item
                  ? 'bg-slateGray-600 font-medium text-white-1000'
                  : 'text-white-400 hover:bg-slateGray-600 hover:text-white-1000'
              }`}
            >
              {item}
            </button>
          </div>
        ))}
      </nav>

      {/* Mobile Dropdown */}
      <div className="w-full lg:hidden">
        <div className="rounded-[.25rem] bg-textGradient pl-[1px]">
          <button
            onClick={() => setIsOpen((prev) => !prev)}
            className="flex w-full items-center justify-between rounded-[.25rem] bg-slateGray-600 px-4 py-2.5 text-left text-sm font-semibold text-white-1000"
          >
            {active || 'Select'}
            {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </button>
        </div>

        {isOpen && (
          <div className="mt-2 rounded-lg bg-steelTeal-800 shadow-lg">
            {menuItems.map((item) => (
              <button
                key={item}
                onClick={() => {
                  onSelect(item);
                  setIsOpen(false);
                }}
                className={`block w-full px-4 py-2 text-left text-sm ${
                  active === item
                    ? 'bg-slateGray-600 font-medium text-white-1000'
                    : 'text-white-400 hover:bg-slateGray-600 hover:text-white-1000'
                }`}
              >
                {item}
              </button>
            ))}
          </div>
        )}
      </div>
    </>
  );
}

export default HelpSidebar;
