'use client';
import React from 'react';
import SportsIcon from '@/assets/images/svg-images/sports-icon.svg';
import LiveIcon from '@/assets/images/svg-images/live-icon.svg';
import SoccerIcon from '@/assets/images/svg-images/soccer-icon.svg';
import HorseRacing from '@/assets/images/svg-images/horse.svg';
import TennisIcon from '@/assets/images/svg-images/tennis-icon.svg';
import FifaIcon from '@/assets/images/svg-images/fifa-icon.svg';
import BasketballIcon from '@/assets/images/svg-images/basketball-icon.svg';
import IceHockey from '@/assets/images/svg-images/ice-hockey-icon.svg';
import VolleyBallIcon from '@/assets/images/svg-images/volleyball-icon.svg';
import RugbyIcon from '@/assets/images/svg-images/rugby-icon.svg';
import Image from 'next/image';

const SportsBook = () => {
    const sports = [
        { label: 'LIVE', icon: LiveIcon },
        { label: 'SOCCER', icon: SoccerIcon },
        { label: 'HORSE RACING', icon: HorseRacing },
        { label: 'TENNIS', icon: TennisIcon },
        { label: 'FIFA', icon: FifaIcon },
        { label: 'BASKETBALL', icon: BasketballIcon },
        { label: 'ICE HOCKEY', icon: IceHockey },
        { label: 'VOLLEYBALL', icon: VolleyBallIcon },
        { label: 'RUGBY', icon: RugbyIcon },
    ];

    return (
        <div className="p-4">
            <div className="flex items-center gap-2 mb-4">
                <Image src={SportsIcon} alt="Sports Icon" width={20} height={20} />
                <p className="bg-cardBorderGradient bg-clip-text text-sm font-semibold uppercase text-transparent">
                    Sportsbook
                </p>
            </div>

            <div className="flex flex-wrap gap-3">
                {sports.map((sport, idx) => (
                    <button
                        key={idx}
                        className="flex flex-col items-center justify-center gap-1 rounded-2xl border border-gameTabBorder bg-BadgeGradientBg text-white transition hover:bg-[#3c361f] flex-none min-w-28 md:min-w-32 py-4"
                    >
                        <Image src={sport.icon} alt={sport.label} width={40} height={40} />
                        <span className="text-xs md:text-sm text-center">{sport.label}</span>
                    </button>
                ))}
            </div>
        </div>
    );
};

export default SportsBook;
