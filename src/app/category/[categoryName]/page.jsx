'use client';
import Category from '@/components/Category';
import Gamefilter from '@/components/HomePage/GameFilter';
import LobbyGames from '@/components/HomePage/LobbyGames';
import useRecommendations from '@/hooks/useRecommendations';
import { useCallback, useEffect, useState } from 'react';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import useAuthStore from '@/store/useAuthStore';
import { decodeCategory } from '@/utils/helper';
import GameSlider from '@/components/HomePage/GameSlider/Gameslider';
import Groups from '@/components/HomePage/Groups';
import ProviderSlider from '@/components/HomePage/ProviderSlider';
import GameTabs from '@/components/HomePage/GameTabs';
import LiveTable from '@/components/HomePage/LiveTable';
import Favorites from '@/components/Favorites';
import ContinuePlaying from '@/components/ContinuePlaying';

function GamePage({ params }) {
  const { isAuthenticated } = useAuthStore();
  const [categoryParams, setCategoryParams] = useState(params);
  const [icon, setIcon] = useState(<ForYouIcon size={16} activeMenu />);

  const { data, gamesLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useRecommendations({
      limit: 50,
      enabled:
        categoryParams?.categoryName === 'For You' ||
        categoryParams?.categoryName === 'Lobby',
    });

  useEffect(() => {
    if (isAuthenticated) {
      setIcon(<ForYouIcon size={16} activeMenu />);
    }
  }, [isAuthenticated]);
  useEffect(() => {
    const storedCategory = localStorage.getItem('selectedCategory');
    if (storedCategory) {
      setCategoryParams({ categoryName: storedCategory });
    } else {
      setCategoryParams(params);
    }
  }, [params]);

  const handleTabChange = useCallback((newTab) => {
    localStorage.setItem('selectedCategory', newTab);
    setCategoryParams({ categoryName: newTab });
  }, []);

  return (
    <>
      <Gamefilter
        defaultTab={decodeCategory(categoryParams?.categoryName)}
        setIcon={setIcon}
        page="category"
        onTabChange={handleTabChange}
      />
      {categoryParams?.categoryName === 'Lobby' && (
        <>
          <GameSlider />
          <Groups />
        </>
      )}

      {isAuthenticated && categoryParams?.categoryName === 'Lobby' && (
        <ProviderSlider />
      )}

      {categoryParams?.categoryName === 'Lobby' ? (
        <>
          {isAuthenticated && (
            <>
              <Category
                selectedTab="For You"
                externalGames={data}
                externalLoading={gamesLoading}
                externalFetchNextPage={fetchNextPage}
                externalHasNextPage={hasNextPage}
                externalIsFetchingNextPage={isFetchingNextPage}
                externalIcon={icon}
                isCarousel
              />
            </>
          )}
          <LobbyGames />
        </>
      ) : categoryParams?.categoryName === 'For You' ? (
        <Category
          selectedTab="For You"
          externalGames={data}
          externalLoading={gamesLoading}
          externalFetchNextPage={fetchNextPage}
          externalHasNextPage={hasNextPage}
          externalIsFetchingNextPage={isFetchingNextPage}
          externalIcon={icon}
        />
      ) : categoryParams?.categoryName === 'Favorites' ? (
        <>
          <Favorites />
        </>
      ) : categoryParams?.categoryName === 'Continue Playing' ? (
        <>
          <ContinuePlaying />
        </>
      ) : (
        <Category params={categoryParams} externalIcon={icon} />
      )}
      {categoryParams?.categoryName === 'Lobby' && (
        <>
          <GameTabs />
          <LiveTable />
        </>
      )}
    </>
  );
}

export default GamePage;
