'use client';

import React from 'react';
import Image from 'next/image';

const PromotionCard = ({ image, category, title, subtitle }) => {
  return (
    <div className="h-[13.8125rem] overflow-hidden rounded-xl bg-textGradient p-[1px]">
      <div className="relative h-full w-full overflow-hidden rounded-xl">
        <Image
          src={image}
          alt={category || 'Promotion'}
          className="h-full w-full object-cover"
        />

        {/* Category Badge */}
        {category && (
          <div className="absolute left-5 top-0 rounded-bl-lg rounded-br-lg bg-textGradient px-6 py-1 text-xs font-bold uppercase text-steelTeal-800">
            {category}
          </div>
        )}

        {/* Title + Subtitle */}
        <div className="absolute bottom-5 left-5">
          {title && (
            <h3 className="bg-textGradient bg-clip-text text-2xl font-semibold uppercase text-transparent">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-2xl uppercase text-white-1000">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PromotionCard;
