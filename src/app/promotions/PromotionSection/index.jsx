import React from 'react';
import StarGradient from '@/assets/images/svg-images/star-gradient.svg';
import Promotion1 from '@/assets/webp/promotion-1.webp';
import Promotion2 from '@/assets/webp/promotion-2.webp';
import Promotion3 from '@/assets/webp/promotion-3.webp';
import Image from 'next/image';

const promotions = [
  {
    image: Promotion1,
    category: 'casino',
    title: '50% Bonus on',
    subtitle: 'deposits over $1,000',
  },
  {
    image: Promotion2,
    category: 'sports',
    title: 'Free Bet',
    subtitle: 'on your first wager',
  },
  {
    image: Promotion3,
    category: 'poker',
    title: 'Win Big',
    subtitle: 'with weekly tournaments',
  },
];

const PromotionCard = ({ image, category, title, subtitle }) => {
  return (
    <div className="h-[13.8125rem] overflow-hidden rounded-xl bg-textGradient p-[1px]">
      <div className="relative h-full w-full overflow-hidden rounded-xl">
        <Image
          src={image}
          alt={category}
          className="h-full w-full object-cover"
        />

        {/* Category Badge */}
        <div className="absolute left-5 top-0 rounded-bl-lg rounded-br-lg bg-textGradient px-6 py-1 text-xs font-bold uppercase text-steelTeal-800">
          {category}
        </div>

        {/* Text Content */}
        <div className="absolute bottom-5 left-5">
          <h3 className="bg-textGradient bg-clip-text text-2xl font-semibold uppercase text-transparent">
            {title}
          </h3>
          <p className="text-2xl uppercase text-white-1000">{subtitle}</p>
        </div>
      </div>
    </div>
  );
};
const PromotionSection = () => {
  return (
    <div className="py-6">
      {/* Section Heading */}
      <div className="inner-heading mb-6 flex gap-1">
        <Image src={StarGradient} alt="Star" height={20} width={20} />
        <h4 className="bg-TintGoldGradient bg-clip-text text-sm font-semibold uppercase text-transparent">
          Promotions
        </h4>
      </div>

      {/* Filters */}
      <div className="mb-6 flex items-center gap-10 text-sm font-bold md:px-4 max-sm:mb-4">
        <p className="cursor-pointer bg-tabTextGradient bg-clip-text text-transparent">
          All
        </p>
        <p className="cursor-pointer text-steelTeal-200">Casino</p>
        <p className="cursor-pointer text-steelTeal-200">Sports</p>
      </div>

      {/* Cards Grid */}
      <div className="gap grid gap-4 md:grid-cols-3 max-lg:grid-cols-2 max-sm:grid-cols-1">
        {promotions.map((promo, index) => (
          <PromotionCard
            key={index}
            image={promo.image}
            category={promo.category}
            title={promo.title}
            subtitle={promo.subtitle}
          />
        ))}
      </div>
    </div>
  );
};

export default PromotionSection;
