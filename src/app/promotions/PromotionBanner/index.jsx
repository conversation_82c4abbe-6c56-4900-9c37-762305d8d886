import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import React from 'react';

function PromotionBanner() {
  return (
    <div className="overflow-hidden rounded-3xl bg-gradientIcon p-[1px]">
      <div className="rounded-3xl bg-promotionMobBanner bg-cover bg-center px-3 py-8 lg:bg-promotionBanner max-sm:min-h-[30rem] max-sm:p-4">
        <div className="max-w-[32.5rem] text-center max-sm:flex max-sm:h-full max-sm:min-h-[30rem] max-sm:flex-col  max-sm:justify-start max-sm:text-center">
          <h2 className="mb-3 text-[6.25rem] font-black uppercase leading-[6.25rem] text-golden-600  max-md:text-[4.75rem] max-sm:mb-0">
            ENjoy
          </h2>
          <h3 className="text-[2.5rem] font-bold uppercase text-white-1000 max-md:text-3xl">
            Promotions
          </h3>
          <p className="text-4xl text-white-1000 max-md:text-3xl">
            View Weekly For Perks!
          </p>
        </div>
      </div>
    </div>
  );
}

export default PromotionBanner;
