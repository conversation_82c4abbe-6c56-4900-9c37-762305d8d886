import React from 'react';
import VipBox from '@/assets/webp/vip-benefit-9.webp';
import Image from 'next/image';
import ThemeTable from '@/app/account/ThemeTable';
import GradientCheckCircle from '@/assets/icons/GradientCheckCircle';

const columns = [
  { header: 'VIP RANK', accessor: 'vip' },
  { header: 'Sapphire', accessor: 'sapphire' },
  { header: 'Bronze', accessor: 'bronze' },
  { header: 'Silver', accessor: 'silver' },
  { header: 'Gold', accessor: 'gold' },
  { header: 'Platinum', accessor: 'platinum' },
  { header: 'Diamond', accessor: 'diamond' },
  { header: 'Diamond Elite', accessor: 'elite' },
  { header: 'Noir', accessor: 'noir' },
];

const data = [
  {
    vip: 'Daily Bonus Rakeback',
    sapphire: <GradientCheckCircle className="h-6 w-6" />,
    bronze: <GradientCheckCircle className="h-6 w-6" />,
    silver: <GradientCheckCircle className="h-6 w-6" />,
    gold: <GradientCheckCircle className="h-6 w-6" />,
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
  {
    vip: 'Weekly Raffle Access',
    sapphire: <GradientCheckCircle className="h-6 w-6" />,
    bronze: <GradientCheckCircle className="h-6 w-6" />,
    silver: <GradientCheckCircle className="h-6 w-6" />,
    gold: <GradientCheckCircle className="h-6 w-6" />,
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
  {
    vip: 'Monthly Bonuses',
    sapphire: <GradientCheckCircle className="h-6 w-6" />,
    bronze: <GradientCheckCircle className="h-6 w-6" />,
    silver: <GradientCheckCircle className="h-6 w-6" />,
    gold: <GradientCheckCircle className="h-6 w-6" />,
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
  {
    vip: 'Level Up Bonus',
    sapphire: '',
    bronze: <GradientCheckCircle className="h-6 w-6" />,
    silver: <GradientCheckCircle className="h-6 w-6" />,
    gold: <GradientCheckCircle className="h-6 w-6" />,
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
  {
    vip: 'Additional Monthly Reward',
    sapphire: '',
    bronze: '',
    silver: '',
    gold: <GradientCheckCircle className="h-6 w-6" />,
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
  {
    vip: 'FansBets Events Eligibility',
    sapphire: '',
    bronze: '',
    gold: '',
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
  {
    vip: 'FansBets Events Eligibility',
    sapphire: '',
    bronze: '',
    gold: '',
    platinum: <GradientCheckCircle className="h-6 w-6" />,
    diamond: <GradientCheckCircle className="h-6 w-6" />,
    elite: <GradientCheckCircle className="h-6 w-6" />,
    noir: <GradientCheckCircle className="h-6 w-6" />,
  },
];

function TheBenefitTable() {
  return (
    <div className="py-3 lg:py-5">
      <div className="inner-heading  mb-3 lg:mb-8">
        <h4 className="text-2xl font-black uppercase">The benefits</h4>
      </div>
      <ThemeTable columns={columns} data={data} />
    </div>
  );
}

export default TheBenefitTable;
