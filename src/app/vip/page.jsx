'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import useAuthStore from '@/store/useAuthStore';

const VipBanner = dynamic(() => import('./VipBanner'), { ssr: false });
const VipBenefits = dynamic(() => import('./VipBenefits'), { ssr: false });
const VipRewards = dynamic(() => import('./VipRewards'), { ssr: false });
const VipFaq = dynamic(() => import('./VipFaq'), { ssr: false });
const LearnMoreSection = dynamic(() => import('./LearnMoreSection'), { ssr: false });
const VipLoggedInBanner = dynamic(() => import('./VipLoggedInBanner'), { ssr: false });
const AvailableRewards = dynamic(() => import('./AvailableRewards'), { ssr: false });
const TheBenefitTable = dynamic(() => import('./TheBenefitTable'), { ssr: false });
const VipLevel = dynamic(() => import('./VipLevel'), { ssr: false });

function Vip() {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  return (
    <div className="p-[0.625rem]">
      {!isAuthenticated ? (
        <>
          <VipBanner />
          <VipBenefits />
          <VipRewards />
        </>
      ) : (
        <>
          <VipLoggedInBanner />
          <AvailableRewards />
          <TheBenefitTable />
          <VipLevel />
          <VipFaq />
        </>
      )}
      <VipFaq />
      <LearnMoreSection />
    </div>
  );
}

export default Vip;
