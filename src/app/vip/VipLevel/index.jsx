'use client';
import React, { useEffect, useState } from 'react';
import ThemeAccordion from '@/app/account/ThemeAccordion';
import DiamondIcon from '@/assets/webp/sapphire-diamond.webp';
import BronzeIcon from '@/assets/webp/bronze.webp';
import PlatinumIcon from '@/assets/webp/platinum.webp';
import GoldIcon from '@/assets/webp/gold.webp';
import SilverIcon from '@/assets/webp/silver.webp';
import NoirIcon from '@/assets/webp/noir.webp';
import EliteIcon from '@/assets/webp/elite.webp';
import Image from 'next/image';
import GradientCheckCircle from '@/assets/icons/GradientCheckCircle';
import ThemeAccordionTable from '@/app/account/ThemeAccordionTable';
import BetsIcon from '@/assets/icons/BetsIcon';
import GradientProgressBar from '@/app/account/ProgressBar';
import Level1 from '@/assets/webp/level-1.webp';
import Level2 from '@/assets/webp/level-2.webp';
import Level3 from '@/assets/webp/level-3.webp';
import Level4 from '@/assets/webp/level-4.webp';
import Level5 from '@/assets/webp/level-5.webp';
import ProgressCheck from '@/assets/webp/progress-check.webp';
import ProgressLock from '@/assets/webp/progress-lock.webp';
import LockIcon from '@/assets/icons/LockIcon';
import { useVipTier } from '@/reactQuery/gamesQuery';
import MainLoader from '@/components/Common/Loader/MainLoader';

const columns = [
  { header: 'Level', accessor: 'level' },
  { header: 'Points Required', accessor: 'points' },
  { header: 'Completed', accessor: 'completed' },
];

function VipLevel() {
  const [progress, setProgress] = useState(90);

  const { data: vipTierData, isLoading } = useVipTier({ enabled: true });

  console.log('Updated vipTierData', vipTierData);

  const tierImages = {
    tier1: Level1,
    tier2: Level2,
    tier3: Level3,
    tier4: Level4,
    tier5: Level5,
  };

  const formatNumber = (num) => {
    if (num >= 1_000_000_000) return num / 1_000_000_000 + 'B';
    if (num >= 1_000_000) return num / 1_000_000 + 'M';
    if (num >= 1_000) return num / 1_000 + 'K';
    return num?.toString();
  };

  return (
    <div className="relative py-3 lg:py-5">
      <div className="inner-heading mb-5">
        <h4 className="text-2xl font-black uppercase">VIP LEVELS</h4>
      </div>
      {isLoading ? (
        <div className="flex h-96 items-center justify-center">
          <MainLoader className="w-32" />
        </div>
      ) : (
        vipTierData?.vipTierLevel?.map((item, index) => (
          <ThemeAccordion
            accordionBg="bg-transparent"
            key={index}
            vipTierLevel={vipTierData.active.levelId}
            id={item.id}
            canOpen={Number(item.id) <= Number(vipTierData.active.levelId)}
            className="rounded-2xl border border-slateGray-700"
            title={
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center gap-3">
                  <Image
                    src={item?.icon || DiamondIcon}
                    alt={item?.name || 'VIP Diamond'}
                    width={40}
                    height={40}
                  />
                  <h4 className="text-sm font-bold text-white-1000 max-lg:text-[.9375rem] max-lg:text-sm max-sm:text-left">
                    {item?.name}
                  </h4>
                </div>
                {vipTierData.active.levelId > item.id ? (
                  <GradientCheckCircle className="mr-3" />
                ) : vipTierData.active.levelId === item.id ? (
                  <BetsIcon className="mr-3" />
                ) : (
                  <LockIcon className="mr-3 [&_*]:fill-scarlet-200" />
                )}
              </div>
            }
          >
            <div>
              <ThemeAccordionTable
                columns={columns}
                data={item?.vipTiers}
                vipTierData={vipTierData}
                renderDetails={(row) => {
                  const activeLevel = Number(vipTierData?.active?.levelId ?? 0);
                  const activeTierId = Number(vipTierData?.active?.tierId ?? 0);
                  const userWager = Number(vipTierData?.userWagerAmount ?? 0);
                  const itemId = Number(item?.id ?? 0);

                  return (
                    <div className="mb-5 lg:px-[3.75rem] max-lg:px-5 max-sm:p-0">
                      <div className="relative top-[1.6875rem] z-10 flex items-center xl:left-[1.25rem] max-sm:left-0">
                        {Object.entries(row?.moreDetails || {}).map(
                          ([tier, value]) => {
                            const rowId = Number(row?.id ?? 0);

                            const isCompleted =
                              itemId < activeLevel ||
                              (itemId === activeLevel &&
                                rowId < activeTierId) ||
                              (itemId === activeLevel &&
                                rowId === activeTierId &&
                                userWager >= value);

                            return (
                              <div
                                key={tier}
                                className="flex w-[20%] flex-col items-end"
                              >
                                <div className="flex flex-col items-end justify-center">
                                  <Image
                                    src={tierImages[tier]}
                                    alt={tier}
                                    width={60}
                                    height={60}
                                    className="relative left-1  mb-2 h-12 w-12 max-lg:h-10 max-lg:w-10 max-sm:left-0"
                                  />
                                  <h4
                                    className={`mb-4 w-full pr-2 text-right text-sm font-bold text-white-1000 lg:text-base ${
                                      isCompleted ? 'opacity-100' : 'opacity-30'
                                    }`}
                                  >
                                    {formatNumber(value)}
                                  </h4>
                                  <Image
                                    src={
                                      isCompleted ? ProgressCheck : ProgressLock
                                    }
                                    alt="VIP Diamond"
                                    width={30}
                                    height={30}
                                    className="mr-1 max-xl:mr-0"
                                  />
                                </div>
                              </div>
                            );
                          },
                        )}
                      </div>

                      <GradientProgressBar
                        value={(() => {
                          const rowId = Number(row?.id ?? 0);
                          const requirement = Number(
                            Object.values(row?.moreDetails || {})[4] ?? 0,
                          );

                          const completed =
                            itemId < activeLevel ||
                            (itemId === activeLevel && rowId < activeTierId) ||
                            (itemId === activeLevel &&
                              rowId === activeTierId &&
                              userWager >= requirement);

                          if (completed) return 100;

                          if (
                            itemId === activeLevel &&
                            rowId === activeTierId
                          ) {
                            return Math.min(
                              100,
                              Math.round((userWager / requirement) * 100),
                            );
                          }

                          return 0;
                        })()}
                        onChange={setProgress}
                        min={0}
                        max={100}
                        step={10}
                        showSteps
                      />
                    </div>
                  );
                }}
              />
            </div>
          </ThemeAccordion>
        ))
      )}
    </div>
  );
}

export default VipLevel;
