'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import DiamondIcon from '@/assets/webp/sapphire-diamond.webp';
import GradientProgressBar from '@/app/account/ProgressBar';

function VipLoggedInBanner() {
  const [progress, setProgress] = useState(5);
  return (
    <div className="mb-3 overflow-hidden rounded-3xl bg-gradientIcon p-[1px] lg:mb-10">
      <div className="rounded-3xl bg-vipMobBanner bg-cover bg-center px-10 py-8 lg:bg-vipBanner max-sm:min-h-[30rem] max-sm:p-4">
        <div className="max-w-[25rem] rounded-[1.25rem] bg-gameCardBorder p-5">
          <div className="mb-4 flex items-center gap-2">
            <Image
              src={DiamondIcon}
              alt="VIP Diamond"
              width={36}
              height={36}
              className="max-sm:h-10 max-sm:w-10"
            />
            <h4 className="text-xl font-bold">dukebafor</h4>
          </div>
          <div className="w-full">
            {/* <h4 className="mb-3 text-xl font-bold max-sm:hidden">dukebafor</h4> */}
            <div className="flex flex-1 flex-col">
              <div className="flex justify-between text-[.9375rem] font-semibold text-white-400">
                <span className="">Your VIP Progress</span>
                <span className="">354,549 Points</span>
              </div>
              <GradientProgressBar
                value={progress}
                onChange={setProgress}
                min={0}
                max={100}
                step={1}
                labels={['0', '5%', '25%', '50%', '75%', '100%']}
              />

              <div className="flex justify-between text-[.9375rem] font-semibold text-white-400">
                <span>
                  Platinum I <span className="block">250k</span>
                </span>
                <span>
                  Platinum II <span className="block">500k</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default VipLoggedInBanner;
