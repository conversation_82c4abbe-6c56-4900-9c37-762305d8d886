import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import React from 'react';
import QuestionIcon from '@/assets/webp/question-icon-2.webp';
import SupportIcon from '@/assets/webp/question-icon-1.webp';
import Image from 'next/image';
function LearnMoreSection() {
  return (
    <div className="relative mb-3 flex flex-col gap-10 overflow-hidden lg:mb-5  max-sm:mb-3 max-sm:gap-3">
      <div className="relative rounded-3xl bg-slateGray-700 p-10 max-sm:p-4">
        <h4 className="mb-3 bg-textGradient bg-clip-text text-2xl font-black uppercase text-transparent">
          learn more on our fans forum
        </h4>
        <p className="text-xs leading-5 text-white-1000 lg:text-[.9375rem]">
          Visit our Fans Forum to chat with many of our players about whats new
          with FansBets and all of our amazing Fans Products. We look forward to
          seeing you there and building the largest community of fans and
          players worldwide.
        </p>
        <Image
          src={QuestionIcon}
          alt="QuestionIcon"
          className="absolute right-5 top-0 max-w-[15rem]   max-sm:-right-4 max-sm:-top-9 max-sm:max-w-[9.25rem]"
        />
        <PrimaryButton
          variant="secondary"
          className="mt-11 !w-auto !text-[.9375rem]"
        >
          View forum
        </PrimaryButton>
      </div>
      <div className="relative overflow-hidden rounded-3xl bg-slateGray-700 p-10 max-sm:p-4">
        <h4 className="mb-3 bg-textGradient bg-clip-text text-2xl font-black  uppercase text-transparent">
          Live 24-hour HuMan support
        </h4>
        <p className="text-xs leading-5 text-white-1000 lg:text-[.9375rem]">
          Real support from real people. We're available through instant live
          chat and email to help you.
        </p>
        <Image
          src={SupportIcon}
          alt="SupportIcon"
          className="absolute right-5 top-0 max-w-[15rem]  max-sm:-right-4 max-sm:-top-9 max-sm:max-w-[9.25rem]"
        />
        <PrimaryButton
          variant="secondary"
          className="mt-11 !w-auto !text-[.9375rem] "
        >
          Chat with us
        </PrimaryButton>
      </div>
    </div>
  );
}

export default LearnMoreSection;
