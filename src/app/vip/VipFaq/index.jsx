import React from 'react';
import ThemeAccordion from '@/app/account/ThemeAccordion';
function VipFaq() {
  return (
    <div className="relative py-3 lg:py-5">
      <div className="inner-heading mb-5">
        <h4 className="text-2xl font-black uppercase">FAQ</h4>
      </div>
      <ThemeAccordion
        accordionBg="bg-slateGray-700"
        title={
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium  text-white-1000  max-lg:text-[.9375rem] max-lg:text-sm max-sm:text-left">
              Why should I become a VIP on FansBets?
            </h4>
          </div>
        }
      >
        <div>
          <p className="text-[.9375rem] text-white-1000 max-sm:text-xs">
            FansBets is the ultimate online gambling experience, where you can
            connect with friends in real-time and play exciting games from top
            providers such as Pragmatic Gaming, Nolimit City and Evolution
            Gaming’s Live Casino. Try your luck on all the most popular titles
            and become a VIP member with unmatched benefits as you level up on
            FansBets.
          </p>
        </div>
      </ThemeAccordion>
      <ThemeAccordion
        accordionBg="bg-slateGray-700"
        title={
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium text-white-1000  max-lg:text-[.9375rem] max-lg:text-sm max-sm:text-left">
              What is a Bonus Bar Reward and when is it received? How is it
              calculated?
            </h4>
          </div>
        }
      >
        <div>
          <p className="text-[.9375rem] text-white-1000 max-sm:text-xs">
            FansBets is the ultimate online gambling experience, where you can
            connect with friends in real-time and play exciting games from top
            providers such as Pragmatic Gaming, Nolimit City and Evolution
            Gaming’s Live Casino. Try your luck on all the most popular titles
            and become a VIP member with unmatched benefits as you level up on
            FansBets.
          </p>
        </div>
      </ThemeAccordion>
      <ThemeAccordion
        accordionBg="bg-slateGray-700"
        title={
          <div className="flex items-center gap-2">
            <h4 className="text-left text-sm font-medium   text-white-1000 max-lg:text-[.9375rem] max-lg:text-sm">
              Why should I become a VIP on FansBets?
            </h4>
          </div>
        }
      >
        <div>
          <p className="text-[.9375rem] text-white-1000 max-sm:text-xs">
            FansBets is the ultimate online gambling experience, where you can
            connect with friends in real-time and play exciting games from top
            providers such as Pragmatic Gaming, Nolimit City and Evolution
            Gaming’s Live Casino. Try your luck on all the most popular titles
            and become a VIP member with unmatched benefits as you level up on
            FansBets.
          </p>
        </div>
      </ThemeAccordion>
      <ThemeAccordion
        accordionBg="bg-slateGray-700"
        title={
          <div className="flex items-center gap-2">
            <h4 className="text-left text-sm font-medium  text-white-1000 max-lg:text-[.9375rem] max-lg:text-sm">
              What is a Bonus Bar Reward and when is it received? How is it
              calculated?
            </h4>
          </div>
        }
      >
        <div>
          <p className="text-[.9375rem] text-white-1000 max-sm:text-xs">
            FansBets is the ultimate online gambling experience, where you can
            connect with friends in real-time and play exciting games from top
            providers such as Pragmatic Gaming, Nolimit City and Evolution
            Gaming’s Live Casino. Try your luck on all the most popular titles
            and become a VIP member with unmatched benefits as you level up on
            FansBets.
          </p>
        </div>
      </ThemeAccordion>
    </div>
  );
}

export default VipFaq;
