import React from 'react';
import VipBox from '@/assets/webp/vip-benefit-9.webp';
import Image from 'next/image';

function AvailableRewards() {
  return (
    <div className="py-3 lg:py-5">
      <div className="inner-heading mb-3 lg:mb-8">
        <h4 className="text-2xl font-black uppercase">Available rewards</h4>
      </div>
      <div className="relative rounded-[1.25rem] border border-inputBgColor bg-slateGray-700 p-7 text-center  max-sm:py-3">
        <div className="mx-auto max-w-[12.9375rem]">
          <Image
            src={VipBox}
            width={72}
            hight={72}
            alt="Reward"
            className="mx-auto mb-6"
          />
          <h4 className="text-[.9375rem] font-semibold text-white-1000">
            No rewards available
          </h4>
          <p className="text-sm text-white-400">
            You currently don’t have any VIP Rewards available to claim.
          </p>
        </div>
      </div>
    </div>
  );
}

export default AvailableRewards;
