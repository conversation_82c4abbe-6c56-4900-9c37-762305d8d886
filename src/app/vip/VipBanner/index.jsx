import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import React from 'react';

function VipBanner() {
  return (
    <div className="overflow-hidden rounded-3xl bg-gradientIcon p-[1px]">
      <div className="rounded-3xl bg-vipMobBanner bg-cover bg-center px-10 py-8 lg:bg-vipBanner max-sm:min-h-[30rem] max-sm:p-4">
        <div className="max-w-[32.5rem] max-sm:flex max-sm:h-full max-sm:min-h-[30rem] max-sm:flex-col max-sm:justify-center max-sm:text-center">
          <h3 className="mb-3 text-3xl font-bold uppercase lg:text-4xl max-sm:font-bold">
            The Ultimate
            <span className="bg-vipTextGradient bg-clip-text pl-1 text-transparent max-sm:block">
              VIP Club
            </span>
          </h3>
          <p className="mb-10 text-sm font-semibold leading-5 text-steelTeal-200 lg:text-[.9375rem]">
            There's simply no VIP program that matches ours. Earn a Ferrari,
            receive top-tier deposit matches, embark on memorable trips, and 
            gain entry into the WSOP. No other VIP Program even comes close.
          </p>
          <PrimaryButton
            variant="secondary"
            className="mt-auto !w-auto text-[.9375rem]"
          >
            View rank
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
}

export default VipBanner;
