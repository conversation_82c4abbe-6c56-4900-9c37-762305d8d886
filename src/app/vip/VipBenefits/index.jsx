import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import React from 'react';
import Vip1 from '@/assets/webp/vip-benefit-1.webp';
import Vip2 from '@/assets/webp/vip-benefit-2.webp';
import Vip3 from '@/assets/webp/vip-benefit-3.webp';
import Vip4 from '@/assets/webp/vip-benefit-4.webp';
import Vip5 from '@/assets/webp/vip-benefit-5.webp';
import Vip6 from '@/assets/webp/vip-benefit-6.webp';
import Vip7 from '@/assets/webp/vip-benefit-7.webp';
import Vip8 from '@/assets/webp/vip-benefit-8.webp';
import Vip9 from '@/assets/webp/vip-benefit-9.webp';
import Image from 'next/image';
const vip = [
  {
    text: 'Dedicated VIP Manager',
    img: Vip1,
  },
  {
    text: 'Earn Increased Bonus RAKEBACK Percentages',
    img: Vip2,
  },
  {
    text: 'MONTHLY CUSTOM BONUSES for all players',
    img: Vip3,
  },
  {
    text: 'Game, Concert, and Event Tickets',
    img: Vip4,
  },
  {
    text: 'Luxury Gifts And Experiences',
    img: Vip5,
  },
  {
    text: 'Increased betting limits',
    img: Vip6,
  },
  {
    text: 'Custom Monthly NOIR Contests',
    img: Vip7,
  },
  {
    text: 'Bespoke Perks – You tell us',
    img: Vip8,
  },
  {
    text: 'ACCESS TO VIP-ONLY GROUPS ',
    img: Vip9,
  },
];

function VipBenefits() {
  return (
    <div className="py-10 max-sm:py-3">
      <div className="inner-heading mb-12">
        <h4 className="text-center text-3xl font-bold lg:text-4xl">
          THE{' '}
          <span className="bg-vipTextGradient bg-clip-text text-transparent">
            VIP
          </span>{' '}
          BENEFITS!
        </h4>
      </div>
      <div className="grid grid-cols-3 gap-[1.1875rem] max-md:grid-cols-2 max-sm:grid-cols-1">
        {vip.map((item, index) => (
          <div
            key={index}
            className="h-[12.5rem] overflow-hidden rounded-[1.25rem] bg-slateGray-700 px-4 py-6 text-center"
          >
            <p className="font-black uppercase  text-white-1000 lg:text-xl max-lg:text-base">
              {' '}
              {item.text}
            </p>
            <Image
              src={item.img}
              alt={item.text}
              width={300}
              height={300}
              className="mx-auto mb-4 mt-[-1rem] w-[14.5rem]"
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export default VipBenefits;
