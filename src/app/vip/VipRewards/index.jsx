import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import React from 'react';
import Reward1 from '@/assets/webp/reward-1.webp';
import Reward2 from '@/assets/webp/reward-2.webp';
import Reward3 from '@/assets/webp/reward-3.webp';
import Image from 'next/image';
function VipRewards() {
  return (
    <div className="bg-rewardBg relative bg-cover  bg-center  bg-no-repeat py-10 max-sm:bg-[length:100%] max-sm:bg-[100%] max-sm:py-3">
      <div className="inner-heading mb-12 max-sm:mb-0">
        <h4 className="mb-4 text-center text-2xl font-bold lg:text-4xl max-lg:text-3xl max-sm:mb-0 max-sm:font-black">
          <span className="bg-vipTextGradient bg-clip-text pr-1 text-transparent">
            VIP
          </span>
          REWARDS AND PERKS
        </h4>
        <p className="hidden text-center text-[.9375rem] font-semibold text-steelTeal-200 lg:block">
          Get more out of your premium status!
        </p>
      </div>
      <div className="flex items-start justify-start max-sm:flex-col">
        <Image
          src={Reward1}
          alt="first"
          className="mx-auto max-w-[16.625rem] max-lg:max-w-[10rem]"
        />
        <Image
          src={Reward2}
          alt="Second"
          className="mx-auto mt-12 max-w-[24.5rem] max-lg:max-w-[16rem]"
        />
        <Image
          src={Reward3}
          alt="Third"
          className="mx-auto max-w-[16.625rem] max-lg:max-w-[10rem]"
        />
      </div>
    </div>
  );
}

export default VipRewards;
