import React, { useState } from 'react';
import DepositIFill from '@/assets/icons/DepositIFill';
import FirstBet from '@/assets/icons/FirstBet';
import GradientCheckCircle from '@/assets/icons/GradientCheckCircle';
import InfoCircleIcon from '@/assets/icons/Info-Circle';
import InfoIcon from '@/assets/icons/InfoIcon';
import VerifyEmail from '@/assets/icons/VerifyEmail';
import WhiteCheckCircle from '@/assets/icons/WhiteCheckCircle.';
import ThemeAccordion from '../ThemeAccordion';
import ThemeSelect from '@/components/Common/InputField/ThemeSelect';
import FileUploadBox from '@/components/FileUploadBox';
import UploadIcon from '@/assets/webp/upload-line.webp';

const steps = [
  {
    text: 'Verify your email',
    status: 'Completed',
    icon: <VerifyEmail className="h-6 w-6" />,
  },
  {
    text: 'Make your first deposit',
    status: 'Pending',
    icon: <DepositIFill className="h-6 w-6" />,
  },
  {
    text: 'Place your first bet',
    status: 'Pending',
    icon: <FirstBet className="h-6 w-6" />,
  },
];

const KYCLevels = () => {
  const options = [
    { value: 'Public', label: 'Public' },
    { value: 'Private', label: 'Private' },
  ];
  const [bannerPreview, setBannerPreview] = useState(null);
  const [bannerError, setBannerError] = useState('');

  const handleBannerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (!['image/png', 'image/jpeg', 'image/webp'].includes(file.type)) {
        setBannerError('Invalid file type. Please upload PNG, JPG, or WEBP.');
        return;
      }
      setBannerError('');
      setBannerPreview(URL.createObjectURL(file));
    }
  };
  const [selected, setSelected] = React.useState(null);
  return (
    <div className="mb-6  gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
      <div className="mb-6 flex  items-center justify-between">
        <h4 className="top-heading text-[1.0625rem] font-semibold">
          Verification
        </h4>
        <div className="flex items-center justify-center gap-2">
          <GradientCheckCircle className="h-6 w-6" />
          <WhiteCheckCircle className="h-6 w-6" />
          <WhiteCheckCircle className="h-6 w-6" />
          <span className="font-semibold text-steelTeal-200">Level 1</span>
        </div>
      </div>
      <div>
        <ThemeAccordion title="Level 1">
          <div className="mb-4 max-w-[29.5rem]">
            <ThemeSelect
              options={options}
              value={selected}
              onChange={setSelected}
              placeholder="Select Label"
              className="placeholder:!font-semibold"
              label="Identity Document"
              color="text-white"
            />
          </div>
          <div className="upload-section-wrap">
            <div className="grid grid-cols-3  gap-4 max-sm:grid-cols-1">
              <FileUploadBox
                label="Front of document"
                preview={bannerPreview}
                error={bannerError}
                required
                onChange={handleBannerImageChange}
                placeholderImg={UploadIcon}
                placeholderText="Upload"
                borderColor="border-white"
              />
              <FileUploadBox
                label="Back of document"
                preview={bannerPreview}
                error={bannerError}
                required
                onChange={handleBannerImageChange}
                placeholderImg={UploadIcon}
                placeholderText="Upload"
                borderColor="border-white"
              />
              <FileUploadBox
                label="live selfie"
                preview={bannerPreview}
                error={bannerError}
                required
                onChange={handleBannerImageChange}
                placeholderImg={UploadIcon}
                placeholderText="Upload"
                borderColor="border-white"
              />
            </div>
            <div className="mt-5 flex items-center gap-2">
              <InfoIcon className="max-sm:h-10 max-sm:w-10" />
              <p className="text-sm text-steelTeal-200">
                If an account has no additional flags, withdrawals are available
                regardless of KYC status or level
              </p>
            </div>
          </div>
        </ThemeAccordion>
        <ThemeAccordion title="Level 2">
          <div className="mb-4 max-w-[29.5rem]">
            <ThemeSelect
              options={options}
              value={selected}
              onChange={setSelected}
              placeholder="Select Label"
              className="placeholder:!font-semibold"
              label="Proof of Address"
              color="text-white"
            />
          </div>
          <div className="upload-section-wrap">
            <div className="grid grid-cols-3  gap-4 max-sm:grid-cols-1">
              <FileUploadBox
                label="Front of document"
                preview={bannerPreview}
                error={bannerError}
                required
                onChange={handleBannerImageChange}
                placeholderImg={UploadIcon}
                placeholderText="Upload"
                borderColor="border-white"
              />
            </div>
            <div className="mt-5 flex items-center gap-2">
              <InfoIcon className="max-sm:h-10 max-sm:w-10" />
              <p className="text-sm text-steelTeal-200">
                If an account has no additional flags, withdrawals are available
                regardless of KYC status or level
              </p>
            </div>
          </div>
        </ThemeAccordion>
        <ThemeAccordion title="Level 3">
          <div className="mb-4 max-w-[29.5rem]">
            <ThemeSelect
              options={options}
              value={selected}
              onChange={setSelected}
              placeholder="Select Label"
              className="placeholder:!font-semibold"
              label="source of funds document"
              color="text-white"
            />
          </div>
          <div className="upload-section-wrap">
            <div className="grid grid-cols-3  gap-4 max-sm:grid-cols-1">
              <FileUploadBox
                label="Front of document"
                preview={bannerPreview}
                error={bannerError}
                required
                onChange={handleBannerImageChange}
                placeholderImg={UploadIcon}
                placeholderText="Upload"
                borderColor="border-white"
              />
            </div>
            <div className="mt-5 flex items-center gap-2">
              <InfoIcon className="max-sm:h-10 max-sm:w-10" />
              <p className="text-sm text-steelTeal-200">
                If an account has no additional flags, withdrawals are available
                regardless of KYC status or level
              </p>
            </div>
          </div>
        </ThemeAccordion>
        <ThemeAccordion title="Level 4">
          <div className="mb-4 max-w-[29.5rem]">
            <ThemeSelect
              options={options}
              value={selected}
              onChange={setSelected}
              placeholder="Select Label"
              className="placeholder:!font-semibold"
              label="source of funds document"
              color="text-white"
            />
          </div>
          <div className="upload-section-wrap">
            <div className="grid grid-cols-3  gap-4 max-sm:grid-cols-1">
              <FileUploadBox
                label="Front of document"
                preview={bannerPreview}
                error={bannerError}
                required
                onChange={handleBannerImageChange}
                placeholderImg={UploadIcon}
                placeholderText="Upload"
                borderColor="border-white"
              />
            </div>
            <div className="mt-5 flex items-center gap-2">
              <InfoIcon className="max-sm:h-10 max-sm:w-10" />
              <p className="text-sm text-steelTeal-200">
                If an account has no additional flags, withdrawals are available
                regardless of KYC status or level
              </p>
            </div>
          </div>
        </ThemeAccordion>
      </div>
      <div className="mt-5 flex items-center gap-2">
        <InfoIcon className="max-sm:h-10 max-sm:w-10" />
        <p className="text-sm text-steelTeal-200">
          If an account has no additional flags, withdrawals are available
          regardless of KYC status or level
        </p>
      </div>
    </div>
  );
};

export default KYCLevels;
