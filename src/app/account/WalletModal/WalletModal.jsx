'use client';

import { X } from 'lucide-react';
import IconButton from '@/components/Common/Button/IconButton';
import Tabs from '@/components/Common/Tabs';
import useAuthTab from '@/store/useAuthTab';
import Deposit from './Deposit';
import Withdrawal from './Withdrawal';
import { motion, AnimatePresence } from 'framer-motion';
import useModalStore from '@/store/useModalStore';
import { useFetchCurrenciesQuery, useFetchGatewaysQuery } from '@/reactQuery/paymentQuery';
import { useState } from 'react';

function WalletModal() {
  const { selectedTab, setSelectedTab } = useAuthTab((state) => state);
  const { clearModals } = useModalStore();
  const { data: gateways } = useFetchGatewaysQuery();
  const { data: currencies,  } = useFetchCurrenciesQuery();
  const [iframeUrl, setIframeUrl] = useState(null);

  const tabs = [
    {
      label: 'Deposit',
      content: <Deposit setIframeUrl={setIframeUrl} />,
    },
    {
      label: 'Withdrawal',
      content: <Withdrawal />,
    },
    {
      label: 'Buy Crypto',
      content: <Withdrawal />,
    },
  ];
  const handleCloseModal = () => {
    clearModals();
  };
  return (
    <AnimatePresence>
      <div
        id="authentication-modal"
        tabIndex="-1"
        aria-hidden="true"
        className="fixed inset-0 z-[999] flex h-full w-full items-end justify-center md:items-center"
      >
        <motion.div
          key="auth-modal"
          drag="y"
          dragConstraints={{ top: 0 }}
          dragElastic={0.2}
          onDragEnd={(event, info) => {
            if (info.offset.y > 10) {
              handleCloseModal();
            }
          }}
          initial={{ y: '100%', opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: '100%', opacity: 0 }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
          className="relative max-h-full w-full max-w-[32.5rem] p-0 md:min-h-[31.688rem] md:p-4"
        >
          <div className="relative overflow-y-auto rounded-tl-3xl rounded-tr-3xl bg-cetaceanBlue2-2000 md:rounded-[1.5rem]">
            <IconButton
              onClick={clearModals}
              className="!absolute right-4 top-4 max-md:hidden"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
            <div className="absolute left-1/2 top-[6px] flex h-1 w-20 -translate-x-1/2 transform rounded-full bg-primaryBorder  md:hidden"></div>

            {/* If iframeUrl exists, show iframe, else show tabs */}
            {iframeUrl ? (
              <div className="h-[500px] w-full md:h-[600px]">
                <iframe
                  src={iframeUrl}
                  title="Deposit"
                  className="h-full w-full border-0"
                />
              </div>
            ) : (
              <Tabs
                classes="[&>ul>li>button]:font-bold max-sm:px-2.5 max-sm:py-3 p-4 pb-0"
                tabs={tabs}
                setSelectedTab={setSelectedTab}
              />
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}

export default WalletModal;
