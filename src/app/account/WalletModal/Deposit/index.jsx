import React, { useState } from 'react';
import CurrencySelect from '@/components/Common/InputField/CurrencySelect';
import BitCoin from '@/assets/icons/BitCoin';
import InputField from '@/components/Common/InputField';
import CopyInputIcon from '@/assets/icons/CopyInputIcon';
import qrImg from '@/assets/webp/qr.webp';
import InfoCircleIcon from '@/assets/icons/Info-Circle';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import { usePaymentMutation } from '@/reactQuery/paymentQuery';
import { useGatewayStore } from '@/store/useGatewayStore';

const currencyOptions = [
  {
    value: 'btc',
    label: 'Bitcoin',
    icon: BitCoin,
    right: '$40,000',
  },
  {
    value: 'eth',
    label: 'Ethereum',
    icon: BitCoin,
    right: '$3,000',
  },
  {
    value: 'ltc',
    label: 'Litecoin',
    icon: BitCoin,
    right: '$180',
  },
];

function Deposit({ setIframeUrl }) {
  const [amount, setAmount] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const [address, setAddress] = useState('');
  const [formErrors, setFormErrors] = useState({});

  const { gateways,currencies  } = useGatewayStore();

  const { mutate: makeDeposit } = usePaymentMutation({
    onSuccess: (res) => {
      const url = res?.data?.praxisCashier?.redirect_url;
      if (url) {
        setIframeUrl(url); // send URL to WalletModal
      }
    },
  });

  const handleChange = (e) => {
    setAddress(e.target.value);
    setFormErrors((prev) => ({ ...prev, address: '' }));
  };

  const handleAmountChange = (e) => {
    let value = e.target.value;

    // remove everything except digits and dot
    value = value.replace(/[^0-9.]/g, '');

    // allow only a single dot
    const firstDotIdx = value.indexOf('.');
    if (firstDotIdx !== -1) {
      // cut off any extra dots after the first
      value =
        value.slice(0, firstDotIdx + 1) +
        value.slice(firstDotIdx + 1).replace(/\./g, '');
    }

    setAmount(value);
    setFormErrors((prev) => ({ ...prev, amount: '' }));
  };

  const handlePaymentMethodChange = (value) => {
    setSelectedPaymentMethod(value);
    setFormErrors((prev) => ({ ...prev, selectedPaymentMethod: '' }));
  };

  const handleCurrencyChange = (value) => {
    setSelectedCurrency(value);
    setFormErrors((prev) => ({ ...prev, selectedCurrency: '' }));
  };

  const validateForm = () => {
    const errors = {};

    if (!amount) {
      errors.amount = 'Amount is required';
    } else if (isNaN(amount) || Number(amount) <= 0) {
      errors.amount = 'Enter a valid amount';
    }

    if (!selectedPaymentMethod) {
      errors.selectedPaymentMethod = 'Payment method is required';
    }

    if (!selectedCurrency) {
      errors.selectedCurrency = 'Currency is required';
    }

    // if (!address) {
    //   errors.address = 'Deposit address is required';
    // }

    setFormErrors(errors);

    return Object.keys(errors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      console.log({
        amount,
        selectedPaymentMethod,
        selectedCurrency,
        address,
      });
      const selectedGateway = gateways?.find(
        (g) => g.payment_method === selectedPaymentMethod?.label,
      );
      const payload = {
        amount: Number(amount),
        currencyId: selectedCurrency?.value,
        paymentMethod: selectedPaymentMethod?.value,
        gateway: selectedGateway?.gateway,
      };

      makeDeposit(payload);
    }
  };

  return (
    <>
      <div className="p-4 max-sm:p-2.5">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <InputField
              type="text"
              name="amount"
              value={amount}
              placeholder="$500"
              onChange={handleAmountChange}
              error={formErrors.amount}
              label="Amount"
              labelRight="$0.70"
              color="text-white-1000"
              className="!rounded-[0.625rem]"
              inputBgColor="bg-inputBgColor"
            />
            <button
              type="button"
              onClick={() => setAmount('500')}
              className="absolute right-2 top-[1.875rem] rounded-lg bg-white-400 px-3 py-1.5 text-xs font-semibold"
            >
              Max
            </button>
          </div>

          <div className="flex-1">
            <CurrencySelect
              label="Payment Method"
              options={gateways}
              value={selectedPaymentMethod}
              onChange={handlePaymentMethodChange}
              placeholder="Select payment"
              error={formErrors.selectedPaymentMethod}
            />
          </div>
        </div>

        {/* Currency */}
        <div className="mb-5">
          <CurrencySelect
            label="Currency"
            options={currencies}
            value={selectedCurrency}
            onChange={handleCurrencyChange}
            placeholder="Select currency"
            error={formErrors.selectedCurrency}
          />
        </div>

        {/* Address */}
        {/* <div className="mb-5">
          <InputField
            type="text"
            name="address"
            value={address}
            placeholder="Address"
            onChange={handleChange}
            error={formErrors.address}
            endIcon={CopyInputIcon}
            label="Deposit address"
            color="text-white-1000"
            className="!rounded-[0.625rem]"
            inputBgColor="bg-inputBgColor"
          />
        </div> */}
        {/* Submit */}
        <PrimaryButton
          variant="secondary"
          className="my-4 w-full"
          onClick={handleSubmit}
        >
          Deposit
        </PrimaryButton>

        {/* Info Note */}
        <div className="mb-4 flex items-start gap-2 pr-9 text-sm text-scarlet-200">
          <InfoCircleIcon className="h-4 w-4 [&_*]:fill-scarlet-200" />
          <p className="text-left">
            Your deposit must be sent on the Bitcoin (BTC) network to be
            processed.
          </p>
        </div>

        {/* QR */}
        <div className="mx-auto mb-4 h-[8.75rem] w-[8.75rem]">
          <Image src={qrImg} alt="qrImg" className="w-full" />
        </div>

        {/* History */}
        <div className="mb-4">
          <h4 className="text-center text-[.9375rem] font-semibold text-white-1000">
            Deposit history
          </h4>
        </div>
      </div>

      {/* Footer */}
      <div className="wallet-footer border-t border-secondaryBtnBg p-6 text-center">
        <p className="mb-3 text-sm text-steelTeal-200">
          Improve your security with Two-Factor Authentication
        </p>
        <PrimaryButtonOutline variant="secondary" className="w-full">
          Enable 2FA
        </PrimaryButtonOutline>
      </div>
    </>
  );
}

export default Deposit;
