import React, { useState } from 'react';
import CurrencySelect from '@/components/Common/InputField/CurrencySelect';
import BitCoin from '@/assets/icons/BitCoin';
import InputField from '@/components/Common/InputField';
import CopyInputIcon from '@/assets/icons/CopyInputIcon';
import qrImg from '@/assets/webp/qr.webp';
import InfoCircleIcon from '@/assets/icons/Info-Circle';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import LockIcon from '@/assets/icons/LockIcon';

function Withdrawal() {
  const [email, setEmail] = useState('');
  const [formErrors, setFormErrors] = useState({ email: '' });
  const handleChange = (e) => {
    setEmail(e.target.value);
    setFormErrors({ email: '' });
  };
  const [selectedCurrency, setSelectedCurrency] = useState(null);
  const currencyOptions = [
    {
      value: 'btc',
      label: 'Bitcoin',
      icon: BitCoin,
      right: '$40,000',
    },
    {
      value: 'eth',
      label: 'Ethereum',
      icon: BitCoin,
      right: '$3,000',
    },
    {
      value: 'ltc',
      label: 'Litecoin',
      icon: BitCoin,
      right: '$180',
    },
  ];

  return (
    <>
      <div className="p-4 max-sm:p-2.5">
        <div className="mb-5">
          <CurrencySelect
            label="Choose Currency"
            options={currencyOptions}
            value={selectedCurrency}
            onChange={setSelectedCurrency}
            placeholder="Select currency"
          />
        </div>
        <div className="mb-5">
          <InputField
            type="text"
            name="email"
            value={email}
            placeholder="Email"
            onChange={handleChange}
            error={formErrors.email}
            endIcon={CopyInputIcon}
            label="Bitcoin (BTC) Address*"
            color="text-white-1000"
            className="!rounded-[0.625rem]"
            inputBgColor="bg-inputBgColor"
          />
        </div>
        <div className="relative mb-5">
          <InputField
            type="text"
            name="email"
            value={email}
            placeholder="$500"
            onChange={handleChange}
            error={formErrors.email}
            label="Amount"
            labelRight="$0.70"
            color="text-white-1000"
            className="!rounded-[0.625rem]"
            inputBgColor="bg-inputBgColor"
          />
          <button className="absolute right-2 top-[1.875rem] rounded-lg bg-white-400 px-3 py-1.5 text-xs font-semibold">
            Max
          </button>
        </div>
        <div className="flex flex-col gap-4">
          <div className="mb-4 flex items-start gap-2 pr-9 text-sm text-steelTeal-200">
            <InfoCircleIcon className="h-4 w-4 [&_*]:fill-steelTeal-200" />
            <p className="text-left">Withdrawal Fee: 0.00009000 BTC</p>
          </div>
          <div className="mb-4 flex items-start gap-2 pr-9 text-sm text-steelTeal-200">
            <LockIcon className="h-4 w-4 " />
            <p className="text-left">
              Your withdrawal will be processed on Bitcoin (BTC)
            </p>
          </div>
        </div>
        <PrimaryButton variant="secondary" className="mb-16 mt-4 w-full">
          Withdraw
        </PrimaryButton>
      </div>
      <div className="wallet-footer  border-t border-secondaryBtnBg p-6  text-center">
        <p className="mb-3 text-sm text-steelTeal-200">
          Improve your security with Two-Factor Authentication
        </p>
        <PrimaryButtonOutline variant="secondary" className="w-full">
          Enable 2FA
        </PrimaryButtonOutline>
      </div>
    </>
  );
}

export default Withdrawal;
