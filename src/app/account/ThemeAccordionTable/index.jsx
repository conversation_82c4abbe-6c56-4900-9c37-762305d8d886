'use client';
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GradientCheckCircle from '@/assets/icons/GradientCheckCircle';
import BetsIcon from '@/assets/icons/BetsIcon';
import LockIcon from '@/assets/icons/LockIcon';

const ThemeAccordionTable = ({ columns, data, renderDetails, vipTierData }) => {
  const [openRow, setOpenRow] = useState(null);

  const toggleRow = (row) => {
    if (Number(row.id) <= Number(vipTierData.active.tierId)) {
      setOpenRow(openRow === row.id ? null : row.id);
    }
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border-separate border-spacing-y-2 whitespace-nowrap">
        <thead>
          <tr className="text-left text-xs font-semibold text-white-400">
            {columns.map((col, idx) => (
              <th key={idx} className="px-4 pb-0">
                {col.header}
              </th>
            ))}
          </tr>
        </thead>

        <tbody
          className="[&>tr:nth-child(even)>td]:bg-transparent 
            [&>tr:nth-child(odd)>td]:bg-slateGray-700 
            [&>tr>td:first-child]:rounded-l-[0.625rem] 
            [&>tr>td:last-child]:rounded-r-[0.625rem]
          "
        >
          {data.map((row, i) => (
            <React.Fragment key={i}>
              {/* main row */}
              <tr
                onClick={() => toggleRow(row)}
                className="text-white cursor-pointer overflow-hidden text-[.9375rem] transition-colors"
              >
                {columns.map((col, j) => {
                  let content;

                  if (col.accessor === 'level') {
                    content = row.levelName;
                  } else if (col.accessor === 'points') {
                    content = row.wagerAmount;
                  } else if (col.accessor === 'completed') {
                    const tierId = Number(vipTierData?.active?.tierId ?? 0);
                    const itemId = Number(row?.id ?? 0);
                    if (tierId > itemId) {
                      content = (
                        <div className="flex items-center gap-3">
                          <GradientCheckCircle className="h-6 w-6" />
                          <span className="hidden xl:block">Completed</span>
                        </div>
                      );
                    } else if (tierId === itemId) {
                      content = (
                        <div className="flex items-center gap-3">
                          <BetsIcon className="h-6 w-6" />
                          <span className="hidden xl:block">Not completed</span>
                        </div>
                      );
                    } else {
                      content = (
                        <div className="flex items-center gap-3">
                          <LockIcon className="h-6 w-6 [&_*]:fill-scarlet-200" />
                          <span className="hidden xl:block">Not completed</span>
                        </div>
                      );
                    }
                  }

                  return (
                    <td key={j} className="px-4 py-2.5">
                      {content}
                    </td>
                  );
                })}
              </tr>

              {/* accordion row */}
              <AnimatePresence>
                {openRow === row.id && (
                  <motion.tr
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <td
                      colSpan={columns.length}
                      className="text-white  lg:px-4 lg:py-3"
                    >
                      {renderDetails
                        ? renderDetails(row)
                        : row.details || 'No extra details available.'}
                    </td>
                  </motion.tr>
                )}
              </AnimatePresence>
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ThemeAccordionTable;
