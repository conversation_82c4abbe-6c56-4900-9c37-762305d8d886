import React, { useState } from 'react';
import ThemeTable from '../ThemeTable';
import SuccessIcon from '@/assets/icons/SuccessIcon';
import FailIcon from '@/assets/icons/FailIcon';
import PendingIcon from '@/assets/icons/PendingIcon';
import NoDeposit from '@/assets/webp/gift.webp';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import WalletModal from '../WalletModal/WalletModal';
import useModalStore from '@/store/useModalStore';
import { useTransactionsQuery } from '@/reactQuery/paymentQuery';
import loading from '@/app/loading';
import { formatDateTime } from '@/utils/helper';
import Pagination from '@/components/Paginations';
const columns = [
  {
    header: 'Date And Time',
    accessor: 'updatedAt',
    render: (updatedAt) => formatDateTime(updatedAt),
  },
  {
    header: 'Amount',
    accessor: 'moreDetails',
    render: (moreDetails) => moreDetails?.amount || '-',
  },
  {
    header: 'Status',
    accessor: 'status',
    render: (status) => {
      let bgClass = '';
      let icon = null;
      let text = '';
      let textClass = '';

      if (status === 'completed') {
        bgClass = 'bg-green-100';
        textClass = 'text-green-200';
        icon = <SuccessIcon />;
        text = 'Confirm';
      } else if (status === 'failed') {
        bgClass = 'bg-scarlet-100';
        textClass = 'text-scarlet-200';
        icon = <FailIcon />;
        text = 'Rejected';
      } else {
        // treat everything else as "pending"
        bgClass = 'bg-slateGray-650';
        textClass = 'text-steelTeal-200';
        icon = <PendingIcon />;
        text = 'Pending';
      }

      return (
        <div
          className={`text-white inline-flex w-auto items-center justify-center gap-[0.2rem] rounded-lg px-1.5 py-1 text-xs font-semibold ${bgClass} ${textClass}`}
        >
          {icon}
          {text}
        </div>
      );
    },
  },
];

function Deposit() {
  const { openModal } = useModalStore((state) => state);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 14;

  const { data, isLoading, isError } = useTransactionsQuery({
    pageNo: currentPage,
    pageSize,
  });

  const totalPages = Math.ceil((data?.count || 0) / pageSize);

  return (
    <>
      {isLoading ? (
        loading()
      ) : data?.count !== 0 ? (
        <>
          <ThemeTable columns={columns} data={data?.rows} />
          <div className="mt-8 flex items-center justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        </>
      ) : (
        <div className="py-6 text-center">
          <Image
            src={NoDeposit}
            alt="No deposit"
            height={500}
            width={500}
            className="mx-auto mb-[1.5rem] w-[4.5rem]"
          />
          <h4 className="mb-2 text-[.9375rem] font-semibold">No deposits</h4>
          <p className="text-sm text-white-400">
            Make your first one and start winning
          </p>
          <PrimaryButton
            variant="secondary"
            className="!mx-auto !my-6 !w-auto"
            onClick={() => openModal(<WalletModal />)}
          >
            Make my first deposit
          </PrimaryButton>
        </div>
      )}
    </>
  );
}


export default Deposit;
