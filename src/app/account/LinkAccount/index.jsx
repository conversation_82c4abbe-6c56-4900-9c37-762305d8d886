import SteamIcon from '@/assets/icons/SteamIcon';
import TwitchIcon from '@/assets/icons/TwitchIcon';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import React from 'react';

const accounts = [
  { name: 'Steam', icon: <SteamIcon /> },
  { name: 'Twitch', icon: <TwitchIcon /> },
];

const LinkAccount = () => {
  return (
    <div className="mb-6 gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
      <h4 className="mb-5 text-[1.0625rem] font-semibold">Link account</h4>
      <div className="flex flex-col gap-2">
        {accounts.map((acc, i) => (
          <div
            key={i}
            className="flex items-center justify-between rounded-[0.625rem] bg-inputBgColor p-3"
          >
            <div className="flex items-center gap-3">
              {acc.icon}
              <span className="text-white text-sm">{acc.name}</span>
            </div>
            <PrimaryButton
              variant="secondary"
              className="h-[1.75rem] !w-auto rounded-lg text-xs font-semibold"
            >
              Connect
            </PrimaryButton>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LinkAccount;
