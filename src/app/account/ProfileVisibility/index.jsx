import React, { useState } from 'react';
import DepositIFill from '@/assets/icons/DepositIFill';
import FirstBet from '@/assets/icons/FirstBet';
import GradientCheckCircle from '@/assets/icons/GradientCheckCircle';
import VerifyEmail from '@/assets/icons/VerifyEmail';
import WhiteCheckCircle from '@/assets/icons/WhiteCheckCircle.';
import InputField from '@/components/Common/InputField';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import SwitchButton from '@/components/ChatWindow/Friends/SwitchButton';
import InfoIcon from '@/assets/icons/InfoIcon';

const steps = [
  {
    text: 'Verify your email',
    status: 'Completed',
    icon: <VerifyEmail className="h-6 w-6" />,
  },
  {
    text: 'Make your first deposit',
    status: 'Pending',
    icon: <DepositIFill className="h-6 w-6" />,
  },
  {
    text: 'Place your first bet',
    status: 'Pending',
    icon: <FirstBet className="h-6 w-6" />,
  },
];

const ProfileVisibility = () => {
  const [email, setEmail] = useState('');
  const [formErrors, setFormErrors] = useState({ email: '' });
  const handleChange = (e) => {
    setEmail(e.target.value);
    setFormErrors({ email: '' });
  };
  return (
    <div className="mb-6  gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
      <div className="mb-6 flex  items-center justify-between  max-sm:mb-3 max-sm:flex-col">
        <h4 className="top-heading text-[1.0625rem] font-semibold max-sm:mb-5 max-sm:w-full">
          Profile visibility
        </h4>
        <div className="relative max-sm:w-full">
          <InputField
            type="email"
            name="email"
            value={email}
            placeholder="Email"
            onChange={handleChange}
            error={formErrors.email}
            label="CHANGE USERNAME"
            color="text-white-400"
            className="!rounded-[0.625rem]"
          />
          <PrimaryButton
            variant="secondary"
            className="absolute right-2 top-[1.875rem] h-[1.75rem] !w-auto rounded-lg text-xs font-semibold"
          >
            Change
          </PrimaryButton>
        </div>
      </div>
      <div>
        <div className="mb-[0.875rem] rounded-[0.625rem] border border-inputBgColor p-3">
          <SwitchButton
            rightLabel={
              <div className="flex items-center gap-2.5">
                Ghost Mode <InfoIcon />
              </div>
            }
            showLeftLabel={false}
            onKnobColor="bg-textGradient"
            offKnobColor="bg-erieBlack-300"
            onBgColor="bg-transparent"
            offBgColor="bg-textGradient"
            switchWidth="w-[2.125rem]"
            switchHeight="h-5"
            knobSize="h-[.875rem] w-[.875rem]"
            wrapperClassName="!justify-start"
          />
        </div>
        <div className="mb-[0.875rem] rounded-[0.625rem] border border-inputBgColor p-3">
          <SwitchButton
            rightLabel={
              <div className="flex items-center gap-2.5">
                Hide Bets <InfoIcon />
              </div>
            }
            showLeftLabel={false}
            onKnobColor="bg-textGradient"
            offKnobColor="bg-erieBlack-300"
            onBgColor="bg-transparent"
            offBgColor="bg-textGradient"
            switchWidth="w-[2.125rem]"
            switchHeight="h-5"
            knobSize="h-[.875rem] w-[.875rem]"
            wrapperClassName="!justify-start"
          />
        </div>
        <div className="mb-[0.875rem] rounded-[0.625rem] border border-inputBgColor p-3">
          <SwitchButton
            rightLabel={
              <div className="flex items-center gap-2.5">
                Keep Full Name Private <InfoIcon />
              </div>
            }
            showLeftLabel={false}
            onKnobColor="bg-textGradient"
            offKnobColor="bg-erieBlack-300"
            onBgColor="bg-transparent"
            offBgColor="bg-textGradient"
            switchWidth="w-[2.125rem]"
            switchHeight="h-5"
            knobSize="h-[.875rem] w-[.875rem]"
            wrapperClassName="!justify-start"
          />
        </div>
      </div>
    </div>
  );
};

export default ProfileVisibility;
