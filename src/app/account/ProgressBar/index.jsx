'use client';

import React from 'react';

export default function GradientProgressBar({
  value = 0,
  onChange = () => {},
  min = 0,
  max = 100,
  step = 1,
  gradient = 'linear-gradient(180deg, #E9BB58 0%, #F0D674 50%, #BF7C37 100%)',
  trackColor = 'linear-gradient(180deg, rgba(233, 187, 88, 0.5) 0%, rgba(240, 214, 116, 0.5) 50%, rgba(191, 124, 55, 0.5) 100%)',
}) {
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div className="relative my-[0.5rem] w-full">
      {/* Track */}
      <div
        className="relative h-3 w-full overflow-hidden rounded-full"
        style={{ background: trackColor }}
      >
        {/* Filled Gradient Bar with white stripes */}
        <div
          className="absolute left-0 top-0 h-3 rounded-full"
          style={{
            width: `${percentage}%`,
            background: `
              ${gradient}, 
              repeating-linear-gradient(
                45deg,
                rgba(255,255,255,0.4) 0px,
                rgba(255,255,255,0.4) 4px,
                transparent 4px,
                transparent 8px
              )
            `,
            backgroundBlendMode: 'overlay',
          }}
        />

        {/* Thumb */}
        <div
          className="absolute top-1/2 hidden h-4 w-4 -translate-y-1/2 rounded-full border border-yellow-200 bg-yellow-400 shadow-md"
          style={{ left: `calc(${percentage}% - 8px)` }}
        />
      </div>

      {/* Hidden Range Input */}
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        className="absolute -mt-3 h-4 w-full cursor-pointer opacity-0"
      />
    </div>
  );
}
