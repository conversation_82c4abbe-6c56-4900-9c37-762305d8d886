'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import Accordion from '@/components/ChatWindow/RecentChat/Accordion';

const ThemeAccordion = ({
  title,
  children,
  accordionBg = 'bg-inputBgColor',
  className = '',
  canOpen = true
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-3 rounded-[0.625rem]">
      {/* Header */}
      <button
        onClick={() => {
          if (canOpen) setIsOpen(!isOpen);
        }}
        className={`flex min-h-12 w-full items-center justify-between  rounded-[0.625rem]  px-5 py-3 text-sm font-medium focus:outline-none ${accordionBg} ${className}`}
      >
        <p className="w-full text-left text-[.9375rem] font-bold text-white-400">
          {title}
        </p>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.25 }}
        >
          <ChevronDown size={22} />
        </motion.div>
      </button>

      {/* Content with smooth expand/collapse */}
      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            key="content"
            initial="collapsed"
            animate="open"
            exit="collapsed"
            variants={{
              open: { height: 'auto', opacity: 1 },
              collapsed: { height: 0, opacity: 0 },
            }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="py-4 lg:px-6">{children}</div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ThemeAccordion;
