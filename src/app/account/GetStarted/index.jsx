import DepositIFill from '@/assets/icons/DepositIFill';
import FirstBet from '@/assets/icons/FirstBet';
import GradientCheckCircle from '@/assets/icons/GradientCheckCircle';
import VerifyEmail from '@/assets/icons/VerifyEmail';
import WhiteCheckCircle from '@/assets/icons/WhiteCheckCircle.';
import React from 'react';

const steps = [
  {
    text: 'Verify your email',
    status: 'Completed',
    icon: <VerifyEmail className="h-6 w-6" />,
  },
  {
    text: 'Make your first deposit',
    status: 'Pending',
    icon: <DepositIFill className="h-6 w-6" />,
  },
  {
    text: 'Place your first bet',
    status: 'Pending',
    icon: <FirstBet className="h-6 w-6" />,
  },
];

const GetStarted = () => {
  return (
    <div className="mb-6  gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
      <div className="mb-6 flex  items-center justify-between">
        <h4 className="top-heading text-[1.0625rem] font-semibold">
          Get started
        </h4>
        <div className="flex items-center justify-center gap-2">
          <GradientCheckCircle className="h-6 w-6" />
          <WhiteCheckCircle className="h-6 w-6" />
          <WhiteCheckCircle className="h-6 w-6" />
          <span className="font-semibold text-steelTeal-200">1/3</span>
        </div>
      </div>
      <div className="flex flex-col gap-3">
        {steps.map((step, i) => (
          <div
            key={i}
            className="flex items-center justify-between rounded-[0.625rem] bg-inputBgColor px-4 py-3"
          >
            <div className="flex items-center gap-2">
              <span className="text-white flex items-center gap-2  text-[.9375rem] font-semibold">
                {step.icon} {step.text}
              </span>
            </div>
            <div className="flex items-center gap-2">
              {step.status === 'Completed' ? (
                <GradientCheckCircle className="h-6 w-6" />
              ) : (
                <WhiteCheckCircle className="h-6 w-6" />
              )}
              <span
                className={`rounded text-[.9375rem] font-semibold max-sm:hidden ${
                  step.status === 'Completed'
                    ? 'bg-textGradient bg-clip-text text-transparent'
                    : 'text-white-400'
                }`}
              >
                {step.status}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GetStarted;
