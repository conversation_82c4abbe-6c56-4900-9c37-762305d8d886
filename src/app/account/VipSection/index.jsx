'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import DiamondIcon from '@/assets/webp/sapphire-diamond.webp';
import GradientProgressBar from '../ProgressBar';

const VipSection = () => {
  const [progress, setProgress] = useState(5);
  return (
    <div className="mb-6 flex items-center  gap-4 rounded-2xl bg-slateGray-700  px-6 py-4 max-sm:flex-col max-sm:items-start">
      <div className="flex items-center gap-2">
        <Image
          src={DiamondIcon}
          alt="VIP Diamond"
          width={110}
          height={110}
          className="max-sm:h-10 max-sm:w-10"
        />
        <h4 className="hidden text-xl font-bold max-sm:block">dukebafor</h4>
      </div>
      <div className="w-full">
        <h4 className="mb-3 text-xl font-bold max-sm:hidden">dukebafor</h4>
        <div className="flex flex-1 flex-col">
          <div className="flex justify-between text-[.9375rem] font-semibold text-white-400">
            <span className="">Your VIP Progress</span>
            <span className="">354,549 Points</span>
          </div>
          <GradientProgressBar
            value={progress}
            onChange={setProgress}
            min={0}
            max={100}
            step={1}
            labels={['0', '5%', '25%', '50%', '75%', '100%']}
          />

          <div className="flex justify-between text-[.9375rem] font-semibold text-white-400">
            <span>
              Platinum I <span className="block">250k</span>
            </span>
            <span>
              Platinum II <span className="block">500k</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VipSection;
