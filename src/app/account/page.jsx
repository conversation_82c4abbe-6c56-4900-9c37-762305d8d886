'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { AnimatePresence, motion } from 'framer-motion';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useGameStore from '@/store/useGameStore';
const Auth = dynamic(() => import('@/components/Auth'), { ssr: false });
import HomeIcon from '@/assets/icons/HomeIcon';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import AccountUser from '@/assets/icons/AccountUser';
import BetsIcon from '@/assets/icons/BetsIcon';
import DepositIcon from '@/assets/icons/DepositIFill';
import WithdrawalIcon from '@/assets/icons/WithdrawalIcon';
import AccountSettings from '@/assets/icons/AccountSettings';
import AccountLogout from '@/assets/icons/AccountLogout';
import VipSection from './VipSection';
import AccountPage from '.';
import Bets from './Bets';
import Deposit from './Deposit';
import Settings from './Settings';
import useAuthModalStore from '@/store/useAuthModalStore';

export default function Account({
  onTabChange = () => {},
  setIcon = () => {},
  defaultTab = 'Account',
  showGameFilter = true,
}) {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [localSearchQuery, setLocalSearchQuery] = useState('');

  const { openModal } = useAuthModalStore();
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const setSearchQuery = useGameStore((state) => state.setSearchQuery);

  const containerRef = useRef(null);
  const tabsRef = useRef({});

  const TABS = useMemo(
    () => [
      {
        label: 'Account',
        icon: <AccountUser size={16} />,
        content: <AccountPage />,
      },
      {
        label: 'Bets',
        icon: <BetsIcon size={16} />,
        content: <Bets />,
      },
      {
        label: 'Deposits',
        icon: <DepositIcon size={16} />,
        content: <Deposit />,
      },
      {
        label: 'Withdrawals',
        icon: <WithdrawalIcon size={16} />,
        content: <div>🏦 Withdrawals Details</div>,
      },
      {
        label: 'Settings',
        icon: <AccountSettings size={16} />,
        content: <Settings />,
      },
    ],
    [],
  );

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab]);

  useEffect(() => {
    if (!activeTab) return;
    if (activeTab === 'For You') {
      setIcon(<ForYouIcon size={16} activeMenu />);
    } else {
      const tab = TABS.find((t) => t.label === activeTab);
      if (tab) setIcon(tab.icon);
    }
    onTabChange(activeTab);
  }, [activeTab, TABS, setIcon, onTabChange]);

  useEffect(() => {
    if (!activeTab || !tabsRef.current[activeTab]) return;
    const timeout = setTimeout(() => {
      tabsRef.current[activeTab].scrollIntoView({
        behavior: 'auto',
        inline: 'center',
        block: 'nearest',
      });
    }, 0);
    return () => clearTimeout(timeout);
  }, [activeTab]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(localSearchQuery);
    }, 500);
    return () => clearTimeout(handler);
  }, [localSearchQuery, setSearchQuery]);

  // Drag-to-scroll handlers
  let isDown = false;
  let startX;
  let scrollLeft;

  const handleMouseDown = (e) => {
    isDown = true;
    containerRef.current.classList.add('cursor-grabbing');
    startX = e.pageX - containerRef.current.offsetLeft;
    scrollLeft = containerRef.current.scrollLeft;
  };

  const handleMouseLeaveOrUp = () => {
    isDown = false;
    containerRef.current.classList.remove('cursor-grabbing');
  };

  const handleMouseMove = (e) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX - containerRef.current.offsetLeft;
    const walk = (x - startX) * 1.5;
    containerRef.current.scrollLeft = scrollLeft - walk;
  };

  return (
    <div className="p-[0.625rem]">
      {showGameFilter && (
        <div
          ref={containerRef}
          className="flex w-full cursor-grab flex-nowrap overflow-x-auto rounded-xl bg-slateGray-700 p-1 shadow-tabsShadow [-ms-overflow-style:none] [scrollbar-width:none] md:space-x-3 [&::-webkit-scrollbar]:hidden"
          onMouseDown={handleMouseDown}
          onMouseLeave={handleMouseLeaveOrUp}
          onMouseUp={handleMouseLeaveOrUp}
          onMouseMove={handleMouseMove}
        >
          {TABS.map((tab) => (
            <button
              key={tab.label}
              ref={(el) => (tabsRef.current[tab.label] = el)}
              onClick={() => {
                if (tab.label === 'For You' && !isAuthenticated) {
                  localStorage.setItem('activeTab', 1);
                  setSelectedTab(1);
                  openModal(<Auth />);
                  return;
                }
                setActiveTab(tab.label);
                setLocalSearchQuery('');
                setSearchQuery('');
                setIcon(
                  tab.label === 'For You' ? (
                    <ForYouIcon size={16} activeMenu />
                  ) : (
                    tab.icon
                  ),
                );
              }}
              className={`flex min-w-[max-content] items-center gap-2 whitespace-nowrap rounded-[10px] px-4 py-1 text-[15px] font-semibold transition md:px-6 ${
                activeTab === tab.label
                  ? 'bg-primaryBorder text-white-1000 [&_svg_path]:fill-white-1000'
                  : 'text-steelTeal-200'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>
      )}

      {/* Tab Content with Framer Motion */}
      <div className="mt-4">
        <AnimatePresence mode="wait">
          {TABS.filter((t) => t.label === activeTab).map((tab) => (
            <motion.div
              key={tab.label}
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -15 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
            >
              {tab.content}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}
