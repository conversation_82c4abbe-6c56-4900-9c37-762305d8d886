const ThemeTable = ({ columns, data, tableBg = 'bg-slateGray-700' }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border-separate border-spacing-y-2 whitespace-nowrap">
        <thead>
          <tr className="text-left text-xs font-semibold text-white-400">
            {columns.map((col, idx) => (
              <th key={idx} className="px-4 pb-0">
                {col.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody
          className={`[&>tr:nth-child(even)>td]:bg-transparent
                      [&>tr>td:first-child]:rounded-l-[0.625rem]
                      [&>tr>td:last-child]:rounded-r-[0.625rem]`}
        >
          {data.map((row, i) => (
            <tr key={i} className="text-white overflow-hidden text-sm">
              {columns.map((col, j) => {
                const cellData = row[col.accessor];

                if (col.render) {
                  return (
                    <td
                      key={j}
                      className={`px-4 py-2 ${i % 2 === 0 ? tableBg : ''}`}
                    >
                      {col.render(cellData, row)}
                    </td>
                  );
                }

                return (
                  <td
                    key={j}
                    className={`px-4 py-2.5 ${i % 2 === 0 ? tableBg : ''}`}
                  >
                    {cellData}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ThemeTable;
