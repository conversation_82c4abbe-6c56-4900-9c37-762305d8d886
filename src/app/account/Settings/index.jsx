import React, { useState } from 'react';
import WhiteCheckCircle from '@/assets/icons/WhiteCheckCircle.';
import InputField from '@/components/Common/InputField';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import { motion, AnimatePresence } from 'framer-motion';

function Settings() {
  const [email, setEmail] = useState('');
  const [formErrors, setFormErrors] = useState({ email: '' });
  const handleChange = (e) => {
    setEmail(e.target.value);
    setFormErrors({ email: '' });
  };
  const [isOpen, setIsOpen] = useState(false);
  return (
    <>
      <div className="mb-6  gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
        <div className="mb-6 flex  items-center justify-between">
          <h4 className="top-heading text-[1.0625rem] font-semibold">
            Get started
          </h4>
          <div className="flex items-center justify-center gap-2">
            <WhiteCheckCircle className="h-6 w-6" />
            <span className="font-semibold text-steelTeal-200">Unverified</span>
          </div>
        </div>
        <div className="relative w-full">
          <InputField
            type="email"
            name="email"
            value={email}
            placeholder="Email"
            onChange={handleChange}
            error={formErrors.email}
            label="CHANGE EMAIL"
            color="text-white-400"
            className="!rounded-[0.625rem]"
          />
          <PrimaryButton
            variant="secondary"
            className="absolute right-2 top-[1.875rem] h-[1.75rem] !w-auto rounded-lg text-xs font-semibold"
          >
            Change
          </PrimaryButton>
        </div>
        <PrimaryButton
          variant="secondary"
          className="mt-6 !w-auto rounded-lg text-[.9375rem] font-semibold"
        >
          Send verification email
        </PrimaryButton>
      </div>
      <div className="mb-6  gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
        <div className="mb-6 flex  items-center justify-between">
          <h4 className="top-heading text-[1.0625rem] font-semibold">
            Change password
          </h4>
        </div>
        <div className="relative mb-6 w-full">
          <InputField
            type="password"
            name="email"
            value={email}
            placeholder="Email"
            onChange={handleChange}
            error={formErrors.email}
            label="OLD PASSWORD"
            color="text-white-400"
            className="!rounded-[0.625rem]"
          />
        </div>
        <div className="relative mb-6 w-full">
          <InputField
            type="password"
            name="email"
            value={email}
            placeholder="Email"
            onChange={handleChange}
            error={formErrors.email}
            label="NEW PASSWORD"
            color="text-white-400"
            className="!rounded-[0.625rem]"
          />
        </div>
        <PrimaryButton
          variant="secondary"
          className="mt-6 !w-auto rounded-lg text-[.9375rem] font-semibold"
        >
          Change Password
        </PrimaryButton>
      </div>
      {/* <div className="mb-6  gap-4 rounded-2xl bg-slateGray-700 p-6 max-sm:p-4">
        <div className="mb-6 flex  items-center justify-between">
          <h4 className="top-heading text-[1.0625rem] font-semibold">
            Two-Factor Authentication
          </h4>
          <div className="flex items-center justify-center gap-2">
            <WhiteCheckCircle className="h-6 w-6" />
            <span className="font-semibold text-steelTeal-200">Unverified</span>
          </div>
        </div>
        <p className="text-sm text-white-400">
          Using two-factor authentication is highly recommended because it
          protects your account with both your password and your phone. While
          2FA is enabled, you will not be able to login via Steam.
        </p>

        <PrimaryButton
          variant="secondary"
          className="mt-6 !w-auto rounded-lg text-[.9375rem] font-semibold"
        >
          Enable 2FA
        </PrimaryButton>
      </div> */}
      <div className="mb-6 gap-4 rounded-2xl bg-slateGray-700 px-6 py-5 max-sm:p-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h4 className="top-heading text-[1.0625rem] font-semibold">
            Login history
          </h4>
          <PrimaryButton
            variant="secondary"
            className="!w-auto rounded-lg text-[.9375rem] font-semibold"
            onClick={() => setIsOpen((prev) => !prev)}
          >
            {isOpen ? 'Hide' : 'Show'}
          </PrimaryButton>
        </div>

        {/* Accordion Content */}
        <AnimatePresence initial={false}>
          {isOpen && (
            <motion.div
              key="content"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
              className="overflow-hidden"
            >
              <div className="mt-4 space-y-3">
                <div className="rounded-lg bg-slateGray-600 p-3 text-sm">
                  22 Aug 2025 - Logged in from Chrome (Indore, India)
                </div>
                <div className="rounded-lg bg-slateGray-600 p-3 text-sm">
                  21 Aug 2025 - Logged in from Safari (iPhone)
                </div>
                <div className="rounded-lg bg-slateGray-600 p-3 text-sm">
                  20 Aug 2025 - Logged in from Edge (Windows)
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
}

export default Settings;
