import React from 'react';
import ThemeTable from '../ThemeTable';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import TableStar from '@/assets/icons/TableStar';

const columns = [
  { header: 'Game', accessor: 'game' },
  { header: 'Date And Time', accessor: 'date' },
  {
    header: 'Bet ID',
    accessor: 'betId',
    render: (betId) => (
      <span className="text bg-textGradient bg-clip-text font-semibold text-transparent">
        {betId}
      </span>
    ),
  },
  {
    header: 'Amount',
    accessor: 'amount',
    render: (amount) => (
      <div className="text-white flex items-center gap-1">
        <span>{amount}</span>
        <TableStar className="h-4 w-4" />
      </div>
    ),
  },
  { header: 'Multiplier', accessor: 'multiplier' },
  {
    header: 'Payout',
    accessor: 'payout',
    render: (payout) => (
      <div className="text-white flex items-center gap-1">
        <span>{payout}</span>
        <TableStar className="h-4 w-4" />
      </div>
    ),
  },
];

const data = [
  {
    game: 'Sweet Bonanza',
    date: '13.05.2024 / 21:16',
    betId: '#245194',
    amount: '9,027.40',
    multiplier: 'X12.36',
    payout: '9,027.40',
  },
  {
    game: 'God of Olymous',
    date: '13.05.2024 / 20:45',
    betId: '#245183',
    amount: '616.76',
    multiplier: 'X0.00',
    payout: '616.76',
  },
  {
    game: 'Sweet Bonanza',
    date: '13.05.2024 / 21:16',
    betId: '#245194',
    amount: '20.22',
    multiplier: 'X12.36',
    payout: '20.22',
  },
  {
    game: 'God of Olymous',
    date: '13.05.2024 / 20:45',
    betId: '#245183',
    amount: '4,525.02',
    multiplier: 'X0.00',
    payout: '4,525.02',
  },
];

function Bets() {
  return (
    <>
      <div className="my-6 flex items-center gap-3">
        <PrimaryButtonOutline className="!rounded-[0.625rem] !text-[.9375rem]">
          Casino
        </PrimaryButtonOutline>
        <PrimaryButtonOutline className="!rounded-[0.625rem] border-slateGray-700 bg-slateGray-700 !text-[.9375rem]">
          Sports
        </PrimaryButtonOutline>
      </div>
      <ThemeTable columns={columns} data={data} />
    </>
  );
}

export default Bets;
