'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import UploadImg from '@/assets/webp/upload-img.webp';
import GroupBannerModal from '@/components/Common/Modal/GroupBannerModal';

const BannerUploadSection = () => {
  const [bannerRequired, setBannerRequired] = useState(false);
  const handleBannerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (validateImageFile(file, setBannerError, 'banner')) {
        setBannerImage(file);
        setSelectedDefaultBanner(null);
        const reader = new FileReader();
        reader.onloadend = () => {
          setBannerPreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Reset preview if validation fails
        setBannerImage(null);
        setBannerPreview(null);
      }
    }
  };

  return (
    <div className="mb-6 grid grid-cols-4 gap-6 max-lg:gap-3  max-sm:grid-cols-1 ">
      <div className="w-full">
        <div class="mb-1 flex items-center justify-between">
          <label class="relative flex gap-2 text-xs font-semibold">
            Profile Image
          </label>
          <div class="hover:text-white text-sm text-gray-400 transition-colors hover:cursor-pointer">
            Select New
          </div>
        </div>
        <div
          className={`relative w-full rounded-[0.625rem] border 
                        ${bannerRequired ? 'border-red-500 ring-2 ring-red-500' : 'border-richBlack-1000'} 
                        border-[2px] border-dashed border-steelTeal-620 bg-inputBgColor`}
        >
          <div className="flex flex-col items-center justify-center p-6 max-lg:p-4">
            {/* Upload Section */}
            <label
              htmlFor="banner-img"
              className="flex cursor-pointer flex-col items-center justify-center"
            >
              <Image
                src={UploadImg}
                className="mx-auto mb-4 w-9"
                alt="Upload"
              />
              <p className="text-[.9375rem] font-semibold text-white-360 max-lg:text-xs">
                Upload Banner Image
              </p>
              <input
                type="file"
                id="banner-img"
                onChange={handleBannerImageChange}
                className="hidden"
                accept="image/png, image/jpeg, image/jpg, image/webp"
              />
            </label>

            <p className="my-1 text-xs font-medium text-gray-400">Or</p>
            <p
              onClick={() =>
                openModal(
                  <GroupBannerModal
                    casinoBanners={casinoBanners}
                    sportsBanners={sportsBanners}
                    genericBanners={genericBanners}
                    onSelect={(banner) => {
                      setBannerPreview(banner);
                      setSelectedDefaultBanner(banner);
                      setBannerImage(null);
                      setBannerError('');
                      setBannerRequired(false);
                    }}
                  />,
                )
              }
              className="cursor-pointer text-xs font-semibold text-white-360 underline underline-offset-4"
            >
              Select from Defaults
            </p>
          </div>
        </div>
      </div>
      <div className="col-span-3 w-full max-sm:col-span-1">
        <div class="mb-1 flex items-center justify-between">
          <label class="relative flex gap-2 text-xs font-semibold">
            Cover Image
          </label>
          <div class="hover:text-white text-sm text-gray-400 transition-colors hover:cursor-pointer">
            Select New
          </div>
        </div>
        <div
          className={`relative w-full rounded-[0.625rem] border 
                        ${bannerRequired ? 'border-red-500 ring-2 ring-red-500' : 'border-richBlack-1000'} 
                        border-[2px] border-dashed border-steelTeal-620 bg-inputBgColor`}
        >
          <div className="flex flex-col items-center justify-center p-6 max-lg:p-4">
            {/* Upload Section */}
            <label
              htmlFor="banner-img"
              className="flex cursor-pointer flex-col items-center justify-center"
            >
              <Image
                src={UploadImg}
                className="mx-auto mb-4 w-9"
                alt="Upload"
              />
              <p className="text-[.9375rem] font-semibold text-white-360 max-lg:text-xs">
                Upload Banner Image
              </p>
              <input
                type="file"
                id="banner-img"
                onChange={handleBannerImageChange}
                className="hidden"
                accept="image/png, image/jpeg, image/jpg, image/webp"
              />
            </label>

            <p className="my-1 text-xs font-medium text-gray-400">Or</p>
            <p
              onClick={() =>
                openModal(
                  <GroupBannerModal
                    casinoBanners={casinoBanners}
                    sportsBanners={sportsBanners}
                    genericBanners={genericBanners}
                    onSelect={(banner) => {
                      setBannerPreview(banner);
                      setSelectedDefaultBanner(banner);
                      setBannerImage(null);
                      setBannerError('');
                      setBannerRequired(false);
                    }}
                  />,
                )
              }
              className="cursor-pointer text-xs font-semibold text-white-360 underline underline-offset-4"
            >
              Select from Defaults
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerUploadSection;
