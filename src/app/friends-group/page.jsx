'use client';

import GradientTabs from '@/components/Common/GradientTabs';
import {
  useGetFriendsListQuery,
  useGetGroupListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import { useEffect, useState } from 'react';
import FriendsPage from '../../components/Dashboard/FriendsPage';
import GroupPage from '../../components/Dashboard/GroupPage';

function FriendsGroup() {
  const { isAuthenticated } = useAuthStore((state) => state);

  const {
    data: friendsList,
    isLoading: friendsListLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({
    params: { search: '' },
    enabled: isAuthenticated,
  });

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch,
    isLoading: isMyGroupsLoading,
  } = useGetGroupListQuery({ enabled: isAuthenticated });

  const [friendList, setFriendList] = useState(friendsList);
  const [groupList, setGroupListList] = useState(data?.groups);

  useEffect(() => {
    setFriendList(friendsList);
    setGroupListList(data?.groups);
  }, [friendsList, data?.groups]);

  const tabs = [
    {
      label: 'Friends',
      content: <FriendsPage users={friendList} />,
    },
    {
      label: 'Groups',
      content: <GroupPage groups={groupList} />,
    },
  ];
  return (
    <div className="mx-auto max-w-[55.125rem] px-[0.625rem]">
      <GradientTabs tabs={tabs} />
    </div>
  );
}

export default FriendsGroup;
