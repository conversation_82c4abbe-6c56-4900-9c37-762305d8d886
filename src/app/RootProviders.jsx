'use client';

import { Toaster } from 'react-hot-toast';
import Script from 'next/script';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { SkeletonTheme } from 'react-loading-skeleton';
import { ReactQueryClientProvider } from '@/components/ReactQueryClientProvider/ReactQueryClientProvider';
import ClientSuspenseWrapper from '@/components/LayoutWrapper/ClientSuspenseError';
import SideMenu from '@/components/SideMenu';
import ChatWindow from '@/components/ChatWindow';
import Lobby from '@/components/Lobby';

import dynamic from 'next/dynamic';
const DraggableGamePopup = dynamic(() => import('@/components/DraggableGamePopUp'),{ ssr: false } );

import BottomMenu from '@/components/BottomMenu';

const Modal = dynamic(() => import('@/components/Common/Modal/page'), {
  ssr: false,
});

const CallModal = dynamic(() => import('@/components/Common/Modal/CallModal'), {
  ssr: false,
});

const Auth = dynamic(() => import('@/components/Auth'), { ssr: false });

export default function RootProviders({ children }) {
  return (
    <GoogleOAuthProvider
      clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ''}
    >
      <ClientSuspenseWrapper>
        <ReactQueryClientProvider>
          <SkeletonTheme baseColor="rgb(5, 20, 56)" highlightColor="#444">
            
            {/* Sidebar (left) */}
            <SideMenu />

            {/* Global notifications */}
            <Toaster position="top-right" />

            {/* Modals */}
            <CallModal />
            <Modal />
            <Auth/>

            {/* Main content (center) */}
            {children}

            {/* Right chat */}
            <ChatWindow />
            {/* Draggable pop up */}
            <DraggableGamePopup/>

            {/* Lobby, Bottom menu */}
            <Lobby />
            <BottomMenu />
          </SkeletonTheme>
        </ReactQueryClientProvider>
      </ClientSuspenseWrapper>

      {/* Heavy scripts */}
      <Script src="/pixi-legacy.min.js" strategy="afterInteractive" />
    </GoogleOAuthProvider>
  );
}
