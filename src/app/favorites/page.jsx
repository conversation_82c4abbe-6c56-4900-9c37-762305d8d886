'use client';

import React, { useState } from 'react';
import LobbyGames from '@/components/HomePage/LobbyGames';
import GameSlider from '@/components/HomePage/GameSlider/Gameslider';
import GameTabs from '@/components/HomePage/GameTabs';
import LiveTable from '@/components/HomePage/LiveTable';
import Groups from '@/components/HomePage/Groups';
import useAuthStore from '@/store/useAuthStore';
import Category from '@/components/Category';
import useRecommendations from '@/hooks/useRecommendations';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import Favorites from '@/components/Favorites';
import ProviderSlider from '@/components/HomePage/ProviderSlider';
import Gamefilter from '@/components/HomePage/GameFilter';
import ContinuePlaying from '@/components/ContinuePlaying';

function FavoritesPage() {
  const { isAuthenticated } = useAuthStore();
  const [selectedTab, setSelectedTab] = useState('Favorites');
  const [icon, setIcon] = useState(<ForYouIcon size={16} activeMenu />)
  const { data,
    gamesLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useRecommendations({ limit: 100, enabled: selectedTab === 'For You' || selectedTab === 'Lobby' })

  return (
    <>
      <Gamefilter onTabChange={setSelectedTab} setIcon={setIcon} defaultTab="Favorites" />

      {selectedTab === 'Lobby' && (
        <>
          <GameSlider />
          <Groups />
        </>)}
      {isAuthenticated && selectedTab === 'Lobby' && <ProviderSlider />}

      {selectedTab === 'Lobby' ? (
        <>
          {isAuthenticated
            && (
              <>
                <Category
                  selectedTab="For You"
                  externalGames={data}
                  externalLoading={gamesLoading}
                  externalFetchNextPage={fetchNextPage}
                  externalHasNextPage={hasNextPage}
                  externalIsFetchingNextPage={isFetchingNextPage}
                  externalIcon={icon}
                  isCarousel
                />
              </>
            )
          }
          <LobbyGames />
        </>
      ) : selectedTab === 'For You' ? (
        <Category
          selectedTab="For You"
          externalGames={data}
          externalLoading={gamesLoading}
          externalFetchNextPage={fetchNextPage}
          externalHasNextPage={hasNextPage}
          externalIsFetchingNextPage={isFetchingNextPage}
          externalIcon={icon}
        />
      ) : selectedTab === 'Favorites' ? (
        <>
          <Favorites />
        </>
      ) : selectedTab === 'Continue Playing' ? (
        <>
          <ContinuePlaying />
        </>
      ) :
        (
          <Category selectedTab={selectedTab} externalIcon={icon}/>
        )}
      {selectedTab === 'Lobby' && <>
        <GameTabs />
        <LiveTable />
      </>}

    </>)
}
export default FavoritesPage;
