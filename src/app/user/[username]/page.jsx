'use client';

import TextTabs from '@/components/Common/TextTabs';
import { useEffect } from 'react';
import About from '../../../components/Friends/About';
import PrivateBanner from '../../../components/Friends/PrivateBanner';

import useAuthStore from '@/store/useAuthStore';
import { useParams, useRouter } from 'next/navigation';

import useFriendDetails from '@/hooks/useFriendDetails';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import PrivateChat from '../../../components/Friends/PrivateChat';

function Private() {
  const params = useParams();
  console.log('params', params);
  const username = params?.username?.split('-').join(' ');
  const { data: userDetails, isLoading } = useFriendDetails({ username });
  const { setUserId } = usePrivateChatStore();
  const router = useRouter();
  console.log('privateChatDetails', userDetails);

  useEffect(() => {
    if (!userDetails?.id) return;
    setUserId(userDetails.id);
  }, [userDetails?.id]);

  const tabs = [
    {
      label: 'About',
      content: <About userDetails={userDetails} />,
    },
    {
      label: 'Chat',
      content: <PrivateChat receiverDetails={userDetails} />,
    },
  ];
  if (!userDetails && !isLoading) {
    return (
      <div className="flex h-[90vh] w-full flex-col items-center justify-center text-center">
        <h1 className="text-2xl font-bold text-gray-200 md:text-3xl">
          User Not Found
        </h1>
        <p className="mt-2 max-w-md text-sm text-gray-400 md:text-base">
          Sorry, we couldnt find that user. They may not exist or have been
          removed.
        </p>
        <button
          onClick={() => router.push('/')} // replace with your routing
          className="text-black mt-6 rounded-lg 
             bg-TintGoldGradient px-6 py-2 text-sm font-medium text-black-1000 
             shadow-lg hover:from-yellow-500 hover:via-yellow-600
             hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2"
        >
          Go Back Home
        </button>
      </div>
    );
  }
  return (
    <div className="p-[0.625rem]">
      <PrivateBanner groupData={userDetails} isLoading={isLoading} />
      <TextTabs
        tabs={tabs}
        onChange={(index) => console.log('Active tab index:', index)}
      />
    </div>
  );
}

export default Private;
