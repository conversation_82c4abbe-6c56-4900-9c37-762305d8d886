// app/layout.jsx (server component)
import { Inter } from 'next/font/google';
import './globals.css';
import Footer from '@/components/Footer';
import MainSection from '@/components/Common/MainSection';
import Header from '@/components/Header';
import RootProviders from './RootProviders';

const inter = Inter({ subsets: ['latin'] });
// const { setActiveMenu, openMenu, setOpenMenu } = useGeneralStore();

export const metadata = {
  title: 'FansBets',
  description: 'online casino platform',
  icons: {
    icon: '/favicon.ico',
  },
};

export const dynamic = 'force-dynamic';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
      />
      <body className={inter.className}>
        <div className="bg-black-1000">
          <RootProviders>
            <MainSection>
              <Header />
              {children}
            </MainSection>
            <Footer />
          </RootProviders>
        </div>
      </body>
    </html>
  );
}
