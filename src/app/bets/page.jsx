'use client';

import React, { useEffect, useState } from 'react';
import GradientTabs from '@/components/Common/GradientTabs';
import {
  useGetFriendsListQuery,
  useGetGroupListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import GradientTrophy from '@/assets/images/svg-images/trophy.svg';
import ChallengesIcon from '@/assets/icons/ChallengesIcon';
import Image from 'next/image';
import CasinoTabs from './CasinoTabs';
import SportsTabs from './SportsTabs';

function BetsPage() {
  const { isAuthenticated } = useAuthStore((state) => state);

  const {
    data: friendsList,
    isLoading: friendsListLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({
    params: { search: '' },
    enabled: isAuthenticated,
  });

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch,
    isLoading: isMyGroupsLoading,
  } = useGetGroupListQuery({ enabled: isAuthenticated });

  const [friendList, setFriendList] = useState(friendsList);
  const [groupList, setGroupListList] = useState(data?.groups);

  useEffect(() => {
    setFriendList(friendsList);
    setGroupListList(data?.groups);
  }, [friendsList, data?.groups]);

  const tabs = [
    {
      label: 'Casino',
      content: <CasinoTabs />,
    },
    {
      label: 'Sports',
      content: <SportsTabs />,
    },
  ];
  return (
    <div className="mx-auto max-w-[55.125rem] px-[0.625rem]">
      <div className="mb-4 flex items-center gap-1">
        <Image src={GradientTrophy} alt="Trophy" />
        <p className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent">
          Bets
        </p>
      </div>
      <GradientTabs classes="justify-start" tabs={tabs} />
    </div>
  );
}

export default BetsPage;
