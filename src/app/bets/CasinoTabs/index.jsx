import React from 'react';
import TableStar from '@/assets/icons/TableStar';
import ThemeTable from '@/app/account/ThemeTable';
import GameImg1 from '@/assets/webp/table-game-1.webp';
import GameImg2 from '@/assets/webp/table-game-2.webp';
import GameImg3 from '@/assets/webp/table-game-3.webp';
import Image from 'next/image';
import CasinoMobTabs from '../CasinoMobTabs';
const columns = [
  {
    header: 'GAMES',
    accessor: 'game',
    render: (game) => (
      <div className="flex items-center gap-2">
        <div className="h-9 w-[1.875rem] overflow-hidden rounded-[.25rem]">
          <Image
            src={GameImg1}
            alt="Game"
            className="h-full w-full object-cover"
          />
        </div>
        <div>
          <h4 className="mb-[.125rem] text-sm font-medium text-white-1000">
            Operation Space
          </h4>
          <p className="text-xs text-steelTeal-200">solidfighter383</p>
        </div>
      </div>
    ),
  },
  {
    header: 'BET AMOUNT',
    accessor: 'betId',
    render: (betId) => (
      <div className="text-white flex items-center gap-1">
        <span>{betId}</span>
        <TableStar className="h-4 w-4" />
      </div>
    ),
  },
  {
    header: 'MULTIPLIER',
    accessor: 'amount',
  },

  {
    header: 'PAYOUT',
    accessor: 'payout',
    render: (payout) => (
      <div className="text-white flex items-center gap-1">
        <span>{payout}</span>
        <TableStar className="h-4 w-4" />
      </div>
    ),
  },
];

const data = [
  {
    game: '',
    amount: '9,027.40',
    multiplier: 'X12.36',
    payout: '9,027.40',
  },
  {
    game: '',
    amount: '9,027.40',
    multiplier: 'X12.36',
    payout: '9,027.40',
  },
  {
    game: '',
    amount: '9,027.40',
    multiplier: 'X12.36',
    payout: '9,027.40',
  },
  {
    game: '',
    amount: '9,027.40',
    multiplier: 'X12.36',
    payout: '9,027.40',
  },
];

function CasinoTabs() {
  return (
    <>
      <div className="hidden md:block">
        <ThemeTable columns={columns} data={data} />
      </div>
      <CasinoMobTabs />
    </>
  );
}

export default CasinoTabs;
