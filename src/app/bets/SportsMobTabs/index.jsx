import React from 'react';
import TableStar from '@/assets/icons/TableStar';
import ThemeTable from '@/app/account/ThemeTable';
import GameImg1 from '@/assets/webp/table-game-1.webp';
import GameImg2 from '@/assets/webp/table-game-2.webp';
import GameImg3 from '@/assets/webp/table-game-3.webp';
import Image from 'next/image';

function SportsMobTabs() {
  return (
    <>
      <div className="block md:hidden">
        <div className="mb-2 flex justify-between">
          <p className="text-xs font-semibold uppercase text-steelTeal-200">
            GAME
          </p>
          <p className="text-xs font-semibold uppercase text-steelTeal-200">
            PAYOUT
          </p>
        </div>
        <div
          className="[&>:nth-child(even)]:bg-transparent
    [&>:nth-child(odd)]:bg-slateGray-700"
        >
          <div className="card mb-2   rounded-lg px-4 py-2">
            <div className="mb-2 flex justify-between">
              <div className="flex items-center gap-2">
                <div className="h-9 w-[1.875rem] overflow-hidden rounded-[.25rem]">
                  <Image
                    src={GameImg1}
                    alt="Game"
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <h4 className="mb-[.125rem] text-sm font-medium text-white-1000">
                    Operation Space
                  </h4>
                  <p className="text-xs text-steelTeal-200">solidfighter383</p>
                </div>
              </div>
              <div className="text-white flex items-center gap-1 text-sm">
                <span>200.55</span>
                <TableStar className="h-4 w-4" />
              </div>
            </div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-xs text-steelTeal-200">Bet Amount</span>
              <div className="text-white flex items-center gap-1 text-sm">
                <span>200.55</span>
                <TableStar className="h-4 w-4" />
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-steelTeal-200">Multiplier</span>
              <span className="text-white text-sm">200.55</span>
            </div>
          </div>
          <div className="card mb-2   rounded-lg px-4 py-2">
            <div className="mb-2 flex justify-between">
              <div className="flex items-center gap-2">
                <div className="h-9 w-[1.875rem] overflow-hidden rounded-[.25rem]">
                  <Image
                    src={GameImg1}
                    alt="Game"
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <h4 className="mb-[.125rem] text-sm font-medium text-white-1000">
                    Operation Space
                  </h4>
                  <p className="text-xs text-steelTeal-200">solidfighter383</p>
                </div>
              </div>
              <div className="text-white flex items-center gap-1 text-sm">
                <span>200.55</span>
                <TableStar className="h-4 w-4" />
              </div>
            </div>
            <div className="mb-2 flex items-center justify-between">
              <span className="text-xs text-steelTeal-200">Bet Amount</span>
              <div className="text-white flex items-center gap-1 text-sm">
                <span>200.55</span>
                <TableStar className="h-4 w-4" />
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs text-steelTeal-200">Multiplier</span>
              <span className="text-white text-sm">200.55</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default SportsMobTabs;
