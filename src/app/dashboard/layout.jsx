'use client';

import React, { useEffect, useState } from 'react';
import Tabs from '@/components/Common/Tabs';
import GradientTabs from '@/components/Common/GradientTabs';
// import FriendsPage from './FriendsPage';
// import GroupPage from './GroupPage';
import {
  useGetFriendsListQuery,
  useGetGroupListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import FriendsPage from '@/components/Dashboard/FriendsPage';
import GroupPage from '@/components/Dashboard/GroupPage';
// import FriendsPage from '@/components/Dashboard/FriendsPage';
// import GroupPage from '@/components/Dashboard/GroupPage';

function DashboardLayout({ children }) {
  const { isAuthenticated } = useAuthStore((state) => state);

  const {
    data: friendsList,
    isLoading: friendsListLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({
    params: { search: '' },
    enabled: isAuthenticated,
  });

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch,
    isLoading: isMyGroupsLoading,
  } = useGetGroupListQuery({ enabled: isAuthenticated });

  const [friendList, setFriendList] = useState(friendsList);
  const [groupList, setGroupListList] = useState(data?.groups);

  useEffect(() => {
    setFriendList(friendsList);
    setGroupListList(data?.groups);
  }, [friendsList, data?.groups]);

  const tabs = [
    {
      label: 'Friends',
      // content: <FriendsPage users={friendList} />,
      path:"friends"
    },
    {
      label: 'Groups',
      // content: <GroupPage groups={groupList} />,
      path:"groups"

    },
  ];
  return (
    <div className="mx-auto max-w-[55.125rem] px-[0.625rem]">
      <GradientTabs tabs={tabs} authTabs={[0]} />
      {children}
    </div>
  );
}

export default DashboardLayout;
