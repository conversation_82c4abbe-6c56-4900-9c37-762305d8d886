'use client';
import React from 'react';
import Image from 'next/image';
import HistoryIcon from '@/assets/images/svg-images/history.svg?url';
import Pragmatic from '@/assets/images/stock-images/pragmatic.webp';
import Evolution from '@/assets/images/stock-images/evolution.webp';
import Hacksaw from '@/assets/images/stock-images/hacksaw.webp';
import Nolimit from '@/assets/images/stock-images/nolimit.webp';
import PushGaming from '@/assets/images/stock-images/pushgaming.webp';
import RelaxGaming from '@/assets/images/stock-images/relaxgaming.webp';
import useGeneralStore from '@/store/useGeneralStore';

export default function PublishersPage() {
  const providers = [
    { icon: Pragmatic },
    { icon: Evolution },
    { icon: Hacksaw },
    { icon: Nolimit },
    { icon: PushGaming },
    { icon: RelaxGaming },
  ];

  const { openMenu, openChat } = useGeneralStore();

  return (
    <div className="my-3 mb-6 px-3">
      <div className="mb-4 flex items-center gap-2">
        <Image
          src={HistoryIcon}
          alt="History Icon"
          className="h-5 w-5 object-cover"
          width={20}
          height={20}
        />
        <p className="bg-cardBorderGradient bg-clip-text text-sm font-semibold uppercase text-transparent">
          Publishers
        </p>
      </div>

      <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
        {providers.map((provider, index) => (
          <div
            key={index}
            className={`
              flex-[0_0_calc(100%/6)]
              max-3xl:flex-[0_0_calc(100%/5)]
              max-xxl:flex-[0_0_calc(100%/4)]
              max-2xl:flex-[0_0_calc(100%/3.5)]
              max-xl:flex-[0_0_calc(100%/4)]
              max-lg:flex-[0_0_calc(100%/3.5)]
              max-md:flex-[0_0_calc(100%/3.5)]
              max-sm:flex-[0_0_calc(100%/2.2)]
              ${
                openMenu && openChat
                  ? ' max-xxl:flex-[0_0_calc(100%/6)]'
                  : openMenu
                    ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]'
                    : openChat
                      ? ''
                      : 'max-xxl:flex-[0_0_calc(100%/6)]'
              }
            `}
          >
            <button
              className="
                flex max-h-[6rem] min-h-[6rem] w-full flex-col items-center justify-center 
                gap-1 rounded-[0.625rem] bg-slateGray-700 px-2 py-1 transition 
                md:rounded-2xl md:px-4 md:py-4 max-sm:max-h-16 max-sm:min-h-16
              "
            >
              <Image
                className="w-[7rem] max-sm:w-20"
                src={provider.icon}
                alt={`Provider ${index + 1}`}
                width={1000}
                height={1000}
              />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
