/* eslint-disable no-shadow */
/* eslint-disable no-nested-ternary */

'use client';

import { useEffect, useState, useRef, useMemo } from 'react';
import dynamic from 'next/dynamic';
import {
  useGameLaunchQuery,
  useProviderRecommendations,
  useUpdateFavoriteMutation,
} from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import {
  getAccessToken,
  getTextValue,
  slugify,
  unSlugify,
} from '@/utils/helper';
import { walletSocket } from '@/utils/socket';
import ExpandIcon from '@/assets/icons/ExpandIcon';
import FocusIcon from '@/assets/icons/FocusIcon';
import GraphIcon from '@/assets/icons/GraphIcon';
import PictureInPicture from '@/assets/icons/PictureInPicture';
import SwitchButton from '@/components/ChatWindow/Friends/SwitchButton';
import ThemeAccordion from '@/app/account/ThemeAccordion';
import Image from 'next/image';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import defaultImage from '../../../../../assets/images/demo-image/card-image.png';
const Auth = dynamic(() => import('@/components/Auth'), { ssr: false });
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import { useQueryClient } from '@tanstack/react-query';
import GamePopup from '@/components/Common/Modal/GamePopup';
import useTheatreStore from '@/store/useThreatreStore';
import useDraggableStore from '@/store/useDraggableGamePopup';
import CarouselSection from '@/components/Common/CarouselSection';
import PlayCard from '@/components/PlayCard';
import { useRouter, useParams } from 'next/navigation';
import Tooltip from '@/components/Common/Tooltip';
import useIsMobile from '@/hooks/useIsMobile';
import useGeneralStore from '@/store/useGeneralStore';
import useAuthModalStore from '@/store/useAuthModalStore';

const GameDetailsAndProviderGames = ({
  isAuthenticated,
  gameName,
  gameProvider,
}) => {
  const params = useParams();
  const router = useRouter();
  const [isDemoMode, setIsDemoMode] = useState(!isAuthenticated);
  const [imgSrc, setImgSrc] = useState(null);
  const [hasPreloadedSecondPage, setHasPreloadedSecondPage] = useState(false);

  const { data: gameDetails, isLoading } = useGameLaunchQuery({
    params: {
      gameName: gameName || unSlugify(params?.name),
      gameProvider: gameProvider || unSlugify(params?.provider),
      isMobile: window?.innerWidth < 441,
      currency: 'USD',
      isDemo: isDemoMode ? 'true' : 'false',
    },
    enabled: true,
  });

  const {
    data: providerGames,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useProviderRecommendations({
    providerId: gameDetails?.providerId || '',
    enabled: !!gameDetails?.providerId,
  });

  useEffect(() => {
    setImgSrc(
      gameDetails?.gameThumbnail &&
        typeof gameDetails.gameThumbnail === 'string' &&
        gameDetails.gameThumbnail.startsWith('http')
        ? gameDetails.gameThumbnail
        : defaultImage,
    );
  }, [gameDetails?.gameThumbnail]);

  useEffect(() => {
    if (isAuthenticated) {
      setIsDemoMode(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (
      providerGames?.gameData?.length > 0 &&
      hasNextPage &&
      !isFetchingNextPage &&
      gameDetails?.providerId &&
      !hasPreloadedSecondPage
    ) {
      const timer = setTimeout(() => {
        fetchNextPage();
        setHasPreloadedSecondPage(true);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [
    providerGames?.gameData?.length,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    gameDetails?.providerId,
    hasPreloadedSecondPage,
  ]);

  const handleCarouselReachEnd = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const handleGameClick = (provider, name) => {
    router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`);
  };

  return (
    <>
      {isLoading ? (
        <div className="flex h-96 items-center justify-center">
          {/* <MainLoader className="w-32" /> */}
        </div>
      ) : (
        <>
          <ThemeAccordion
            accordionBg="bg-slateGray-700"
            canOpen={true}
            title={
              <div className="flex items-center gap-2">
                <h4 className="text-xl font-medium uppercase text-white-1000 max-lg:text-sm ">
                  {getTextValue(gameDetails?.gameName) || 'Loading...'}
                </h4>
                <div className="flex gap-2 max-md:hidden">
                  <button
                    className=" z-2
                relative rounded-lg px-4 
                py-2 font-semibold text-white-1000 before:absolute 
                before:inset-0.5 before:rounded-lg 
                before:border-2 
                before:border-transparent 
                before:bg-reverseGradientBg before:content-[''] before:[-webkit-mask:linear-gradient(#fff_0_0)_padding-box,_linear-gradient(#fff_0_0)] before:[background-clip:border-box] before:[mask-composite:exclude]
                after:absolute after:left-0 after:top-0 after:z-[-1] after:h-full after:w-full after:bg-inputBgColor after:content-[''] max-lg:text-xs
              "
                  >
                    <span className="text-gradient relative z-10 block">
                      {getTextValue(gameDetails?.providerName) || 'N/A'}
                    </span>
                  </button>
                  <button className="rounded-lg bg-inputBgColor px-4 py-2 font-semibold text-steelTeal-200 max-lg:text-xs" 
                    onClick={()=>router.push(`/category/${slugify(gameDetails?.categoryName)}`)
                  }
                  >
                    {getTextValue(gameDetails?.categoryName) || 'N/A'}
                  </button>
                </div>
              </div>
            }
          >
            {/* max-h-48 max-w-36 overflow-hidden rounded-2xl md:min-w-[12.25rem] */}
            <div className="flex items-start gap-5 max-sm:flex-col max-sm:p-5">
              <div className="game-wrap relative max-h-48 min-w-36 overflow-hidden rounded-xl pb-[13%] max-lg:pb-[26%] max-md:pb-[46%]">
                <Image
                  src={imgSrc}
                  alt="Game"
                  className="absolute left-0 top-0 h-full w-full"
                  width={1000}
                  height={1000}
                  onError={() => setImgSrc(defaultImage)}
                />
              </div>
              <div>
                <p className="text-[.9375rem] text-white-1000 max-sm:text-xs">
                  {gameDetails?.description || 'No description available'}
                </p>
                <div className="mt-2 inline-block rounded-lg bg-inputBgColor px-4 py-2 text-xs font-semibold text-steelTeal-200">
                  Edge 3.62%
                </div>
              </div>
            </div>
          </ThemeAccordion>

          {providerGames?.gameData?.length > 0 && (
            <section className="rounded-lg bg-oxfordBlue-1000 shadow-container">
              <CarouselSection
                title={`${gameDetails?.providerName || 'Provider'} Most Popular`}
                navigationStyle="default"
                showNavigation
                cardGap="gap-0"
                containerMargin="mt-3 mb-10"
                titleClassName="text-[14px] font-[600] text-white-1000"
                emblaOptions={{
                  loop: false,
                  align: 'start',
                  slidesToScroll: 'auto',
                }}
                onReachEnd={handleCarouselReachEnd}
              >
                {providerGames?.gameData.map((game) => (
                  <div
                    key={game?.gameId}
                    className="flex-[0_0_calc(100%/6)] pl-3 max-3xl:flex-[0_0_calc(100%/5)] max-xxl:flex-[0_0_calc(100%/4)] max-xl:flex-[0_0_calc(100%/5)] max-lg:flex-[0_0_calc(100%/4.3)] max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]"
                  >
                    <PlayCard
                      gameId={game?.gameId}
                      gameImage={game?.iconUrl ?? ''}
                      gameName={game?.gameName?.EN || game?.gameName}
                      isFavorite={game?.isFavorite}
                      sizeVariant="fixed"
                      onClick={() =>
                        handleGameClick(
                          game?.providerName,
                          game?.gameName?.EN || game?.gameName,
                        )
                      }
                      providerName={game?.providerName}
                    />
                  </div>
                ))}
              </CarouselSection>
            </section>
          )}
        </>
      )}
    </>
  );
};

const GamePlayerWithControls = ({
  isAuthenticated,
  gameName,
  gameProvider,
}) => {
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useAuthModalStore();
  const [isDemoMode, setIsDemoMode] = useState(!isAuthenticated);
  const [favorite, setFavorite] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const queryClient = useQueryClient();
  const iframeRef = useRef(null);
  const { toggleTheatre } = useTheatreStore();
  const { openDraggablePopup, isOpen, updateDraggableContent } =
    useDraggableStore();
  const [selected, setSelected] = useState('');
  const [hovered, setHovered] = useState(null);

  const longPressTimeouts = useRef({});
  const tooltipHideTimeouts = useRef({});
  const isLongPressRef = useRef({});
  const isMobile = useIsMobile();

  const params = useParams();

  const {
    data: gameDetails,
    isLoading,
    refetch: refetchGameLaunch,
  } = useGameLaunchQuery({
    params: {
      gameName: gameName || unSlugify(params()?.name),
      gameProvider: gameProvider || unSlugify(params()?.provider),
      isMobile: window?.innerWidth < 441,
      currency: 'USD',
      isDemo: isDemoMode ? 'true' : 'false',
    },
    enabled: true,
  });

  const gameSrc = useMemo(() => {
    return !isAuthenticated
      ? gameDetails?.demoAvailable
        ? gameDetails?.gameUrl
        : `${process.env.NEXT_PUBLIC_CDN_BASE_URL}/assets/videos/Red-Curtain-Loop.mp4`
      : gameDetails?.gameUrl || '';
  }, [isAuthenticated, gameDetails?.demoAvailable, gameDetails?.gameUrl]);

  const { mutate: updateFavorite } = useUpdateFavoriteMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['GET_FAVORITES_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_CUSTOM_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_SUB_CATEGORY_GAMES'] });
      queryClient.invalidateQueries({
        queryKey: ['GET_SUB_CATEGORY_LOBBY_GAMES'],
      });
      queryClient.invalidateQueries({ queryKey: ['GET_CATEGORY_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_RECOMMENDATION_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_ACTIVE_PLAYER_PUBLIC'] });
      queryClient.invalidateQueries({
        queryKey: ['GET_ACTIVE_PLAYER_MY_FRIENDS'],
      });
    },
    onError: (error) => {
      setFavorite(!favorite);
      console.error('Error updating favorite', error);
    },
  });

  useEffect(() => {
    if (isAuthenticated) {
      setShowPopup(false);
      setIsDemoMode(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    setFavorite(gameDetails?.isFavorite || false);
  }, [gameDetails?.isFavorite]);

  useEffect(() => {
    if (gameDetails && !isAuthenticated) {
      setShowPopup(true);
    }
  }, [gameDetails, isAuthenticated]);

  useEffect(() => {
    if (isOpen && gameSrc) {
      updateDraggableContent(
        <iframe
          src={gameSrc}
          className="h-[400px] w-full rounded-lg"
          allowFullScreen
        />,
        getTextValue(gameDetails?.gameName) || 'Game',
      );
    } else {
      setSelected('');
    }
  }, [gameSrc, isOpen, updateDraggableContent, gameDetails?.gameName]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement) {
        setSelected('');
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () =>
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);
  const { openMenu, openChat } = useGeneralStore();

  const handleDemoToggle = (isLeft) => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 1);
      setSelectedTab(1);
      openModal(<Auth />);
      return false;
    }
    setIsDemoMode(isLeft);
    return true;
  };

  const toggleFav = (e) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 1);
      setSelectedTab(1);
      openModal(<Auth />);
      return;
    }
    setFavorite(!favorite);
    updateFavorite({ request: !favorite, casinoGameId: gameDetails?.gameId });
  };

  const isVideoFile = (url) => {
    if (!url) return false;
    const videoExtensions = ['.mp4', '.webm'];
    return videoExtensions.some((ext) => url.toLowerCase().includes(ext));
  };

  const handleFullscreen = () => {
    if (iframeRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        iframeRef.current.requestFullscreen();
      }
    }
  };

  const handlePopout = () => {
    openDraggablePopup(
      <iframe
        src={gameSrc}
        className="h-[400px] w-full rounded-lg"
        allowFullScreen
      />,
      getTextValue(gameDetails?.gameName) || 'Game',
    );
  };

  const handleClick = (name, action) => {
    setSelected((prev) => (prev === name ? '' : name)); // toggle
    action?.();
  };

  const memoizedIframe = useMemo(
    () => (
      <iframe
        ref={iframeRef}
        className="h-[calc(100dvh-180px)] w-full"
        src={gameSrc}
        frameBorder="0"
        allowFullScreen
      />
    ),
    [gameSrc],
  );

  return (
    <div
      className={`relative w-full ${
        openMenu && openChat ? '' : openMenu ? 'pl-0' : openChat ? 'pl-0' : ''
      }`}
    >
      {isLoading ? (
        <div className="flex h-[calc(100dvh-180px)] items-center justify-center">
          <MainLoader className="w-32" />
        </div>
      ) : (
        <>
          {showPopup && (
            <GamePopup
              gameDetails={gameDetails}
              demoAvailable={gameDetails?.demoAvailable}
              onClose={() => setShowPopup(false)}
            />
          )}
          {isVideoFile(gameSrc) ? (
            <video
              className="h-[calc(100dvh-180px)] w-full object-cover"
              src={gameSrc}
              autoPlay
              loop
              muted
            >
              Your browser does not support the video tag.
            </video>
          ) : isOpen ? (
            <div className="flex h-[calc(100dvh-180px)] w-full items-center justify-center object-cover">
              <h1>Pop Out Active.</h1>
            </div>
          ) : (
            memoizedIframe
          )}

          <div className="mb-5 flex items-center justify-between rounded-bl-2xl rounded-br-2xl border-t border-primaryBorder bg-slateGray-700 px-5 py-3 max-sm:py-1.5">
            <div className="flex items-center gap-4">
              {[
                {
                  key: 'fullscreen',
                  icon: FocusIcon,
                  onClick: handleFullscreen,
                  tooltip: 'Enable or disable fullscreen mode',
                  position: 'right',
                },
                {
                  key: 'popout',
                  icon: ExpandIcon,
                  onClick: handlePopout,
                  tooltip: 'Pop Out',
                  position: 'top',
                },
                {
                  key: 'theatre',
                  icon: (props) => (
                    <div className="group h-6 w-6">
                      <PictureInPicture className="stroke:!gray-400 h-full w-full group-hover:stroke-golden-600" />
                    </div>
                  ),
                  onClick: toggleTheatre,
                  tooltip: 'Enable or disable theatre mode',
                  position: 'top',
                },
                {
                  key: 'graph',
                  icon: GraphIcon,
                  onClick: () => handleClick('graph'),
                  tooltip: 'Graph',
                  position: 'top',
                },
              ]

                .filter(
                  ({ key }) =>
                    !(isMobile && (key === 'popout' || key === 'theatre')),
                )
                .map(({ key, icon: Icon, onClick, tooltip, position }) => {
                  const isActive = hovered === key || selected === key;
                  const showTooltip = hovered === key;

                  const startPress = () => {
                    isLongPressRef.current[key] = false;
                    longPressTimeouts.current[key] = setTimeout(() => {
                      // Long press detected
                      setHovered(key); // show tooltip
                      isLongPressRef.current[key] = true;

                      // auto-hide tooltip after 3s
                      tooltipHideTimeouts.current[key] = setTimeout(() => {
                        setHovered(null);
                        isLongPressRef.current[key] = false;
                      }, 3000);
                    }, 500); // 0.5s threshold for long press
                  };

                  const endPress = () => {
                    clearTimeout(longPressTimeouts.current[key]);

                    if (!isLongPressRef.current[key]) {
                      // short press → trigger click
                      onClick && onClick();
                    }

                    // reset
                    clearTimeout(tooltipHideTimeouts.current[key]);
                    isLongPressRef.current[key] = false;
                  };

                  return (
                    <div key={key} className="group relative flex items-center">
                      {showTooltip && (
                        <Tooltip text={tooltip} position={position} />
                      )}
                      <button
                        onMouseEnter={() => !isMobile && setHovered(key)}
                        onMouseLeave={() => !isMobile && setHovered(null)}
                        onMouseDown={() => !isMobile && startPress()}
                        onMouseUp={() => !isMobile && endPress()}
                        onTouchStart={startPress}
                        onTouchEnd={endPress}
                      >
                        <Icon
                          className={`h-6 w-6 transition-colors ${
                            isActive
                              ? '[&_*]:fill-golden-600'
                              : '[&_*]:fill-gray-400'
                          }`}
                        />
                      </button>
                    </div>
                  );
                })}
            </div>

            <div className="flex items-center gap-5">
              {gameDetails?.demoAvailable && (
                <div className="rounded-xl bg-inputBgColor px-2.5 py-2 max-sm:rounded-md max-sm:px-1 max-sm:py-1">
                  <SwitchButton
                    showLeftLabel={false}
                    onKnobColor="bg-white-400"
                    offKnobColor="bg-white-400"
                    onBgColor="bg-transparent"
                    offBgColor="bg-transparent"
                    switchWidth="w-[2.125rem]"
                    switchHeight="h-5"
                    activeColor="!text-steelTeal-200"
                    inactiveColor="!text-steelTeal-200"
                    knobSize="h-[.875rem] w-[.875rem]"
                    wrapperClassName="!justify-start"
                    useDynamicRightLabel={true}
                    leftToggleText="Fun"
                    rightToggleText="Real"
                    // labelTextSize="!text-sm"
                    onToggle={handleDemoToggle}
                    defaultLeft={isDemoMode}
                  />
                </div>
              )}

              <button
                type="button"
                onClick={toggleFav}
                className="group flex h-6 w-6 items-center justify-center rounded-lg transition-all duration-300 active:scale-90"
              >
                {favorite ? (
                  <HeartFillIcon className="h-4 w-4 fill-primary-1000" />
                ) : (
                  <>
                    <HeartStrokeIcon className="h-4 w-4 fill-primary-1000 group-hover:hidden" />
                    <HeartFillIcon className="hidden h-4 w-4 fill-primary-1000 lg:group-hover:block" />
                  </>
                )}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

function GamePage() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const accessToken = getAccessToken();
  const [isDemoMode, setIsDemoMode] = useState(!isAuthenticated);
  const params = useParams();
  const gameName = unSlugify(params?.name);
  const gameProvider = unSlugify(params?.provider);

  const { setGreenBonusData, setTotalBetAmountTill } = useGreenBonusStore(
    (state) => ({
      setGreenBonusData: state.setGreenBonusData,
      setTotalBetAmountTill: state.setTotalBetAmountTill,
    }),
  );

  const { data: gameDetails, isLoading } = useGameLaunchQuery({
    params: {
      gameName,
      gameProvider,
      isMobile: window?.innerWidth < 441,
      currency: 'USD',
      isDemo: isDemoMode ? 'true' : 'false',
    },
    enabled: true,
  });

  useEffect(() => {
    if (isAuthenticated) {
      setIsDemoMode(false);
    }
  }, [isAuthenticated]);

  const [showConfetti, setShowConfetti] = useState(false);

  const createConfetti = () => {
    const colors = ['#ff0', '#0f0', '#00f', '#f00', '#ff69b4'];
    const confettiCount = 100;

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement('div');
      confetti.className = 'confetti';
      confetti.style.position = 'fixed';
      confetti.style.top = '-10px';
      confetti.style.left = `${Math.random() * 100}vw`;
      confetti.style.width = `${Math.random() * 10 + 5}px`;
      confetti.style.height = `${Math.random() * 10 + 5}px`;
      confetti.style.backgroundColor =
        colors[Math.floor(Math.random() * colors.length)];
      confetti.style.opacity = '0.8';
      confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
      confetti.style.zIndex = '1000';

      confetti.animate(
        [
          { transform: `translateY(0) rotate(${Math.random() * 360}deg)` },
          {
            transform: `translateY(${window.innerHeight + 10}px) rotate(${Math.random() * 360}deg)`,
          },
        ],
        {
          duration: Math.random() * 2000 + 2000,
          easing: 'linear',
          fill: 'forwards',
        },
      );

      document.body.appendChild(confetti);

      setTimeout(() => {
        confetti.remove();
      }, 4000);
    }
  };

  const isClaimGreenBonusCalled = () => {
    const { betAmountToClaimBonus } = useGreenBonusStore.getState();
    setTotalBetAmountTill({
      totalBetAmountTill: betAmountToClaimBonus,
    });

    setShowConfetti(true);
    createConfetti();
    setTimeout(() => {
      setShowConfetti(false);
    }, 5000);
  };

  useEffect(() => {
    if (isAuthenticated) {
      walletSocket.auth = { token: accessToken };
      walletSocket.connect();

      const handleClaimGreenBonus = () => isClaimGreenBonusCalled();

      walletSocket.on('CLAIM_GREEN_BONUS', handleClaimGreenBonus);

      return () => {
        walletSocket.off('CLAIM_GREEN_BONUS');
        walletSocket.disconnect();
      };
    }
  }, [isAuthenticated, accessToken, isClaimGreenBonusCalled]);

  useEffect(() => {
    if (gameDetails?.isGreenBonusApplicable) {
      setGreenBonusData(gameDetails);
    }
  }, [gameDetails, setGreenBonusData]);
  const { openMenu, openChat } = useGeneralStore();
  return (
    <div
      className={` ${
        openMenu && openChat
          ? ''
          : openMenu
            ? 'ml-0 2xl:pl-[3.5625rem]'
            : openChat
              ? ''
              : ''
      }`}
    >
      <GamePlayerWithControls
        isAuthenticated={isAuthenticated}
        gameName={gameName}
        gameProvider={gameProvider}
      />
      <GameDetailsAndProviderGames
        isAuthenticated={isAuthenticated}
        gameName={gameName}
        gameProvider={gameProvider}
      />
    </div>
  );
}

export default GamePage;
