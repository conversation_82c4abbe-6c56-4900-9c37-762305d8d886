/* eslint-disable no-nested-ternary */
'use client';

import ComingSoon from '@/components/ComingSoon';
import MainLoader from '@/components/Common/Loader/MainLoader';
import { useSportsbookLaunchQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';

// export const dynamic = 'force-dynamic';
function SportsbookPage() {
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);

  const { data, isLoading } = useSportsbookLaunchQuery({
    params: {
      isDemo: !isAuthenticated,
      ...(userDetails?.id && { userId: String(userDetails.id) }),
      ...(isAuthenticated && { fiatCurrency: 'EUR' }),
    },
  });

  return isLoading ? (
    <div className="flex h-96 items-center justify-center">
      {/* <MainLoader className="w-32" /> */}
    </div>
  ) : data ? (
    <div className="relative w-full">
      {/* Sportsbook Iframe */}
      <iframe
        title="sportsbook"
        className=" w-full lg:mt-[-32px] lg:aspect-[1031/800]  lg:h-[calc(100vh-60px)]  max-lg:h-[calc(100vh-115px)] "
        src={data?.gameUrl}
        frameBorder="0"
      />
    </div>
  ) : (
    <div className="flex h-96 items-center justify-center">
      Something went wrong
    </div>
  );
}

export default SportsbookPage;
