'use client';

import { useEffect } from 'react';
// import GroupBanner from '../GroupBanner';
import TextTabs from '@/components/Common/TextTabs';

import About from '@/components/Groups/About';
import GroupBanner from '@/components/Groups/GroupBanner';
import GroupChat from '@/components/Groups/GroupChat';
import Members from '@/components/Groups/Members';
import useGroupChatWindow from '@/hooks/useGroupChatWindow';
import { useGetPublicDetailQuery } from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import { useParams, useRouter } from 'next/navigation';

function Group() {
  const params = useParams();
  const groupName = params.groupname?.split('-').join(' ');
  const {
    data: groupData,
    refetch,
    isPending,
  } = useGetPublicDetailQuery({
    enabled: true,
    groupName,
  });
  console.log('🚀 ~ Group ~ groupData:', groupData);
  const userDetails = useAuthStore((state) => state.userDetails);
  const router = useRouter();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  useGroupChatWindow({ isGroupPage: true });
  const tabs = [
    {
      label: 'About',
      content: (
        <About
          groupData={groupData}
          userDetails={userDetails}
          refetch={refetch}
        />
      ),
    },
    {
      label: 'Chat',
      content: (
        <GroupChat
          groupData={groupData}
          userDetails={userDetails}
          refetch={refetch}
        />
      ),
    },
    {
      label: 'Members',
      content: (
        <Members
          groupData={groupData}
          userDetails={userDetails}
          refetch={refetch}
        />
      ),
    },
  ];
  useEffect(() => {
    if (isAuthenticated) {
      refetch();
    }
  }, [isAuthenticated, refetch]);
  if (!isPending && Object.keys(groupData?.group || {}).length == 0) {
    return (
      <div className="flex h-[90vh] w-full flex-col items-center justify-center text-center">
        <h1 className="text-2xl font-bold text-gray-200 md:text-3xl">
          Group Not Found
        </h1>
        <p className="mt-2 max-w-md text-sm text-gray-400 md:text-base">
          The group you are looking for doesn’t exist or may have been removed.
        </p>
        <button
          onClick={() => router.push('/')} // replace with your routing
          className="text-black mt-6 rounded-lg 
             bg-TintGoldGradient px-6 py-2 text-sm font-medium text-black-1000 
             shadow-lg hover:from-yellow-500 hover:via-yellow-600
             hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2"
        >
          Go Back Home
        </button>
      </div>
    );
  }
  return (
    <div className="p-[0.625rem]">
      <GroupBanner groupData={groupData?.group} refetch={refetch} />
      <TextTabs
        tabs={tabs}
        onChange={(index) => console.log('Active tab index:', index)}
      />
    </div>
  );
}

export default Group;
