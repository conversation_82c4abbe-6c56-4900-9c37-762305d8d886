'use client';

import TextTabs from '@/components/Common/TextTabs';

import { useGetGroupDetailQuery } from '@/reactQuery/chatWindowQuery';
import { useParams, useRouter } from 'next/navigation';
// import GroupBanner from '@/app/group/GroupBanner';
import About from '@/components/Groups/About';
import GroupBanner from '@/components/Groups/GroupBanner';
import GroupChat from '@/components/Groups/GroupChat';
import Members from '@/components/Groups/Members';
import useAuthStore from '@/store/useAuthStore';

function PrivateGroup() {
  const params = useParams();

  const groupId = params.privategroup?.split('-')[0];
  const groupName = params.privategroup?.split('-').slice(1).join(' ');
  const { isAuthenticated } = useAuthStore((state) => state);
  const userDetails = useAuthStore((state) => state.userDetails);
  const router = useRouter();
  const {
    data: groupData,
    isLoading,
    isPending,
  } = useGetGroupDetailQuery({
    groupName,
    groupId,
    enabled: isAuthenticated,
  });
  console.log('🚀 ~ PrivateGroup ~ groupData:', groupData);

  const tabs = [
    {
      label: 'About',
      content: <About groupData={groupData} userDetails={userDetails} />,
    },
    {
      label: 'Chat',
      content: <GroupChat groupData={groupData} userDetails={userDetails} />,
    },
    {
      label: 'Members',
      content: <Members groupData={groupData} userDetails={userDetails} />,
    },
  ];
  if (!isPending && Object.keys(groupData || {}).length == 0) {
    return (
      <div className="flex h-[90vh] w-full flex-col items-center justify-center text-center">
        <h1 className="text-2xl font-bold text-gray-200 md:text-3xl">
          Group Not Found
        </h1>
        <p className="mt-2 max-w-md text-sm text-gray-400 md:text-base">
          The group you are looking for doesn’t exist or may have been removed.
        </p>
        <button
          onClick={() => router.push('/')} // replace with your routing
          className="mt-6 rounded-lg bg-TintGoldGradient 
             px-6 py-2 text-sm font-medium text-black shadow-lg 
             hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700
             focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 text-black-1000">
          Go Back Home
        </button>
      </div>
    );
  }
  return (
    <div className="p-[0.625rem]">
      <GroupBanner groupData={groupData} isLoading={isLoading} />
      <TextTabs
        tabs={tabs}
        onChange={(index) => console.log('Active tab index:', index)}
      />
    </div>
  );
}

export default PrivateGroup;
