import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import {
  acceptDeclineGroupRequest,
  cancelFriendRequest,
  createFriendRequest,
  createGroup,
  getAgoraToken,
  getFriends,
  getFriendsRequest,
  getGroupChats,
  getGroupDetail,
  getGroupDetails,
  getGroupJoinRequestList,
  getGroupList,
  getIncomingMessages,
  getPrivateChat,
  getPublicChats,
  getPublicGroupList,
  getRecentChat,
  getUsersList,
  getUserTags,
  joinGroup,
  sendDeclinedReq,
  sendGroupJoinInviation,
  sendPublicChatsMsg,
  sendTagMsg,
  unFriendRequest,
  updateFriendRequest,
  updateGroup,
  updateJoinedGroup,
  updateMsgRequests,
} from '@/utils/apiCalls';

export const useGetPublicChatsQuery = ({ isAuthenticated }) =>
  useInfiniteQuery({
    queryKey: ['GET_PublicChatsQuery', isAuthenticated],
    queryFn: ({ pageParam = 1 }) => getPublicChats({ pageParam }),
    getNextPageParam: (lastPage, allPages) => {
      const morePagesExist = lastPage?.data?.count;
      if (!morePagesExist) return false;
      return allPages.length + 1;
    },
    select: (data) => ({
      pages: data.pages.flatMap((page) => page.data.rows),
      livePlayersCount: data.pages[0]?.data.livePlayersCount,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useSendPublicMsgMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_PUBLIC_MSG'],
    mutationFn: (data) => sendPublicChatsMsg(data),
    onSuccess,
    onError,
  });

export const useGetGroupDetailQuery = ({ groupName, groupId, enabled = false }) =>
  useQuery({
    queryKey: ['GET_GROUP_DETAIL_QUERY', groupName, groupId],
    queryFn: () =>
      groupId ? getGroupDetail({ groupName, groupId }) : getGroupDetail({ groupName }),
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetPublicDetailQuery = ({
  groupId,
  groupName,
  enabled = true,
}) =>
  useQuery({
    queryKey: ['GET_PUBLIC_GROUP_DETAIL_QUERY', { groupId, groupName }],
    queryFn: () =>
      getPublicGroupList({
        ...(groupId ? { groupId } : {}),
        ...(groupName ? { groupName } : {}),
      }),
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled: enabled && (!!groupId || !!groupName),
  });


export const useGetPrivateChatsQuery = ({ enabled, receiverId }) =>
  useQuery({
    queryKey: ['GET_PRIVATE_CHAT_QUERY', receiverId],
    queryFn: () => getPrivateChat({ receiverId }),
    select: (data) => data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetGroupListQuery = ({ enabled, search }) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_LIST_QUERY', search],
    queryFn: async ({ pageParam = 1 }) => {
      const params = { search, page: pageParam, limit: 20 };
      return getGroupList(params);
    },
    getNextPageParam: (lastPage, allPages) => {
      // Check if there are groups in the last page
      const lastPageGroups = lastPage?.data?.groups || [];

      // If the last page has no groups, don't request more pages
      if (lastPageGroups.length === 0) {
        return undefined;
      }

      const currentCount = allPages.reduce(
        (acc, page) => acc + (page?.data?.groups?.length || 0),
        0,
      );
      const totalCount = lastPage?.data?.count || 0;

      // Strict comparison to ensure we don't continue fetching if we've reached the total
      if (currentCount >= totalCount) {
        return undefined;
      }

      return allPages.length + 1;
    },
    select: (data) => ({
      groups: data.pages.flatMap((page) => page?.data?.groups || []),
      total: data.pages?.[0]?.data?.count || 0,
    }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled,
  });
export const useGetGroupRequestListQuery = ({
  enabled,
  search,
  groupName,
  invitationType,
}) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_REQUEST_LIST_QUERY', search, groupName, invitationType],
    queryFn: async ({ pageParam = 1 }) => {
      const params = {
        search,
        page: pageParam,
        limit: 20,
        groupName,
        invitationType,
      };
      return getGroupJoinRequestList(params);
    },
    getNextPageParam: (lastPage, allPages) => {
      const lastPageCount = lastPage?.data?.data?.rows?.length || 0;
      const totalCount = lastPage?.data?.data?.count || 0;
      const currentCount = allPages.reduce(
        (acc, page) => acc + (page?.data?.data?.rows?.length || 0),
        0
      );

      return lastPageCount > 0 && currentCount < totalCount
        ? allPages.length + 1
        : undefined;
    },
    select: (data) => ({
      groups: data.pages.flatMap((page) => page?.data?.data?.rows || []),
      total: data.pages?.[0]?.data?.data?.count || 0,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetPublicGroupListQuery = ({
  enabled,
  search,
  groupName,
  id,
  groupType,
}) =>
  useInfiniteQuery({
    queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY', search, groupName],
    queryFn: async ({ pageParam = 1 }) => {
      const params = { search, page: pageParam, limit: 20, groupName, groupId: id };
      return getPublicGroupList(params);
    },
    getNextPageParam: (lastPage, allPages) => {
      const lastPageCount = lastPage?.data?.groups?.length || 0;
      const totalCount = lastPage?.data?.count || 0;
      const currentCount = allPages.reduce(
        (acc, page) => acc + (page?.data?.groups?.length || 0),
        0,
      );

      // Stop fetching if the total count is less than the limit (means all items are on the first page)
      if (totalCount <= 20) return undefined;

      // Stop fetching if lastPage has no data or all items are fetched
      return lastPageCount > 0 && currentCount < totalCount
        ? allPages.length + 1
        : undefined;
    },
    select: (data) => ({
      groups: id ? data.pages : data.pages.flatMap((page) => page?.data?.groups || []),
      total: data.pages?.[0]?.data?.count || 0,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGroupChatMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_CREATE'],
    mutationFn: (data) => createGroup(data),
    onSuccess,
    onError,
  });

export const useGetUserTagsQuery = ({ enabled, params }) =>
  useQuery({
    queryKey: ['GET_USER_TAGS_QUERY', params?.search],
    queryFn: () => getUserTags(params),
    select: (data) => data?.data?.rows,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetRecentChatsQuery = ({ params }) =>
  useQuery({
    queryKey: ['GET_RECENT_CHAT_QUERY', params.search],
    queryFn: () => getRecentChat(params),
    select: (data) => data?.data?.chatDetails,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!params.search,
  });
export const useJoinGroupSendMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_PRIVATE_GROUP_INVITATION'],
    mutationFn: (data) => sendGroupJoinInviation(data),
    onSuccess,
    onError,
  });

export const useJoinedGroupMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['jOINED_GROUP'],
    mutationFn: (data) => updateJoinedGroup(data),
    onSuccess,
    onError,
  });

export const useGetGroupDetails = ({ enabled, params }) => {
  return useQuery({
    queryKey: ['GET_GROUP_DETAILS_QUERY', params?.groupId],
    queryFn: () => getGroupDetails(params?.groupId),
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGetGroupChatsQuery = ({ groupId, enabled }) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_CHATS_QUERY', groupId],
    queryFn: ({ pageParam = 1 }) => getGroupChats({ pageParam, groupId }),
    getNextPageParam: (lastPage, allPages) => {
      const totalCount = lastPage?.data?.count || 0;
      const alreadyFetched = allPages.reduce(
        (acc, page) => acc + page.data.rows.length,
        0
      );

      // If we already fetched all rows, return undefined (no next page)
      if (alreadyFetched >= totalCount) return undefined;

      return allPages.length + 1; // next page number
    },
    select: (data) => ({
      pages: data.pages.flatMap((page) => page.data.rows),
      livePlayersCount: data.pages[0]?.data.livePlayersCount,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });


export const useGroupChatUpdateDetailsMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_UPDATE'],
    mutationFn: (data) => updateGroup(data),
    onSuccess,
    onError,
  });

export const useGetFriendsListQuery = ({ enabled = false, params }) =>
  useQuery({
    queryKey: ['GET_FRIEND_LIST_QUERY', params?.search],
    queryFn: () => getFriends(params),
    select: (data) => data?.data?.myFriends,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
    staleTime: 0,
  });

export const useGetFriendsRequestListQuery = ({ params, enabled = true, type = '' }) =>
  useQuery({
    queryKey: ['GET_FRIENDS_REQUEST_LIST', type, params?.search],
    queryFn: () => getFriendsRequest({ type, ...params }),
    select: (data) => data?.data?.allFriendRequests,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const userListQuery = ({ params, enabled }) =>
  useInfiniteQuery({
    queryKey: ['GET_USER_LIST_QUERY', params?.search],
    queryFn: ({ pageParam = 1 }) => getUsersList({ page: pageParam, ...params }),
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage?.data?.allUsers) {
        return undefined;
      }
      const { rows, count } = lastPage.data.allUsers;
      const totalLoadedItems = allPages.reduce((total, page) => {
        return total + (page?.data?.allUsers?.rows?.length || 0);
      }, 0);
      if (totalLoadedItems >= count) {
        return undefined;
      }
      if (!rows || rows.length === 0) {
        return undefined;
      }
      return allPages.length + 1;
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetIncomingMessagesQuery = ({ params, enabled = true }) =>
  useInfiniteQuery({
    queryKey: ['GET_INCOMING_MESSAGE_LIST', params?.search],
    queryFn: ({ pageParam = 1 }) => getIncomingMessages({ page: pageParam, ...params }),
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage?.data?.messageRequests) {
        return undefined;
      }
      const { messageRequests, count } = lastPage.data;
      const totalLoadedItems = allPages.reduce((total, page) => {
        return total + (page?.data?.messageRequests?.length || 0);
      }, 0);
      if (totalLoadedItems >= count) {
        return undefined;
      }
      if (!messageRequests || messageRequests.length === 0) {
        return undefined;
      }
      return allPages.length + 1;
    },
    select: (data) => ({
      pages: data?.pages?.flatMap((page) => page?.data?.messageRequests || []),
      count: data?.pages?.[0]?.data?.count || 0,
      unreadCount: data?.pages?.[0]?.data?.unreadCount || 0
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useCreateFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['CREATE_FRIENDS_REQUEST'],
    mutationFn: (data) => createFriendRequest(data),
    onSuccess,
    onError,
  });

export const useUpdateFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_FRIENDS_REQUEST'],
    mutationFn: (data) => updateFriendRequest(data),
    onSuccess,
    onError,
  });

export const useDeclinedCall = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['cancelled_call'],
    mutationFn: (data) => sendDeclinedReq(data),
    onSuccess,
    onError,
  });

export const useGenerateAgoraToken = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['get_agora_token'],
    mutationFn: (data) => getAgoraToken(data),
    onSuccess,
    onError,
  });

export const useUnFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UN_FRIENDS_REQUEST'],
    mutationFn: (data) => unFriendRequest(data),
    onSuccess,
    onError,
  });


export const useCancelFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['CANCEL_FRIENDS_REQUEST'],
    mutationFn: (data) => cancelFriendRequest(data),
    onSuccess,
    onError,
  });

export const useSendTagMsgMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_TAG_MSG'],
    mutationFn: (data) => sendTagMsg(data),
    onSuccess,
    onError,
  });

export const useJoinGroup = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['USE_JOIN_GROUP'],
    mutationFn: (data) => joinGroup(data),
    onSuccess,
    onError,
  });
export const useAcceptDeclineGroupJonRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['USE_ACCEPT_DECLINE_JOIN_GROUP_REQUEST'],
    mutationFn: (data) => acceptDeclineGroupRequest(data),
    onSuccess,
    onError,
  });

export const useUpdateMsgRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_MSG_REQUEST'],
    mutationFn: (data) => updateMsgRequests(data),
    onSuccess,
    onError,
  });

