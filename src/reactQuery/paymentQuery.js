import { useQuery, useMutation, useInfiniteQuery } from "@tanstack/react-query";
import { fetchCurrencies, fetchGateways, getUserDepositTransactions, paymentDeposit } from "@/utils/apiCalls";
import { useGatewayStore } from "@/store/useGatewayStore";

export const useFetchGatewaysQuery = (params, { enabled = true } = {}) => {
  const { gateways, setGateways } = useGatewayStore();

  return useQuery({
    queryKey: ["FETCH_GATEWAYS", params],
    queryFn: async () => {
      if (gateways && gateways?.length > 0) {
        return gateways;
      }

      const res = await fetchGateways(params);
      const options = res?.data?.data?.gateway_options || [];

      const transformed = options.map((item) => ({
        ...item,
        value: item.payment_method,
        label: item.payment_method,
      }));

      setGateways(transformed);
      return transformed;
    },
    enabled,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    cacheTime: Infinity,
  });
};

export const usePaymentMutation = ({ onSuccess, onError } = {}) =>
  useMutation({
    mutationKey: ["PAYMENT_DEPOSIT"],
    mutationFn: paymentDeposit,
    onSuccess,
    onError,
  });


export const useFetchCurrenciesQuery = (params, { enabled = true } = {}) => {
  const { currencies, setCurrencies } = useGatewayStore();

  return useQuery({
    queryKey: ["FETCH_CURRENCIES", params],
    queryFn: async () => {
      if (currencies && currencies?.length > 0) {
        return currencies;
      }

      const res = await fetchCurrencies(params);
      const options = res?.data?.currencies?.rows || [];

      const transformed = options.map((item) => ({
        ...item,
        value: item.id,
        label: item.name,
      }));

      setCurrencies(transformed);
      return transformed;
    },
    enabled,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    cacheTime: Infinity,
  });
};

export const useTransactionsQuery = ({ pageNo, pageSize = 14, enabled = true }) =>
  useQuery({
    queryKey: ['transactions', pageNo],
    queryFn: () => getUserDepositTransactions({ pageNo, pageSize }),
    select: (data) => {
      const rows = data?.data?.transactions?.rows || [];
      const count = data?.data?.transactions?.count || 0;
      return { rows, count };
    },
    enabled,
    keepPreviousData: true, 
    refetchOnWindowFocus: false,
  });
