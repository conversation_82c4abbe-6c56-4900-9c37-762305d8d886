import IconButton from '@/components/Common/Button/IconButton';
import Tooltip from '@/components/Common/Tooltip';
import useGroupCall, { rtc } from '@/hooks/useGroupCall';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useCallStore from '@/store/useCallStore';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { Mic, MicOff } from 'lucide-react';
import { useEffect, useState } from 'react';

const ToggleMuteButton = () => {
  const { toggleMicrophone } = useGroupCall();
  const isMuted = useActiveGroupStore((state) => state.isMuted);
  const setIsMuted = useActiveGroupStore((state) => state.setIsMuted);
  //   const [isMuted, setIsMuted] = useState(false);
  const { toggleMuted, setToggleMuted } = useCallStore();

  // useEffect(() => {
  //   setIsMuted(toggleMuted);
  // }, []);
  return (
    <Tooltip text={isMuted ? 'Unmute' : 'mute'}>
      <IconButton onClick={toggleMicrophone} className="h-6 w-6">
        {isMuted ? (
          <MicOff className="h-5 w-5 text-red-500" />
        ) : (
          <Mic className="text-white h-5 w-5" />
        )}
      </IconButton>
    </Tooltip>
  );
};

export default ToggleMuteButton;
