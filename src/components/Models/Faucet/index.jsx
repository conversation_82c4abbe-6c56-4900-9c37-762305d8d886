'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import ReCAPTCHA from 'react-google-recaptcha';
import {
  loadCaptchaEnginge,
  LoadCanvasTemplate,
  validateCaptcha,
} from 'react-simple-captcha';
import toast from 'react-hot-toast';
import FaucetIcon from '@/assets/icons/Faucet';
import { useClaimFausetBonusMutation } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import CloseIcon from '@/assets/icons/CloseIcon';
import useModalStore from '@/store/useModalStore';
import IconButton from '../../Common/Button/IconButton';
import coinAC from '../../../assets/images/stock-images/coin-ac.png';
import PrimaryButton from '../../Common/Button/PrimaryButton';

function Faucet() {
  const [coin, setCoin] = useState('AC');
  const [captchaInput, setCaptchaInput] = useState('');
  const [captchaError, setCaptchaError] = useState('');
  const [googleRecaptcha, setGoogleRecaptcha] = useState(null);
  const [isButtonDisable, setIsButtonDisable] = useState(true);
  const [isInputDisable, setIsInputDisable] = useState(false);
  const { closeModal } = useModalStore((state) => state);
  const { userWallet, setUserWallet } = useAuthStore((state) => state);

  useEffect(() => {
    loadCaptchaEnginge(4, 'rgb(75 85 99)', 'white');
  }, []);

  const toggleSwitch = () => {
    setCoin('AC');
  };

  const mutation = useClaimFausetBonusMutation({
    onSuccess: (res) => {
      setUserWallet({
        ...res?.data?.userWallet,
        acCoin:
          res.data.userWallet.wsc +
          res.data.userWallet.psc +
          res.data.userWallet.bsc,
      });
      toast.success(res?.data?.message || 'Bonus claimed successfully.');
      closeModal();
    },
    onError: (error) => {
      console.log('errr', error);
      toast.error(
        error?.response?.data?.errors?.[0]?.description ||
          'Please try again after some time',
      );
      resetCaptcha();
    },
  });

  const spring = {
    type: 'spring',
    stiffness: 700,
    damping: 30,
  };

  const resetCaptcha = () => {
    loadCaptchaEnginge(4, 'rgb(75 85 99)', 'white');
    setCaptchaInput('');
    setCaptchaError('');
    setIsButtonDisable(true);
    setIsInputDisable(false);
  };

  const handleClaim = () => {
    if (validateCaptcha(captchaInput, false)) {
      setCaptchaError('');
      mutation.mutate({
        isGc: false,
        captchaValue: googleRecaptcha,
      });
      setIsButtonDisable(true);
      setIsInputDisable(true);
    } else {
      setCaptchaError('Captcha did not match. Please try again.');
      toast.error('Captcha did not match. Please try again.');
      resetCaptcha();
    }
  };

  useEffect(() => {
    if (validateCaptcha(captchaInput, false) === true && googleRecaptcha) {
      setIsButtonDisable(false);
      setCaptchaError('');
    } else {
      setIsButtonDisable(true);
    }
  }, [captchaInput, googleRecaptcha]);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <FaucetIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                Faucet ( Unlimited )
              </h3>
            </div>

            <IconButton className="h-6 w-6 min-w-6" onClick={closeModal}>
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="flex flex-col items-center gap-3 px-5 py-6">
            <div className="flex items-center max-md:flex-col">
              <div
                className="switch flex cursor-pointer justify-between"
                data-ison={coin === 'AC'}
                onClick={toggleSwitch}
              >
                <motion.div layout transition={spring}>
                  <button
                    type="button"
                    className={`flex items-center justify-center gap-2 rounded-md border-2 border-solid px-1.5 py-1 text-xs font-black text-white-500 md:gap-3 md:px-2.5 md:py-1.5 md:text-base ${coin === 'AC' ? 'border-green-1000 bg-green-1000' : 'border-orange-1000 bg-orange-1000'}`}
                  >
                    <span className="d-block h-4 w-4 md:h-6 md:w-6">
                      <Image
                        src={coinAC}
                        width={10000}
                        height={10000}
                        className={`w-[1rem] max-w-full md:w-[1.875rem] ${coin === 'AC' ? 'coin-shadow' : 'coin-shadow'}`}
                        alt="Coin"
                      />
                    </span>
                    <span>{'AC'}</span>
                  </button>
                </motion.div>
              </div>
            </div>
            <div className="flex">
              <input
                type="text"
                className="text-white mr-3 h-[48px] w-[183px] rounded-md border border-solid border-richBlack-1000 bg-richBlack-500 p-3 text-base font-normal focus:border-steelTeal-1000"
                value={captchaInput}
                disabled={isInputDisable}
                onChange={(e) => setCaptchaInput(e.target.value)}
              />
              <LoadCanvasTemplate reloadColor="white" />
            </div>
            {captchaError && (
              <p className="mt-2 text-sm text-red-500">{captchaError}</p>
            )}
            <ReCAPTCHA
              sitekey={process.env.NEXT_PUBLIC_GOOGLE_RECAPTCHA_SITE_KEY}
              theme="dark"
              onChange={(val) => setGoogleRecaptcha(val)}
            />
            <PrimaryButton
              className="w-[305px]"
              onClick={handleClaim}
              isLoading={mutation.isPending}
            >
              Claim
            </PrimaryButton>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Faucet;
