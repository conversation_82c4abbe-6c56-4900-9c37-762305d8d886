'use client';

import IconButton from '@/components/Common/Button/IconButton';
import useModalStore from '@/store/useModalStore';
import { UserRoundPlus, X, UserRoundMinus, Loader2, Users } from 'lucide-react';
import { useState, useEffect } from 'react';
import AddFriendsFromSearch from './AddFriendsFromSearch';
import defaultImage from '@/assets/icons/profile-icon.svg';
import Image from 'next/image';
import useDebounce from '@/utils/useDebounce';
import {
  useGetGroupDetails,
  useGetPublicDetailQuery,
  useJoinedGroupMutation,
} from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import UpdateGroupDetailsModal from '../UpdateGroupDetailsModal';
import useGroupChatStore from '@/store/useGroupChatStore';
import useAuthStore from '@/store/useAuthStore';
import Tooltip from '@/components/Common/Tooltip';
import Link from 'next/link';
import GroupNameModal from '@/components/ChatWindow/GroupNameModal';
import AddUserModal from '@/assets/icons/AddUserModal';
import InputField from '@/components/Common/InputField';
import { User } from 'lucide-react';
import ChatAvatar from '@/components/ChatAvatar';
import GroupDetailsModal from '@/components/ChatWindow/GroupDetailsModal';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useGeneralStore from '@/store/useGeneralStore';
import { userStatusColor } from '@/utils/helper';

const ShowGroupModal = ({ groupData }) => {
  const { groupId } = useGroupChatStore((state) => state);
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const { setOpenChat } = useGeneralStore();

  // const {
  //   data: groupDetails,
  //   refetch: refetchGroupDetails,
  //   loading,
  //   isLoading,
  // } = useGetGroupDetails({ params: { groupId }, enabled: !!isAuthenticated });

  // const { data: groupDetails } = useGetPublicDetailQuery({
  //   enabled: true,
  //   groupId,
  //   groupType: 'public',
  // });

  const isPrivate = groupData?.groupType === 'private';

  const {
    data: groupDetails,
    refetch: refetchGroupDetails,
    loading,
    isLoading,
  } = isPrivate
    ? useGetGroupDetails({ params: { groupId }, enabled: true })
    : useGetPublicDetailQuery({ enabled: true, groupId, groupType: 'public' });

  const [searchTerm, setSearchTerm] = useState('');
  const [filteredMembers, setFilteredMembers] = useState([]);

  const debouncedSearchTerm = useDebounce(searchTerm, 800); // Apply debounce with 800ms delay

  const { openModal, closeModal } = useModalStore();
  const queryClient = useQueryClient();

  const isMember = groupDetails?.group?.members?.some(
    (member) => member?.userId == userDetails?.id 
  );

  const navigateToGroupPage = () => {
    const groupName = groupDetails?.group.groupName;
    if (groupName) {
      closeModal();
    }
  };

  const openAddMemberModal = () => {
    openModal(
      <AddFriendsFromSearch
        groupId={groupId}
        groupMembers={groupDetails?.group?.members || []}
        isAdmin={userDetails?.userId === groupDetails?.group?.groupAdmin}
        groupDetails={groupDetails?.group}
      />,
      // <AddFriendsFromSearch  groupId={groupId} groupMembers={} />,
    );
  };
  const openGroupUpdateModal = () => {
    openModal(
      // <AddFriendsFromSearch
      //   groupId={groupId}
      //   groupMembers={groupDetails?.group?.members || []}
      //   isAdmin={userDetails?.userId === groupDetails?.group?.groupAdmin}
      // />,
      <UpdateGroupDetailsModal groupId={groupId} />,
    );
  };
  const { mutate: updateJoinedGroup, isLoading: isRemoving } =
    useJoinedGroupMutation({
      onSuccess: (response) => {
        toast.success('Successfully removed from group');

        queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
        closeModal();
      },
      onError: (error) => {
        const message =
          error.response?.data?.errors?.[0]?.description ||
          'Something went wrong';
        toast.error(message);
      },
    });

  // Initialize filteredMembers when groupDetails changes
  useEffect(() => {
    if (groupDetails?.group?.members) {
      setFilteredMembers(groupDetails.group.members);
    }
  }, [groupDetails]);

  useEffect(() => {
    if (!groupDetails?.group?.members) return;
    let members = [...groupDetails.group.members];
    if (debouncedSearchTerm) {
      members = members.filter((member) => {
        if (!member?.user) return false;

        const { firstName = '', lastName = '', username = '' } = member.user;
        return (
          firstName
            ?.toLowerCase()
            ?.includes(debouncedSearchTerm.toLowerCase()) ||
          lastName
            ?.toLowerCase()
            ?.includes(debouncedSearchTerm.toLowerCase()) ||
          username?.toLowerCase()?.includes(debouncedSearchTerm.toLowerCase())
        );
      });
    }

    members.sort((a, b) => {
      if (a.isAdmin === b.isAdmin) return 0;
      return a.isAdmin ? -1 : 1;
    });

    setFilteredMembers(members);
  }, [debouncedSearchTerm, groupDetails?.group?.members]);

  const handleRemoveMember = (userId) => {
    console.log(`Removing member with userId: ${userId}`);
    // Your logic to remove the member
    const payload = {
      groupId,
      action: 'remove',
      members: [userId],
    };

    updateJoinedGroup(payload);
  };

  const openEditGroupDetails = () => {
    openModal(
      <UpdateGroupDetailsModal
        groupId={groupId}
        groupMembers={groupDetails?.group?.members || []}
      />,
    );
  };

  const isAdmin = userDetails?.id == groupDetails?.group?.groupAdmin;
  const canUpdateGroupDetails =
    groupDetails?.group?.groupSettings?.onlyAdminCanUpdateGroupDetails ===
      false || isAdmin;
  console.log('!!!!!!!!!', groupDetails?.group);

  const canAddMembers =
    groupDetails?.group?.groupSettings?.onlyAdminCanAddMembers === false ||
    isAdmin;
  const isDataLoading = loading || isLoading;

  const groupUrlHref = (groupDetails) => {
    const groupName = encodeURIComponent(groupDetails?.group?.groupName)
      .replace(/%20/g, '-')
      .toLowerCase();
    if (groupDetails?.group?.groupType == 'private')
      return `/private-group/${groupDetails?.group?.uniqueCode}-${groupName}`;
    else {
      return `/group/${groupName}`;
    }
  };

  return (
    // <div
    //   tabIndex="-1"
    //   aria-hidden="true"
    //   className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    // >
    //   <div className="relative w-full max-w-xl p-4">
    //     <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
    //       <div className="flex items-center justify-between p-4">
    //         <h3 className="text-white mt-1 text-lg font-semibold">
    //           Members of {groupDetails?.group?.groupName || 'Group'}
    //         </h3>
    //         <Tooltip text="Details">groupMembers
    //             <Link href={groupUrlHref(groupDetails)}>
    //               <IconButton
    //                 type="button"
    //                 onClick={navigateToGroupPage}
    //                 className="h-6 w-6 min-w-6"
    //               >
    //                 <Users className="groupDetails?.group-hover:fill-white-1000 h-5 w-5 fill-steelTeal-1000 transition-all duration-300" />
    //               </IconButton>
    //             </Link>
    //           </Tooltip>
    //         {isDataLoading ? (
    //           <Loader2 className="h-8 w-8 animate-spin text-steelTeal-1000" />
    //         ) : (
    //           <div className="flex flex-row items-center gap-4">
    //             {canUpdateGroupDetails && (
    //               <span
    //                 onClick={openEditGroupDetails}
    //                 className="cursor-pointer"
    //               >
    //                 Update details
    //               </span>
    //             )}
    //             {canAddMembers && (
    //               <IconButton
    //                 onClick={openAddMemberModal}
    //                 className="h-6 w-6 min-w-6"
    //               >
    //                 <UserRoundPlus className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
    //               </IconButton>
    //             )}
    //             <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
    //               <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
    //             </IconButton>
    //           </div>
    //         )}
    //       </div>
    //       <div className="px-2 py-1">
    //         <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
    //           <input
    //             className="h-9 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
    //             placeholder="Search friends"
    //             value={searchTerm}
    //             onChange={(e) => setSearchTerm(e.target.value)}
    //             disabled={isDataLoading}
    //           />
    //         </div>

    //         <section className="max-h-[218px] overflow-y-auto pt-1">
    //           <div className="flex flex-col gap-2">
    //             {isDataLoading ? (
    //               <div className="flex items-center justify-center py-10">
    //                 <Loader2 className="h-8 w-8 animate-spin text-steelTeal-1000" />
    //                 <span className="ml-2 text-steelTeal-1000">
    //                   Loading members...
    //                 </span>
    //               </div>
    //             ) : filteredMembers?.length > 0 ? (
    //               filteredMembers.map((member) => {
    //                 if (!member?.user) return null;

    //                 const {
    //                   firstName = '',
    //                   lastName = '',
    //                   username = '',
    //                   profileImage = '',
    //                 } = member.user;
    //                 const profile_img = profileImage || defaultImage;
    //                 const userId = member?.userId;
    //                 const memberIsAdmin = member?.isAdmin;

    //                 return (
    //                   <div
    //                     key={userId}
    //                     className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
    //                   >
    //                     <div className="flex flex-row items-center justify-center gap-2.5">
    //                       {/* Profile Image */}
    //                       <div
    //                         className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlugroupIde-1000"
    //                         onClick={() => console.log('Profile clicked')}
    //                       >
    //                         <Image
    //                           src={profile_img}
    //                           width={10000}
    //                           height={10000}
    //                           className="h-full w-full max-w-full rounded-full object-cover object-center"
    //                           alt="Profile"
    //                         />
    //                       </div>

    //                       {/* User Info (Name and Admin check) */}
    //                       <div className="mt-4 flex flex-col justify-center">
    //                         <span className="font-semibold">
    //                           {firstName} {lastName}
    //                         </span>
    //                         {memberIsAdmin && (
    //                           <span className="text-sm text-green-500">
    //                             Admin
    //                           </span>
    //                         )}
    //                         <span className="text-sm text-gray-500">
    //                           @{username}
    //                         </span>
    //                       </div>
    //                     </div>
    //                     {!memberIsAdmin && canAddMembers && (
    //                       <div
    //                         className={`cursor-pointer ${isRemoving ? 'opacity-50' : ''}`}
    //                         onClick={() =>
    //                           !isRemoving && handleRemoveMember(userId)
    //                         }
    //                       >
    //                         {isRemoving ? (
    //                           <Loader2 className="text-white h-6 w-6 animate-spin" />
    //                         ) : (
    //                           <UserRoundMinus className="text-white h-6 w-6" />
    //                         )}
    //                       </div>
    //                     )}
    //                   </div>
    //                 );
    //               })
    //             ) : (
    //               <div className="mt-10 flex justify-center text-steelTeal-1000">
    //                 No members found
    //               </div>
    //             )}
    //           </div>
    //         </section>
    //       </div>
    //     </div>
    //   </div>
    // </div>
    <div className="max-w-groupId[38.25rem] relative w-full p-0 md:p-4">
      <div className="relative flex max-h-[90vh] min-h-[34.875rem] flex-col overflow-hidden rounded-lg bg-steelTeal-800 ">
        {/* Scrollable Content */}
        {isLoading ? (
          <p className="flex h-[300px] items-center justify-center px-7 py-[3.125rem]">
            Loading...
          </p>
        ) : (
          <div className="overflow-y-auto px-7 py-[1.5rem] max-sm:px-4  max-sm:py-[1.5rem]">
            {/* Header */}
            <div className="mb-2 flex items-center justify-between bg-steelTeal-800">
              <h4 className="text-white text-[1.25rem] font-semibold">
                {groupDetails?.group?.groupName || 'Group'}
              </h4>

              <div className="flex items-center gap-4">
                <div
                  onClick={() => {
                    closeModal();
                    if (window?.innerWidth <= 768) {
                      setOpenChat(false);
                    }
                  }}
                >
                 <Link
                  href={groupUrlHref(groupDetails)}
                  className="w-fit rounded-[50px] border border-golden-600 bg-black-1000 px-[10px] py-[5px] text-sm"
                >
                  View page
                </Link>
                </div>

                <>
                  {isAuthenticated && isAdmin && (
                    <div onClick={openGroupUpdateModal}>
                      <Link
                        href="javascript:void(0);"
                        className="text-xs font-semibold text-steelTeal-200 underline"
                      >
                        Update
                      </Link>
                    </div>
                  )}
                  {/* {groupDetails?.group?.groupSettings?.onlyAdminCanAddMembers && ( */}
                  {isAuthenticated  && isMember && (
                    <div onClick={openAddMemberModal}>
                      <AddUserModal className="cursor-pointer" fill="#C3C4C7" />
                    </div>
                  )}
                </>

                {/* )} */}
                <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
                  <X className="h-[1.75rem] w-[1.75rem] text-white-450 transition-all duration-300 hover:text-steelTeal-200" />
                </IconButton>
              </div>
            </div>
            {/* Group Name */}
            <div className="mb-4 flex items-center gap-[1.4375rem]">
              <InputField
                type="text"
                name="userName"
                placeholder="Search Members"
                label=""
                color="text-white-1000"
                className="rounded-[0.625rem] bg-inputBgColor "
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="max-h-[calc(100vh-320px)] overflow-y-auto max-sm:max-h-[calc(100vh-410px)]">
              {filteredMembers?.map((member) => (
                <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
                  <div className="flex w-full items-center justify-between gap-[0.313rem]">
                    {/* <Image
                src={User}
                alt="User"
                className="h-12 w-12 rounded-full object-cover"
              /> */}
                    <div className="flex items-center justify-center gap-2">
                      <div className="relative h-12 w-12">
                        <ChatAvatar
                          profileImage={member?.user?.imageUrl}
                          userName={member?.user?.username}
                          imageClassName="h-full w-full rounded-full object-cover"
                          avatarSize={48}
                        />
                        <span
                          className={`absolute bottom-[0.75px] right-[0.75px] h-3 w-3 rounded-full ${userStatusColor(
                            member?.user?.currentStatus || 'AWAY_MODE',
                          )}`}
                        />
                      </div>
                      <div className="flex flex-col items-start justify-center">
                        <h4 className="text-sm font-bold">{member?.user?.username}</h4>
                        {member.isAdmin && (
                          <div className="!h-5 px-0 text-[10px] font-bold uppercase text-yellow-500">
                            Admin
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* <AddUserModal className="cursor-pointer" /> */}
                </div>
              ))}
            </div>

            {/* <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
            <div className="flex items-center gap-[0.313rem]">
              <Image
                src={User}
                alt="User"
                className="h-12 w-12 rounded-full object-cover"
              />
              <h4 className="text-sm font-bold">Kimberly Mastrangelo</h4>
            </div>
            <AddUserModal className="cursor-pointer" />
          </div>
          <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
            <div className="flex items-center gap-[0.313rem]">
              <Image
                src={User}
                alt="User"
                className="h-12 w-12 rounded-full object-cover"
              />
              <h4 className="text-sm font-bold">Kimberly Mastrangelo</h4>
            </div>
            <AddUserModal className="cursor-pointer" />
          </div> */}
          </div>
        )}
      </div>
    </div>
  );
};

export default ShowGroupModal;
