'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { UserPlus } from 'lucide-react'; // Join group icon
import {
  useGetFriendsListQuery,
  useJoinedGroupMutation,
  useJoinGroupSendMutation,
} from '@/reactQuery/chatWindowQuery';
import { toast } from 'react-hot-toast';
import Tooltip from '@/components/Common/Tooltip';
import IconButton from '@/components/Common/Button/IconButton';
import { X, UserRoundMinus } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import { useQueryClient } from '@tanstack/react-query';
import defaultImage from '@/assets/icons/profile-icon.svg';
import InputField from '@/components/Common/InputField';
import { User } from 'lucide-react';
import AddUserModal from '@/assets/icons/AddUserModal';
import ChatAvatar from '@/components/ChatAvatar';
import ButtonLoader from '@/components/Common/Loader/ButtonLoader';

function AddFriendsFromSearch({ groupId, groupMembers, groupDetails }) {
  console.log('🚀 ~ AddFriendsFromSearch ~ groupDetails:', groupDetails);
  const [search, setSearch] = useState('');
  const [selectedMembers, setSelectedMembers] = useState([]);

  const { data: friendsList } = useGetFriendsListQuery({
    enabled: !!search,
    params: { search },
  });
  console.log('🚀 ~ AddFriendsFromSearch ~ friendsList:', friendsList);
  const { closeModal } = useModalStore();
  const queryClient = useQueryClient();
  const [memberAddingId, setMemberAddingId] = useState(null);
  const { mutate: updateJoinedGroup, isPending } = useJoinedGroupMutation({
    onSuccess: () => {
      // console.log(response, ":::::::::::this is a respopnse i need to check")
      // toast.success('Successfully joined the group!');
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      // closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });
  const { mutate: sendJoinGroupInviation, isPending: isPendingInvitation } =
    useJoinGroupSendMutation({
      onSuccess: () => {
        // console.log(response, ":::::::::::this is a respopnse i need to check")
        toast.success('Invitation send to join group!');
        // queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
        // closeModal();
      },
      onError: (error) => {
        console.log('🚀 ~ AddFriendsFromSearch ~ error:', error);
        const message =
          error.response?.data?.errors?.[0]?.description ||
          'Something went wrong';
        toast.error(message);
      },
    });

  const handleJoinedGroup = () => {
    const payload = {
      groupId,
      action: 'add',
      members: selectedMembers,
    };

    updateJoinedGroup(payload, {
      onSuccess: () => {
        toast.success('Successfully added to the group!');
      },
    });
  };
  const handleAddMember = (member) => {
    const payload = {
      groupId,
      action: 'add',
      members: [member],
    };
    if (groupDetails?.groupType == 'private') {
      sendJoinGroupInviation({
        groupId,
        receiverId: member,
      });
    } else {
      updateJoinedGroup(payload, {
        onSuccess: () => {
          toast.success('Successfully added to the group!');
        },
      });
    }
  };
  const handleMemberSelect = (userId) => {
    setSelectedMembers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId); // If the user is already selected, remove them
      }
      return [...prev, userId]; // Otherwise, add them to the selection
    });
  };

  const handleRemoveMember = (userId) => {
    const payload = {
      groupId,
      action: 'remove',
      members: [userId],
    };

    updateJoinedGroup(payload, {
      onSuccess: () => {
        toast.success('Successfully removed from the group!');
      },
    });
  };

  return (
    // <div
    //   tabIndex="-1"
    //   aria-hidden="true"
    //   className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    // >
    //   <div className="relative w-full max-w-xl p-4">
    //     <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
    //       <div className="flex items-center justify-between p-4">
    //         <h3 className="text-white mt-1 text-lg font-semibold">
    //           Add and Remove Member in Group
    //         </h3>
    //         <IconButton
    //           onClick={() => closeModal()}
    //           className="h-6 w-6 min-w-6"
    //         >
    //           <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
    //         </IconButton>
    //       </div>
    //       <div className="px-2 py-1">
    //         <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
    //           <input
    //             className="h-9 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
    //             placeholder="Search friends"
    //             value={search}
    //             onChange={(e) => setSearch(e.target.value)}
    //           />
    //         </div>
    //         <section className="max-h-[218px] overflow-y-auto pt-1">
    //           <div className="flex flex-col gap-2">
    //             {search && friendsList?.rows?.length > 0 ? (
    //               friendsList.rows.map((friend) => {
    //                 const isMember = groupMembers.some(
    //                   (member) => member.userId === friend.relationUser?.userId,
    //                 );
    //                 const isSelected = selectedMembers.includes(
    //                   friend.relationUser?.userId,
    //                 ); // Check if this user is selected

    //                 return (
    //                   <div
    //                     key={friend.relationUser?.userId}
    //                     className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
    //                   >
    //                     <div className="flex flex-row gap-2.5">
    //                       <div className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000">
    //                         <Image
    //                           src={defaultImage}
    //                           width={10000}
    //                           height={10000}
    //                           className="h-full w-full max-w-full rounded-full object-cover object-center"
    //                           alt="Profile"
    //                         />
    //                       </div>
    //                       <div className="mt-4 flex justify-center">
    //                         {friend.relationUser?.username}
    //                       </div>
    //                     </div>

    //                     <div className="flex items-center gap-2">
    //                       {isMember ? (
    //                         <div
    //                           className="cursor-pointer"
    //                           onClick={() =>
    //                             handleRemoveMember(friend.relationUser?.userId)
    //                           }
    //                         >
    //                           <UserRoundMinus className="text-white h-6 w-6" />
    //                         </div>
    //                       ) : isSelected ? (
    //                         <div
    //                           className="cursor-pointer"
    //                           onClick={() =>
    //                             handleMemberSelect(friend.relationUser?.userId)
    //                           }
    //                         >
    //                           <UserRoundMinus className="text-white h-6 w-6" />
    //                         </div>
    //                       ) : (
    //                         <Tooltip text="Add to group" position="left">
    //                           <UserPlus
    //                             className="text-white h-6 w-6 cursor-pointer"
    //                             onClick={() =>
    //                               handleMemberSelect(
    //                                 friend.relationUser?.userId,
    //                               )
    //                             }
    //                           />
    //                         </Tooltip>
    //                       )}
    //                     </div>
    //                   </div>
    //                 );
    //               })
    //             ) : (
    //               <div className="mt-10 flex justify-center">No Friends</div>
    //             )}
    //           </div>
    //         </section>
    //         <button
    //           type="button"
    //           onClick={handleJoinedGroup}
    //           disabled={selectedMembers.length === 0 || isPending}
    //           className="text-white mt-4 w-full rounded-lg bg-steelTeal-1000 p-2 disabled:opacity-50"
    //         >
    //           {isPending ? 'Joining...' : 'Join Group'}
    //         </button>
    //       </div>
    //     </div>
    //   </div>
    // </div>
    <div className="relative w-full max-w-[38.25rem] p-0 md:p-4">
      <div className="relative flex max-h-[90vh] min-h-[34.875rem] flex-col overflow-hidden rounded-lg bg-steelTeal-800 ">
        {/* Scrollable Content */}
        <div className="overflow-y-auto px-7 py-[1.5rem] max-sm:px-4  max-sm:py-[1.5rem]">
          {/* Header */}
          <div className="mb-2 flex items-center justify-between bg-steelTeal-800">
            <h4 className="text-white text-[1.25rem] font-semibold">
              Invite Friends To {groupDetails?.groupName}
            </h4>
            <div className="flex items-center gap-4">
              {/* <div>

              <Link
                href="javascript:void(0);"
                className="text-white text-xs font-semibold"
              >
                Update
              </Link>
              </div>
              <AddUserModal className="cursor-pointer" /> */}

              <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
                <X className="hover:text-white h-[1.75rem] w-[1.75rem] text-white-450 transition-all duration-300" />
              </IconButton>
            </div>
          </div>
          {/* Group Name */}
          <div className="mb-4 flex items-center gap-[1.4375rem]">
            <InputField
              type="text"
              name="userName"
              placeholder="Enter Friend Name"
              label=""
              color="text-white-1000"
              className="rounded-[0.625rem] bg-inputBgColor "
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="max-h-[calc(100vh-320px)] overflow-y-auto max-sm:max-h-[calc(100vh-410px)]">
            {friendsList?.rows?.map((friend) => {
              const isMember = groupMembers.some(
                (member) => member.userId === friend.relationUser?.id,
              );
              const isSelected = selectedMembers.includes(
                friend.relationUser?.id,
              ); // Check if this user is selected
              if (!isMember)
                return (
                  <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
                    <div className="flex items-center gap-[0.313rem]">
                      <ChatAvatar
                        profileImage={friend.relationUser?.imageUrl}
                        userName={friend.relationUser?.username}
                        imageClassName="h-12 w-12 rounded-full object-cover"
                        avatarSize={48}
                      />
                      <h4 className="text-sm font-bold">
                        {friend.relationUser?.username}
                      </h4>
                    </div>
                    <div
                      onClick={() => {
                        handleAddMember(friend.relationUser?.id);
                        setMemberAddingId(friend.relationUser?.id);
                      }}
                    >
                      {memberAddingId == friend.relationUser?.id &&
                      (isPending || isPendingInvitation) ? (
                        <ButtonLoader />
                      ) : (
                        <AddUserModal className="cursor-pointer" />
                      )}
                    </div>
                    {/* {isMember ? (
                  <div
                    className="cursor-pointer"
                    onClick={() =>
                      handleRemoveMember(friend.relationUser?.id)
                    }
                  >
                    <UserRoundMinus className="text-white h-6 w-6" />
                  </div>
                ) : isSelected ? (
                  <div
                    className="cursor-pointer"
                    onClick={() =>
                      handleMemberSelect(friend.relationUser?.id)
                    }
                  >
                    <UserRoundMinus className="text-white h-6 w-6" />
                  </div>
                ) : (
                  <Tooltip text="Add to group" position="left">
                    <UserPlus
                      className="text-white h-6 w-6 cursor-pointer"
                      onClick={() =>
                        handleMemberSelect(friend.relationUser?.id)
                      }
                    />
                  </Tooltip>
                )} */}
                  </div>
                );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AddFriendsFromSearch;
