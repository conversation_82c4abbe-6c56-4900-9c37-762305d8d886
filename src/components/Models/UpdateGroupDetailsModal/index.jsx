'use client';

import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import IconButton from '@/components/Common/Button/IconButton';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CreateGroupSchema,
  updateGroupSchema,
} from '@/schemas/CreateGroupSchema';
import useModalStore from '@/store/useModalStore';
import { X } from 'lucide-react';
import {
  useGetGroupDetails,
  useGroupChatUpdateDetailsMutation,
} from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import useAuthStore from '@/store/useAuthStore';
import SwitchButton from '@/components/ChatWindow/Friends/SwitchButton';
import InputField from '@/components/Common/InputField';

function ToggleSwitch({ label, name, register, defaultChecked }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-white text-base">{label}</span>
      <label className="relative inline-flex cursor-pointer items-center">
        <input
          type="checkbox"
          {...register(name)}
          defaultChecked={defaultChecked}
          className="peer sr-only"
        />
        <div className="after:bg-white relative h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-1 after:top-1 after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:transition-all after:content-[''] peer-checked:bg-steelTeal-1000 peer-checked:after:translate-x-5 peer-focus:ring-4 peer-focus:ring-steelTeal-300" />
      </label>
    </div>
  );
}

function UpdateGroupDetailsModal({ groupId }) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: groupDetails, refetch: refetchGroupDetails } =
    useGetGroupDetails({
      params: { groupId },
      enabled: !!isAuthenticated,
    });
  const [onlyAdminCanCall, setOnlyAdminCanCall] = useState(false);
  const [onlyAdminCanAddMembers, setOnlyAdminCanAddMembers] = useState(false);
  const [onlyAdminCanUpdateGroupDetails, setOnlyAdminCanUpdateGroupDetailss] =
    useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
    control,
  } = useForm({
    resolver: zodResolver(updateGroupSchema),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      onlyAdminCanCall: false,
      onlyAdminCanAddMembers: false,
      onlyAdminCanUpdateGroupDetails: false,
    },
  });

  console.log('🚀 ~ UpdateGroupDetailsModal ~ errors:', errors);
  const queryClient = useQueryClient();
  const { closeModal } = useModalStore();

  const groupChatMutation = useGroupChatUpdateDetailsMutation({
    onSuccess: () => {
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      toast.success('Group details updated successfully!');
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Failed to update group details';
      toast.error(message);
    },
  });

  console.log(
    '🚀 ~ UpdateGroupDetailsModal ~ groupDetails?.group:',
    groupDetails?.group,
  );
  useEffect(() => {
    if (groupDetails?.group) {
      const { groupName, groupDescription, groupSettings } = groupDetails.group;
      setValue('groupName', groupName);
      setValue('groupDescription', groupDescription);
      setValue('onlyAdminCanCall', groupSettings.onlyAdminCanCall);
      setValue('onlyAdminCanAddMembers', groupSettings.onlyAdminCanAddMembers);
      setValue(
        'onlyAdminCanUpdateGroupDetails',
        groupSettings.onlyAdminCanUpdateGroupDetails,
      );
      setOnlyAdminCanAddMembers(groupSettings.onlyAdminCanAddMembers);
      setOnlyAdminCanCall(groupSettings.onlyAdminCanCall);
      setOnlyAdminCanUpdateGroupDetailss(
        groupSettings.onlyAdminCanUpdateGroupDetails,
      );
    }
  }, [groupDetails, setValue]);

  const onSubmit = async (data) => {
    console.log('🚀 ~ onSubmit ~ data:', data);
    try {
      data.onlyAdminCanCall = String(onlyAdminCanCall);
      data.onlyAdminCanAddMembers = String(onlyAdminCanAddMembers);
      data.onlyAdminCanUpdateGroupDetails = String(
        onlyAdminCanUpdateGroupDetails,
      );
      groupChatMutation.mutate({ ...data, groupId });
    } catch (error) {
      toast.error('Failed to update group details!');
    }
  };

  return (
    <div className="relative w-full max-w-[38.25rem] p-0 md:p-4">
      <div className="relative flex max-h-[90vh] flex-col overflow-hidden rounded-lg bg-steelTeal-800 ">
        {/* Scrollable Content */}
        <div className="overflow-y-auto px-7 py-[3.125rem] max-sm:px-6 max-sm:py-[1.5rem]">
          {/* Header */}
          <div className="mb-[1.8125rem] flex items-center justify-between bg-steelTeal-800">
            <h4 className="text-white  text-[1.25rem] font-semibold">
              Update Group Details
            </h4>
            <div className="flex items-center gap-4">
              <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
                <X className="hover:text-white h-[1.75rem] w-[1.75rem] text-white-450 transition-all duration-300" />
              </IconButton>
            </div>
          </div>
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Group Name */}
            <div className="lex mb-5 items-center gap-[1.4375rem]">
              <InputField
                type="text"
                name="groupName"
                placeholder="Enter Group Name"
                label="Group Name"
                color="text-white-1000"
                className="rounded-[0.625rem] bg-inputBgColor "
                register={register}
                registerName="groupName"
                error={errors?.groupName?.message}
              />
            </div>
            <div className="mb-5 flex items-center gap-[1.4375rem]">
              <InputField
                type="text"
                name="groupDescription"
                placeholder="Enter Group Description"
                label="Group Description"
                color="text-white-1000"
                className="rounded-[0.625rem] bg-inputBgColor "
                register={register}
                registerName="groupDescription"
                error={errors?.groupDescription?.message}
              />
            </div>
            <div className="pb-3">
              <SwitchButton
                leftLabel="Only Admin Can Add Members"
                showRightLabel={false}
                onKnobColor="bg-textGradient"
                offKnobColor="bg-erieBlack-300"
                onBgColor="bg-transparent"
                offBgColor="bg-textGradient"
                switchWidth="w-[2.125rem]"
                switchHeight="h-5"
                knobSize="h-[.875rem] w-[.875rem]"
                setValue={setOnlyAdminCanAddMembers}
                showLeftLabel={true}
                defaultLeft={
                  !groupDetails?.group?.groupSettings.onlyAdminCanAddMembers
                }
              />
            </div>
            {groupDetails?.group?.groupType == 'private' && (
              <div className="pb-3">
                <SwitchButton
                  leftLabel="Only Admin Can Update Group Details"
                  showRightLabel={false}
                  onKnobColor="bg-textGradient"
                  offKnobColor="bg-erieBlack-300"
                  onBgColor="bg-transparent"
                  offBgColor="bg-textGradient"
                  switchWidth="w-[2.125rem]"
                  switchHeight="h-5"
                  knobSize="h-[.875rem] w-[.875rem]"
                  setValue={setOnlyAdminCanUpdateGroupDetailss}
                  showLeftLabel={true}
                  defaultLeft={
                    !groupDetails?.group?.groupSettings
                      .onlyAdminCanUpdateGroupDetails
                  }
                />
              </div>
            )}
            <div className="flex justify-center">
              <PrimaryButton
                variant="secondary"
                className="mt-1 !h-[1.5625rem] !w-fit rounded-[.9375rem] !text-[.8125rem] font-bold"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Updating...' : 'Update Group'}
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default UpdateGroupDetailsModal;
