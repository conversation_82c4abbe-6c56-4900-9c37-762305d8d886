import IconButton from '@/components/Common/Button/IconButton';
import Tooltip from '@/components/Common/Tooltip';
import usePrivateCall from '@/hooks/usePrivateCall';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useActivePlayerStore from '@/store/useActivePlayeStore';
import useCallStore from '@/store/useCallStore';
import { Mic, MicOff } from 'lucide-react';
import { useEffect, useState } from 'react';

const ToggleMuteButton = ({ rtc }) => {
  // const {isMuted,setIsMuted}=usePrivateCall()
  const isMuted = useActivePlayerStore((state) => state.isMuted);
  const setIsMuted = useActivePlayerStore((state) => state.setIsMuted);
  // const [isMuted, setIsMuted] = useState(false);
  const { toggleMuted, setToggleMuted } = useCallStore();
  const toggleMicrophone = async () => {
    setIsMuted(!isMuted);
    setToggleMuted(!isMuted);
    if (rtc.localAudioTrack) {
      if (isMuted) {
        await rtc.localAudioTrack.setEnabled(true);
      } else {
        await rtc.localAudioTrack.setEnabled(false);
      }
    }
  };
  // useEffect(()=>{
  //     setIsMuted(toggleMuted)
  // },[])
  return (
    <Tooltip text={isMuted ? 'Unmute' : 'mute'}>
      <IconButton onClick={toggleMicrophone} className="h-6 w-6">
        {isMuted ? (
          <MicOff className="h-5 w-5 text-red-500" />
        ) : (
          <Mic className="text-white h-5 w-5" />
        )}
      </IconButton>
    </Tooltip>
  );
};

export default ToggleMuteButton;
