import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import IncomingCallIcon from '@/assets/icons/IncomingCallIcon';
import IconButton from '@/components/Common/Button/IconButton';
import useCallModalStore from '@/store/useCallModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { motion } from 'framer-motion';
import { Minimize, PhoneCall } from 'lucide-react';
import { useEffect, useRef } from 'react';
import Draggable from 'react-draggable';
import Timer from './component/Timer';

import ChatAvatar from '@/components/ChatAvatar';
import Tooltip from '@/components/Common/Tooltip';
import usePrivateCall, { rtc } from '@/hooks/usePrivateCall';
import ToggleMuteButton from './component/ToggleMuteButton';
import { CheckCircle } from 'lucide-react';
import CrossCircleIcon from '@/assets/icons/Cross-Circle';
import { CircleCheck } from 'lucide-react';
import { CircleX } from 'lucide-react';
import { Maximize } from 'lucide-react';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';

function CallPopup() {
  const { setIsMinimized, isMinimized } = useCallModalStore((state) => state);
  const { voiceCall, setVoiceCall } = useVoiceCallStore((state) => state);
  const positionRef = useRef({ x: 0, y: 0 });
  const {
    handleAcceptCall,
    handleDeclineCall,

    isCallActive,
    isCallAccepting,
    handleDisconnectCall
  } = usePrivateCall();

  useEffect(() => {
    if (!rtc?.client) return;

    rtc?.client.on('user-published', async (user, mediaType) => {
      await rtc?.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc?.remoteAudioTrack.play();
      }
    });

    rtc?.client.on('user-unpublished', () => {
      if (rtc?.remoteAudioTrack) {
        rtc?.remoteAudioTrack.stop();
        rtc.remoteAudioTrack = null;
      }
    });

    return () => {
      rtc?.client.removeAllListeners();
    };
  }, []);
  if (!voiceCall) return;

  // const MinimizedCall = () => (
  //   <div
  //     className="text-white fixed bottom-[62px]  right-4 z-50 flex cursor-pointer items-center rounded-full bg-gray-800 p-2 shadow-lg lg:bottom-4"
  //     onClick={() => setIsMinimized(false)}
  //   >
  //     <PhoneCall className="h-6 w-6 text-green-500" />
  //     <span className="ml-2">Call in Progress</span>
  //   </div>
  // );
  const handleMinimized = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsMinimized(true);
  };
  const CallContent = () => {
    return (
      <div className="relative h-[382px] w-[334px]  max-w-md rounded-lg bg-[#1B1A1A] px-4 py-3  md:h-[382px]  md:w-[420px]  border-2 border-yellow-400 shadow-[0_0_10px_rgba(255,215,0,0.6)]">
        <div className="drag-handle flex cursor-move justify-between pb-3">
          <div className="flex  items-center justify-between  p-1">
            <h3 className="text-white flex text-lg font-semibold">
              {/* {isCallActive ? (
                'Call Connected'
              ) : (
                <>
                  Incoming Call
                  <span className="inline-block w-6 animate-dots overflow-hidden">
                    ...
                  </span>
                </>
              )} */}
              Connect Request
            </h3>
          </div>
          <div className="flex justify-end gap-2 p-2">
            <Tooltip text={'Minimise'}>
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleMinimized(e);
                }}
                onTouchStart={handleMinimized}
                className="h-7 w-7"
              >
               <Minimize className="h-6 w-6 text-yellow-400"  />
              </IconButton>
            </Tooltip>

            {/* <IconButton onClick={(e) => { e.stopPropagation(); handleDeclineCall(); }} className="h-7 w-7">
              <X className="h-5 w-5 text-white" />
            </IconButton> */}
          </div>
        </div>

        <div className=" relative  before:absolute before:-bottom-2 before:left-0 before:h-[1px] before:w-full before:bg-tabBottomBorder before:blur-[0.1rem] before:content-['']"></div>

        <div className="mt-[24px] flex flex-col items-center p-4 text-center">
          {/* <CustomImage
            src={voiceCall?.profileImage || profile_url}
            alt="Profile"
            width={90}
            height={90}
            className="h-24 w-24 rounded-full object-cover"
          /> */}
          <ChatAvatar
            profileImage={voiceCall?.profileImage}
            firstName={voiceCall?.firstName || ''}
            lastName={voiceCall?.lastName || ''}
            userName={voiceCall?.username}
            imageClassName="size-[80px]  md:size-[110px] rounded-full object-cover"
            imageWidth={window?.innerWidth < 768 ? 80 : 110}
            imageHeight={window?.innerWidth < 768 ? 80 : 110}
            avatarSize={window?.innerWidth < 768 ? 80 : 110}
          />
          <h4 className="mt-5 text-sm font-bold text-steelTeal-300">
            {voiceCall?.username?.toLowerCase() || 'Unknown User'}
          </h4>
          {/* {isCallActive && (
            <p className="text-white mt-2 text-sm">
              Call Duration: <Timer isActive={isCallActive} />
            </p>
          )} */}
        </div>

        <div className="flex items-center justify-center gap-6 p-4">
          {voiceCall?.role == 'publisher' ? (
            <PrimaryButton className="text-white max-w-[158px] rounded-[0.625rem] border-secondaryBtnBg bg-secondaryBtnBg px-4 py-2 text-[.9375rem] font-semibold"
            onClick={handleDisconnectCall}
            >
              Cancel Request
            </PrimaryButton>
          ) : (
            <>
              {!isCallActive && (
                <IconButton
                  onClick={() => {
                    isCallAccepting ? null : handleAcceptCall();
                  }}
                  // className={` h-9 w-9 rounded-full bg-green-600  ${isCallAccepting ? 'animate-pulse' : 'hover:scale-125'} `}
                >
                  <CircleCheck className="h-[38px] w-[38px] text-[#42E225]" />
                </IconButton>
              )}

              <IconButton
                onClick={handleDeclineCall}
                // className={` h-9 w-9  rounded-full bg-red-600 hover:scale-125  `}
              >
                <CircleX className="h-[38px] w-[38px] text-[#B60E01]" />
              </IconButton>
              {/* <IconButton onClick={toggleMicrophone} className="h-6 w-6">
            {mutedRef ? <MicOff className="h-5 w-5 text-red-500" /> : <Mic className="h-5 w-5 text-white" />}
            </IconButton> */}
              <ToggleMuteButton rtc={rtc} />
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {isMinimized ? (
        <>
        {/* <MinimizedCall /> */}
        </>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="bg-black absolute inset-0 z-50 flex h-full w-full items-center justify-center bg-opacity-50"
        >
          <Draggable
            handle=".drag-handle"
            bounds="parent"
            defaultPosition={{ x: 0, y: 0 }}
            onStop={(_, data) => {
              positionRef.current = { x: data.x, y: data.y };
            }}
          >
            <div className="m-4">
              <CallContent />
            </div>
          </Draggable>
        </motion.div>
      )}
    </>
  );
}

export default CallPopup;
