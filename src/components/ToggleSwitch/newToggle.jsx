import React from 'react';

function ToggleSwitchNew({ enabled, setEnabled, authCheck }) {
  return (
    <button
      onClick={() => {
        const authorize = authCheck();
        if (authorize) {
          setEnabled(!enabled);
        }
      }}
      className="flex h-[20px] w-[34px] items-center rounded-full border border-white-400 p-1 transition-colors duration-300 "
    >
      <div
        className={`h-[14px] w-[14px] transform rounded-full bg-cardBorderGradient shadow-md transition-transform duration-300 ${
          enabled ? 'translate-x-[10px]' : 'translate-x-0'
        }`}
      ></div>
    </button>
  );
}

export default ToggleSwitchNew;
