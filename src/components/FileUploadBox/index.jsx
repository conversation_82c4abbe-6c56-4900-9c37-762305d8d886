'use client';

import React from 'react';
import Image from 'next/image';

const FileUploadBox = ({
  label,
  preview,
  error,
  required = false,
  onChange,
  accept = 'image/png, image/jpeg, image/jpg, image/webp',
  placeholderImg,
  placeholderText = 'Upload File',
  height = 'h-32',
  borderColor = 'border-steelTeal-620',
}) => {
  return (
    <div className="">
      {label && (
        <label className="relative mb-1 flex gap-2 text-sm font-semibold">
          {label}
        </label>
      )}
      <div
        className={`relative w-full overflow-hidden rounded-lg  border-[2px] border-dashed 
          ${required && !preview ? '' : borderColor} 
           ${height}`}
      >
        {preview ? (
          <Image
            src={preview}
            alt="Preview"
            layout="fill"
            objectFit="cover"
            className="h-full w-full"
          />
        ) : (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              {placeholderImg && (
                <Image
                  src={placeholderImg}
                  className="mx-auto mb-2 w-9"
                  alt="Upload Placeholder"
                />
              )}
              <p className="text-xs font-semibold capitalize text-steelTeal-200 underline">
                {placeholderText}
              </p>
            </div>
          </div>
        )}
        <input
          type="file"
          onChange={onChange}
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          accept={accept}
        />
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};

export default FileUploadBox;
