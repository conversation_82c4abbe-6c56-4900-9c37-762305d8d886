'use client';
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useGetRecentGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useGameStore from '@/store/useGameStore';
import { slugify } from '@/utils/helper';
import ContinuePlayingIcon from '@/assets/icons/ContinuePlaying';
import GameSection from '../GameSection';

function ContinuePlaying({ isCarousel = false }) {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore((state) => state);
  const { searchQuery, setRecentGames, activeTab } = useGameStore((state) => ({
    searchQuery: state.searchQuery,
    setRecentGames: state.setRecentGames,
    activeTab: state.activeTab,
  }));


  const { data: recentGames, isLoading } = useGetRecentGamesQuery({
    enabled: isAuthenticated && activeTab === "Continue Playing",
    search: searchQuery,
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace("/"); // redirect non-authenticated users
    }
  }, [isAuthenticated, router]);


  useEffect(() => {
    if (isLoading) return;

    if (recentGames && recentGames.length > 0) {
      setRecentGames(recentGames);
    }
  }, [recentGames, isLoading, setRecentGames]);


  return (
    <GameSection
      title="Continue Playing"
      icon={<ContinuePlayingIcon activeMenu />}
      games={recentGames}
      isLoading={isLoading}
      isCarousel={isCarousel}
      viewAllPath="/continue-playing"
      emptyMessage="Your recently played games will appear here. Start playing to continue where you left off!"
      renderGame={(game) => ({
        id: game?.id,
        image: game?.icon_url ?? '',
        name: game?.name?.EN || game?.name,
        isFavorite: game?.isFavorite,
        provider: game['casinoProvider.name'],
      })}
      onGameClick={(provider, name) =>
        router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`)
      }
    />
  );
}

export default ContinuePlaying;
