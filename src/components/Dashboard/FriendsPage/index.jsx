import MessageIcon from '@/assets/icons/MessageIcon';
import FriendCall from '@/components/ChatWindow/Friends/FriendCall';
import FriendRequestItem from '@/components/ChatWindow/RecentChat/Accordion/FriendRequestItem';
import TextTabs from '@/components/Common/TextTabs';
import {
  useGetFriendsListQuery,
  useGetFriendsRequestListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { useOpenChatWindow } from '@/utils/chatWindow';
import { useState } from 'react';
import FriendsAccordion from './FriendsAccordian';

function FriendsPage({ users }) {
  const { setIsPrivateChatOpen, setUserId, searchUserName, setSearchUserName } =
    usePrivateChatStore((state) => state);
  const { isAuthenticated } = useAuthStore((state) => state);
  const openChatWindow = useOpenChatWindow();
  const [activeTab, setActiveTab] = useState(0);

  const { data: friendsRequestList, refetch: refetchFriendsRequestList } =
    useGetFriendsRequestListQuery({ enabled: isAuthenticated });

  const {
    data: friendsList,
    isLoading: friendsListLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({
    params: { search: '' },
    enabled: isAuthenticated,
  });
  const tabs = [
    {
      label: `My Friends (${friendsList?.count || 0})`,
      content: '',
    },
    {
      label: `Friend Requests (${friendsRequestList?.count || 0})`,
      content: '',
    },
  ];

  return (
    <div>
      {/* Tabs */}
      <div
        className="tab-heading gap relative flex justify-center gap-3 before:absolute before:-bottom-2 before:left-0 before:h-[1px]
       before:w-full before:bg-tabBottomBorder before:blur-[0.1rem] before:content-['']"
      >
        <TextTabs
          tabs={tabs}
          onChange={(index) => setActiveTab(index)}
          authTabs={[0, 1]}
        />
      </div>

      {activeTab === 0 ? (
        <div className="mt-6 grid grid-cols-3 gap-[0.625rem] max-sm:grid-cols-1">
          {friendsList?.rows?.map((friend, idx) => {
            const relation = friend.relationUser;
            return (
              <FriendsAccordion
                bgColor="bg-erieBlack-100"
                gradientClass="bg-erieBlack-100"
                key={idx}
                profileImage={friend.profileImage}
                username={relation?.username}
                userId={relation?.id}
                currentStatus={relation?.currentStatus}
                currentGamePlay={relation?.currentGamePlay}
                actions={[
                  // <AddFriend key="add" size={16} />,
                  <FriendCall
                    key="call"
                    userId={relation.id}
                    user={relation}
                  />,
                  <button
                    key="msg"
                    onClick={() => {
                      setUserId(relation.id);
                      setIsPrivateChatOpen(true);
                      if (searchUserName !== '') {
                        setSearchUserName('');
                      }
                      openChatWindow();
                    }}
                    className="cursor-pointer"
                  >
                    <MessageIcon size={16} />
                  </button>,
                ]}
              />
            );
          })}
        </div>
      ) : (
        <div className="mt-6 space-y-2">
          {friendsRequestList?.rows?.length > 0 ? (
            friendsRequestList?.rows?.map((req, idx) => (
              <FriendRequestItem
                key={idx}
                req={req}
                refetchFriendsRequestList={refetchFriendsRequestList}
              />
            ))
          ) : (
            <p className="flex justify-center text-[13px] text-steelTeal-200">
              No Friend Requests
            </p>
          )}
        </div>
      )}
    </div>
  );
}

export default FriendsPage;
