'use client';

import React, { useCallback, useState } from 'react';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import Enter from '../../../assets/images/svg-images/enter.svg';
import GradientMessage from '../../../assets/images/svg-images/gradient-message.svg';
import GameImg from '../../../assets/webp/friends-game.webp';
import ChatAvatar from '@/components/ChatAvatar';
import { useRouter } from 'next/navigation';
import { getTextValue, slugify, userStatusColor } from '@/utils/helper';
import useUserInfoStore from '@/store/useUserInfoStore';
import useModalStore from '@/store/useModalStore';
import UserInfo from '@/components/UserInfoModal';

export default function FriendsAccordion({
  profileImage,
  username,
  userId,
  currentStatus,
  currentGamePlay,
  bgColor,
  actions = [],
  gradientClass = 'bg-accordionGradient',
  isAdmin = false,
}) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useModalStore();

  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(<UserInfo />);
    },
    [openUserInfoModal, openModal],
  );
  return (
    <div className={`text-white w-full rounded-xl ${bgColor}`}>
      {/* Header */}
      <div
        className={`${open ? gradientClass : 'bg-transparent'} rounded-[.8125rem] pb-[1px] transition-colors duration-300`}
      >
        <div
          onClick={() => setOpen(!open)}
          className="relative flex cursor-pointer items-center justify-between rounded-xl px-4 py-[.6875rem]"
        >
          {/* User Info */}
          <div className="flex items-center gap-3">
            <div
              className="relative h-12 w-12"
              onClick={() => handleOpenUserInfoModal(userId)}
            >
              <ChatAvatar
                profileImage={profileImage}
                userName={username}
                imageClassName="h-full w-full rounded-full object-cover"
                avatarSize={50}
              />
              <span
                className={`absolute -bottom-0 -right-0 h-3 w-3 rounded-full ${userStatusColor(currentStatus)}`}
              />
            </div>
            <div className="flex flex-col">
              <span className="pr-1 text-sm font-bold text-steelTeal-200 max-lg:max-w-[80px] max-lg:truncate max-md:max-w-full">
                {username}
              </span>
              {isAdmin && (
                <span className="text-[0.65rem] font-semibold uppercase text-yellow-400">
                  Admin
                </span>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3">
            {actions.length > 0 ? (
              actions.map((Action, i) => (
                <div key={i} className="flex items-center justify-center">
                  {Action}
                </div>
              ))
            ) : (
              <>
                <div className="h-4 w-4">
                  <Image src={Enter} alt="Enter" className="h-4 w-4" />
                </div>
                <div className="h-4 w-4">
                  <Image
                    src={GradientMessage}
                    alt="Message"
                    className="h-4 w-4"
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Accordion Content */}
      {currentGamePlay && (
        <div
          className={`grid transition-all duration-500 ease-in-out ${
            open ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'
          }
          bg-richBlack-610`}
        >
          <div className="overflow-hidden">
            <div className="px-4 py-3 max-lg:p-2">
              <div className="flex items-end justify-between gap-3">
                <div className="flex gap-3">
                  <div className="h-[4.1875rem] w-[4.1875rem] min-w-[4.1875rem] overflow-hidden rounded-[.25rem]">
                    <Image
                      src={currentGamePlay?.gameThumbnail || GameImg}
                      alt="game"
                      className="h-full w-full object-cover"
                      width={100}
                      height={100}
                    />
                  </div>

                  {/* Game Info */}
                  <div>
                    <span className="inline-block rounded-[1.875rem] bg-erieBlack-200 p-[.375rem] text-[0.625rem] font-medium text-white-400 max-lg:p-1 max-lg:text-[.5rem]">
                      Now Playing
                    </span>
                    <h4 className="mt-1 line-clamp-2 text-sm font-semibold max-lg:text-xs">
                      {getTextValue(currentGamePlay?.gameName) ||
                        'Unknown Game'}
                    </h4>
                  </div>
                </div>

                {/* Play Button */}
                <PrimaryButton
                  className="!h-[1.5625rem] !w-auto !rounded-[0.625rem] !text-[.8125rem] !font-bold max-lg:!text-[0.625rem]"
                  variant="secondary"
                  onClick={() =>
                    router.push(
                      `/casino/games/${slugify(currentGamePlay?.moreDetails?.product)}/${slugify(currentGamePlay?.gameName)}`,
                    )
                  }
                >
                  Play
                </PrimaryButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
