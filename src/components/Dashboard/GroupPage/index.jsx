import TextTabs from '@/components/Common/TextTabs';
import {
  useGetGroupListQuery,
  useGetGroupRequestListQuery,
  useGetPublicGroupListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import GroupAccordian from './GroupAccordian';
import { useState, useRef, useEffect, useCallback } from 'react';
import GroupRequestItem from '@/components/ChatWindow/GroupChat/GroupRequestItem';

function GroupPage() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const [activeTab, setActiveTab] = useState(0);

  const myGroupsQuery = useGetGroupListQuery({ enabled: isAuthenticated });
  const publicGroupsQuery = useGetPublicGroupListQuery({
    enabled: true,
    groupType: 'public',
  });
  const invitationQuery = useGetGroupRequestListQuery({
    enabled: isAuthenticated,
    invitationType: 'invite',
  });

  const loadMoreRef = useRef(null);

  const handleLoadMore = useCallback(() => {
    if (
      activeTab === 0 &&
      myGroupsQuery.hasNextPage &&
      !myGroupsQuery.isFetchingNextPage
    ) {
      myGroupsQuery.fetchNextPage();
    } else if (
      activeTab === 1 &&
      publicGroupsQuery.hasNextPage &&
      !publicGroupsQuery.isFetchingNextPage
    ) {
      publicGroupsQuery.fetchNextPage();
    } else if (
      activeTab === 2 &&
      invitationQuery.hasNextPage &&
      !invitationQuery.isFetchingNextPage
    ) {
      invitationQuery.fetchNextPage();
    }
  }, [activeTab, myGroupsQuery, publicGroupsQuery, invitationQuery]);

  useEffect(() => {
    if (!loadMoreRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          handleLoadMore();
        }
      },
      { threshold: 1.0 },
    );

    observer.observe(loadMoreRef.current);

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [handleLoadMore]);

  useEffect(() => {
    setActiveTab(isAuthenticated ? 0 : 1);
  }, []);
  const tabs = [
    { label: `My Groups (${myGroupsQuery.data?.total || 0})`, content: '' },
    {
      label: `Public Groups (${publicGroupsQuery.data?.total || 0})`,
      content: '',
    },
    { label: `Invitations (${invitationQuery.data?.total || 0})`, content: '' },
  ];

  return (
    <div>
      {/* Tabs */}
      <div className="tab-heading relative flex justify-center gap-3 before:absolute before:-bottom-2 before:left-0 before:h-[1px] before:w-full before:bg-tabBottomBorder before:blur-[0.1rem] before:content-['']">
        <TextTabs tabs={tabs} onChange={(index) => setActiveTab(index)} authTabs={[0,2]} isAuthenticated={isAuthenticated}/>
      </div>

      <div className="mt-6">
        {/* My Groups & Public Groups */}
        {activeTab === 0 || activeTab === 1 ? (
          myGroupsQuery.isLoading || publicGroupsQuery.isLoading ? (
            <p className="text-white text-center">Loading...</p>
          ) : (activeTab === 0
              ? myGroupsQuery?.data?.groups
              : publicGroupsQuery?.data?.groups
            )?.length === 0 ? (
            <div className="text-white text-center">No groups found</div>
          ) : (
            <>
              {(activeTab === 0
                ? myGroupsQuery?.data?.groups
                : publicGroupsQuery?.data?.groups
              )?.map((group) => (
                <GroupAccordian
                  key={group.id}
                  friends={[
                    {
                      id: group.id,
                      name: group.groupName,
                      avatar: group.groupBanner,
                      type: group.groupType,
                      membersCount: group.groupMembersCount,
                      onlineCount: group.totalOnlineMembers,
                      members: group.members,
                    },
                  ]}
                  showJoinButton={activeTab === 1}
                />
              ))}
              <div ref={loadMoreRef} className="h-5" />
              {(myGroupsQuery.isFetchingNextPage ||
                publicGroupsQuery.isFetchingNextPage) && (
                <p className="text-white text-center">Loading more...</p>
              )}
            </>
          )
        ) : (
          <div className="mt-6 space-y-2">
            {invitationQuery.isLoading ? (
              <p className="text-white text-center">Loading...</p>
            ) : invitationQuery.data?.groups?.length > 0 ? (
              <>
                {invitationQuery.data.groups?.map((request) => (
                  <GroupRequestItem key={request.id} req={request} />
                ))}
                <div ref={loadMoreRef} className="h-5" />
                {invitationQuery.isFetchingNextPage && (
                  <p className="text-white text-center">Loading more...</p>
                )}
              </>
            ) : (
              <p className="flex justify-center text-[13px] text-steelTeal-200">
                No Group Invitations
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default GroupPage;
