'use client';

import DoubleUserIcon from '@/assets/icons/DoubleUserIcon';
import MessageIcon from '@/assets/icons/MessageIcon';
import Auth from '@/components/Auth';
import ChatAvatar from '@/components/ChatAvatar';
import FriendCall from '@/components/ChatWindow/Friends/FriendCall';
import GroupCall from '@/components/ChatWindow/GroupChat/GroupCall';
import { useJoinGroup } from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { useOpenChatWindow } from '@/utils/chatWindow';
import { useQueryClient } from '@tanstack/react-query';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
import toast from 'react-hot-toast';
import FriendsAccordion from '../FriendsPage/FriendsAccordian';
import useAuthModalStore from '@/store/useAuthModalStore';

export default function GroupAccordian({
  friends = [],
  showJoinButton = false,
}) {
  const [openIndex, setOpenIndex] = useState(null);
  const { setIsGroupChatOpen, setGroupId, setGroupName } = useGroupChatStore(
    (state) => state,
  );
  const { isAuthenticated } = useAuthStore((state) => state);

  const { setIsPrivateChatOpen, setUserId, searchUserName, setSearchUserName } =
    usePrivateChatStore((state) => state);
  const openChatWindow = useOpenChatWindow();
  const { setSelectedTab } = useAuthTab((state) => state);

  const { userDetails } = useAuthStore();
  const queryClient = useQueryClient();
  const joinGroup = useJoinGroup({
    onSuccess: async (response) => {
      toast.success('Group Joined');
      queryClient.invalidateQueries({
        queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });
      refetch();
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });

  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const openGroupChat = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
    setIsPrivateChatOpen(false);
    if (searchUserName !== '') setSearchUserName('');
  };
  const { openModal} = useAuthModalStore();

  return (
    <div className="mb-[0.625rem]">
      {friends?.map((group, index) => (
        <div
          key={group.id}
          className="text-white w-full rounded-xl bg-richBlack-610"
        >
          {/* Header */}
          <div
            onClick={() => toggleAccordion(index)}
            className="relative flex cursor-pointer items-center justify-between rounded-xl bg-erieBlack-200 px-4 py-[0.6875rem]"
          >
            {/* Group Info */}
            <div className="flex items-center gap-3">
              <div className="relative h-12 w-12">
                <ChatAvatar
                  profileImage={group?.avatar}
                  userName={group?.name}
                  avatarSize={50}
                  imageClassName="h-full w-full rounded-full object-cover"
                />
              </div>
              <div className="flex flex-col">
                <h4 className="mb-[0.313rem] text-sm font-bold text-steelTeal-200">
                  {group.name}
                </h4>
                <div className="flex items-center gap-[0.625rem]">
                  <span className="rounded-[1.875rem] bg-secondaryBtnBg px-2.5 py-0 text-[0.625rem] font-semibold uppercase">
                    {group.type}
                  </span>
                  <div className="flex items-center gap-1">
                    <DoubleUserIcon />
                    <p className="text-[0.625rem] font-black text-steelTeal-200">
                      {group.membersCount}
                    </p>
                  </div>
                  <div className="flex items-start justify-start gap-[0.625rem] text-[0.625rem]">
                    <p className=" font-black text-green-600">
                      • {group.onlineCount}
                    </p>
                    <p class=" font-black text-steelTeal-600">• 3</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-3">
              {
                !showJoinButton && (
                  <GroupCall groupId={group.id} groupName={group.name} />
                )
                // <AddFriend size={16} key="add" />
                //  <Image src={Enter} alt="Enter" className="h-4 w-4" />
              }

              {/* Join button logic */}
              {showJoinButton && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();

                    if (!isAuthenticated) {
                      setSelectedTab(0);
                      localStorage.setItem('activeTab', 0);
                      openModal(<Auth />);
                      return;
                    }
                    joinGroup.mutate({ groupId: group.id, action: 'join' });
                  }}
                  className="flex items-center justify-center gap-2 rounded-[10px] bg-red-500 px-2 py-1 text-[12px] font-semibold text-black-1000 transition duration-200 hover:bg-primary-900"
                >
                  Join
                </button>
              )}

              <div
                onClick={(e) => {
                  e.stopPropagation();
                  openGroupChat(group.id, group?.name);
                  openChatWindow();
                }}
              >
                {/* <Image src={GradientMessage} alt="Message" className="h-4 w-4" /> */}
                <MessageIcon className="h-5 w-5" />
              </div>

              <ChevronDown
                className={`h-6 w-6 transform text-steelTeal-200 transition-transform duration-300 ${
                  openIndex === index ? 'rotate-180' : 'rotate-0'
                }`}
              />
            </div>
          </div>

          {/* Accordion Content */}
          <div
            className={`grid transition-all duration-500 ease-in-out ${
              openIndex === index
                ? 'grid-rows-[1fr] opacity-100'
                : 'grid-rows-[0fr] opacity-0'
            }`}
          >
            <div className="overflow-hidden">
              <div className="grid grid-cols-3 gap-[0.625rem] px-[0.625rem] py-4 max-sm:grid-cols-1">
                {group?.members?.map((m) => (
                  <FriendsAccordion
                    bgColor="bg-erieBlack-100"
                    gradientClass="bg-transparent"
                    key={m.id}
                    profileImage={m?.user?.profileImage}
                    username={m.user.username}
                    userId={m.user?.userId}
                    currentStatus={m.user.currentStatus}
                    currentGamePlay={m.user.currentGamePlay}
                    actions={[
                      m.user.areFriends && (
                        <FriendCall
                          key="call"
                          userId={m.user?.userId}
                          user={m?.user}
                        />
                      ),
                      // <AddFriend size={16} key="add" />,
                      userDetails?.id != m.user?.userId && isAuthenticated && (
                        <button
                          key="msg"
                          onClick={() => {
                            setUserId(m.user?.userId);
                            setIsPrivateChatOpen(true);
                            if (searchUserName !== '') setSearchUserName('');
                            openChatWindow();
                          }}
                          className="cursor-pointer"
                        >
                          <MessageIcon size={20} />
                        </button>
                      ),
                    ]}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
