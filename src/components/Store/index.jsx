'use client';
import React, { useState } from 'react';
import { X } from 'lucide-react';
import TreasureIcon from '@/assets/icons/Treasure';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Common/Button/IconButton';
import RainTab from './Rain'; // Import the RainTab component`
import Buy from './Buy';
import TransactionsModal from '../TransactionsModal';
import Redeem from './Redeem';
import Tip from './Tip';

function StoreModal({ userDetails, currentActiveTab }) {
  const { clearModals, openModal } = useModalStore((state) => state);
  const [activeTab, setActiveTab] = useState(
    currentActiveTab ? currentActiveTab : 'rain',
  );

  const handleCloseModal = () => {
    console.log('closing');
    clearModals();
  };

  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div
        className={
          activeTab === 'buy'
            ? 'relative max-h-[90vh] w-full max-w-3xl'
            : 'relative max-h-[90vh] w-full max-w-xl'
        }
      >
        <div className="rounded-lg bg-maastrichtBlue-1000 p-2 shadow-lg">
          <div className="flex items-center justify-between p-2">
            <div className="flex items-center gap-3">
              <TreasureIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                Store
              </h3>
            </div>
            <div className="flex items-center gap-4">
              <button
                type="button"
                onClick={() =>
                  openModal(<TransactionsModal currentActiveTab={activeTab} />)
                }
                className="text-md py-2 text-white-1000 underline"
              >
                {capitalizeFirstLetter(activeTab)} Transactions
              </button>
              <IconButton
                onClick={() => handleCloseModal()}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          <div className="p-4">
            <div className="mb-4 flex items-center justify-center">
              <div className="gap-1 rounded-full bg-cetaceanBlue-1000 p-1 md:gap-4">
                {['Buy', 'Redeem', 'Rain', 'Tips'].map((tab) => (
                  <button
                    key={tab}
                    type="button"
                    className={`flex items-center justify-center rounded-full px-3 py-2 text-sm font-semibold ${activeTab === tab.toLowerCase() ? 'bg-oxfordBlue-1000 text-white-1000' : 'text-steelTeal-1000'}`}
                    onClick={() => setActiveTab(tab.toLowerCase())}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            {activeTab === 'buy' && <Buy />}
            {activeTab === 'redeem' && <Redeem />}
            {activeTab === 'rain' && <RainTab />}
            {activeTab === 'tips' && <Tip userDetails={userDetails} />}
          </div>
        </div>
      </div>
    </div>
  );
}

export default StoreModal;
