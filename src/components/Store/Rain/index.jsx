import React, { useState } from 'react';
import Select from 'react-select';
import Image from 'next/image';
import coinAC from '@/assets/images/stock-images/coin-ac.png';
import { useRainDrop } from '@/hooks/useRainDrop';
import useAuthStore from '@/store/useAuthStore';

const coinOptions = [{ value: 'AC', label: 'AC', image: coinAC }];

const playerOptions = [
  { value: 'VIP ONLY', label: 'VIP ONLY' },
  { value: 'ALL', label: 'ALL' },
];

const messageOptions = [
  { value: 'Good luck everyone!', label: 'Good luck everyone!' },
  { value: "Let's win!", label: "Let's win!" },
  { value: 'Go for it!', label: 'Go for it!' },
];

const customStyles = (bgColor) => {
  return {
    control: (provided) => ({
      ...provided,
      backgroundColor: bgColor ? bgColor : 'rgba(9, 42, 60, 1)',
      color: 'white',
      border: 'none',
      boxShadow: 'none',
      minHeight: '40px',
      padding: '10px 0px',
    }),
    singleValue: (provided) => ({
      ...provided,
      color: 'white',
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      color: 'white',
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: 'rgba(9, 42, 60, 1)',
      color: 'white',
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isFocused
        ? 'rgb(5, 20, 56)'
        : 'rgba(9, 42, 60, 1)',
      color: 'white',
      padding: 10,
      display: 'flex',
      alignItems: 'center',
      cursor: 'pointer',
    }),
  };
};

const formatOptionLabel = ({ label, image }) => (
  <div className="flex items-center font-semibold">
    <Image src={image} alt={label} className="mr-4 h-6 w-6" />
    {label}
  </div>
);

const SelectInput = ({
  label,
  value,
  onChange,
  options,
  formatOptionLabel,
  inputProps,
  errorMessage,
}) => (
  <div className="flex flex-col space-y-2">
    <label className="text-white text-sm font-semibold">{label}</label>
    <div className="flex rounded-md  bg-tiber-1000">
      <Select
        value={value}
        onChange={onChange}
        options={options}
        styles={customStyles()}
        formatOptionLabel={formatOptionLabel}
        className="bg-stealTeal-1000 w-1/4 text-sm tracking-wider"
        components={{ IndicatorSeparator: () => null }}
      />
      {inputProps && (
        <input
          {...inputProps}
          className=" text-white hide-arrows flex-grow bg-cetaceanBlue-1000  px-2 py-2 text-right text-sm font-semibold focus:outline-none"
        />
      )}
    </div>
    {errorMessage && (
      <span className="text-sm text-red-500">{errorMessage}</span>
    )}
  </div>
);

function RainTab() {
  const [coinType, setCoinType] = useState(coinOptions[0]);
  const [playerType, setPlayerType] = useState(playerOptions[0]);
  const [message, setMessage] = useState(messageOptions[0].value);
  const [amount, setAmount] = useState(0);
  const [playerNo, setPlayerNo] = useState(0);

  const [amountError, setAmountError] = useState('');
  const [playerNoError, setPlayerNoError] = useState('');

  const { sendRainDrop, isPending, isError, isSuccess, data, error } =
    useRainDrop();
  const { userDetails } = useAuthStore();

  const handleSendRainDrop = () => {
    setAmountError('');
    setPlayerNoError('');

    let hasError = false;

    if (amount <= 0) {
      setAmountError('Amount must be greater than zero.');
      hasError = true;
    }
    if (playerNo <= 0) {
      setPlayerNoError('Player number must be greater than zero.');
      hasError = true;
    }

    if (hasError) return;

    const data = {
      userId: userDetails?.userId,
      amountType: 0,
      amount: String(amount),
      playerNo,
      playerType: playerType.value.toLowerCase(),
      message,
    };

    sendRainDrop(data);
  };

  const handleAmountChange = (e) => {
    const value = Number(e.target.value);
    setAmount(value);
    if (value > 0) {
      setAmountError('');
    }
  };

  const handlePlayerNoChange = (e) => {
    const value = Number(e.target.value);
    setPlayerNo(value);
    if (value > 0) {
      setPlayerNoError('');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <label className="text-white text-sm font-semibold">Rain Amount</label>
        <div className="mt-0 flex rounded-md  bg-tiber-1000">
          <div
            className="flex w-1/4 items-center font-semibold"
            style={{
              minHeight: '40px',
              paddingTop: '16px',
              paddingBottom: '16px',
              paddingLeft: '8px',
            }}
          >
            <Image src={coinAC} alt={'AC'} className="mr-4 h-6 w-6" />
            AC
          </div>

          <input
            type="number"
            value={amount}
            onChange={handleAmountChange}
            placeholder=""
            className="text-white hide-arrows flex-grow bg-cetaceanBlue-1000  px-2 py-2 text-right text-sm font-semibold focus:outline-none"
          />
        </div>
        {amountError && (
          <span className="text-sm text-red-500">{amountError}</span>
        )}
      </div>
      <SelectInput
        label="Player Number"
        value={playerType}
        onChange={(selected) => setPlayerType(selected)}
        options={playerOptions}
        inputProps={{
          type: 'number',
          value: playerNo,
          onChange: handlePlayerNoChange,
          placeholder: '',
        }}
        errorMessage={playerNoError}
      />
      <div className="flex flex-col space-y-2">
        <label className="text-white text-sm font-semibold">Message</label>
        <Select
          value={messageOptions.find((option) => option.value === message)}
          onChange={(selected) => setMessage(selected.value)}
          options={messageOptions}
          styles={customStyles('rgba(9, 42, 60, 1)')}
          className="w-full cursor-pointer rounded-md  bg-tiber-1000 text-sm font-semibold"
          components={{ IndicatorSeparator: () => null }}
        />
      </div>
      <button
        disabled={isPending}
        onClick={handleSendRainDrop}
        type="submit"
        className="text-md w-full rounded-lg bg-primary-1000 py-2 font-semibold tracking-wide text-white-1000 transition-all duration-200 hover:bg-primary-700"
      >
        {isPending ? 'Loading...' : 'Pour Rain'}
      </button>
    </div>
  );
}

export default RainTab;
