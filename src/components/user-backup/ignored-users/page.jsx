// 'use client';

// import React from 'react';
// import Image from 'next/image';
// import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
// import useIgnoredUsers from '@/hooks/useIgnoredUsers';
// import useUserActions from '@/hooks/useUserActions';
// import UserImg from '../../../assets/images/demo-image/user-img.jpg';

// function IgnoredUsersPage() {
//   // const [selectedOption, setSelectedOption] = useState(null);
//   const { ignoredUsers, ignoredUsersLoading, refetch } = useIgnoredUsers();
//   const { unignoreUser } = useUserActions({
//     refetch,
//   });


//   const handleUnIgnore = (selectedUserId) => {
//     unignoreUser(selectedUserId);
//   };

//   return (
//     <div>
//       <div className="overflow-x-auto">
//         <table className="w-full">
//           <thead>
//             <tr className="border-b border-white-100">
//               <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold text-steelTeal-1000 max-sm:text-sm" />
//               <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold capitalize text-steelTeal-1000 max-sm:text-sm">
//                 nick name
//               </th>
//               <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold capitalize text-steelTeal-1000 max-sm:text-sm">
//                 user name
//               </th>
//               <th className="whitespace-nowrap px-2 py-4 text-left text-base font-bold capitalize text-steelTeal-1000 max-sm:text-sm">
//                 action
//               </th>
//             </tr>
//           </thead>
//           <tbody>
//             {ignoredUsers?.rows?.map((ignoredUser) => (
//               <tr key={ignoredUser?.userId}>
//                 <td className="whitespace-nowrap text-left text-sm font-normal text-white-1000">
//                   <Image
//                     src={ignoredUser?.relationUser?.profileImage || UserImg}
//                     width={10000}
//                     height={10000}
//                     className="h-[30px] w-[30px] rounded-full "
//                     alt="Banner"
//                   />
//                 </td>
//                 <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-white-1000 max-sm:text-sm">
//                   {ignoredUser?.relationUser?.nickname}
//                 </td>
//                 <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-white-1000 max-sm:text-sm">
//                   {ignoredUser?.relationUser?.username}
//                 </td>
//                 <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-scarlet-700 max-sm:text-sm">
//                   <PrimaryButtonOutline
//                     onClick={() =>
//                       handleUnIgnore(ignoredUser?.relationUser?.userId)
//                     }
//                   >
//                     Unignore
//                   </PrimaryButtonOutline>
//                 </td>
//               </tr>
//             ))}
//             {/* <tr>
//               <td className="whitespace-nowrap text-left text-sm font-normal text-white-1000">
//                 <Image
//                   src={UserImg}
//                   width={10000}
//                   height={10000}
//                   className="h-[30px] w-[30px] rounded-full "
//                   alt="Banner"
//                 />
//               </td>
//               <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-white-1000 max-sm:text-sm">
//                 Gordy
//               </td>
//               <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-white-1000 max-sm:text-sm">
//                 user7890
//               </td>
//               <td className="whitespace-nowrap px-2 py-4 text-left text-base font-normal text-scarlet-700 max-sm:text-sm">
//                 <PrimaryButtonOutline>Unignore</PrimaryButtonOutline>
//               </td>
//             </tr> */}
//           </tbody>
//         </table>
//       </div>
//     </div>
//   );
// }

// export default IgnoredUsersPage;
