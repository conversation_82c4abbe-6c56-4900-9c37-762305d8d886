// 'use client';
// import React, { useState, useEffect } from 'react';
// import CloseIcon from '@/assets/icons/CloseIcon';
// import IconButton from '@/components/Common/Button/IconButton';
// import PrimaryButton from '@/components/Common/Button/PrimaryButton';
// import { useUpdateEmail, useVerifyEmail } from '@/reactQuery/authQuery';
// import { useForm, Controller } from 'react-hook-form';
// import { yupResolver } from '@hookform/resolvers/yup';
// import { toast } from 'react-hot-toast';
// import useAuthStore from '@/store/useAuthStore';
// import MainLoader from '@/components/Common/Loader/MainLoader';

// const initialFormState = {
//   currentPassword: '',
//   newPassword: '',
//   confirmNewPassword: '',
// };

// function EmailPage() {
//   const { userDetails } = useAuthStore((state) => state);

//   const [email, setEmail] = useState();
//   const [isEmailVerified, setEmailVerified] = useState(false);
//   const [otp, setOtp] = useState();
//   const [errors, setErrors] = useState({});

//   useEffect(() => {
//     if (userDetails) {
//       setEmailVerified(userDetails.isEmailVerified);
//       setEmail(userDetails.email);
//     }
//   }, [userDetails]);

//   const mutation = useUpdateEmail({
//     onSuccess: (response) => {
//       if (response?.data?.success) {
//         toast.success(response?.data?.message);
//       }
//     },
//     onError: (error) => {
//       const message = error.response?.data?.errors?.[0]?.description;
//       if (message) toast.error(message);
//     },
//   });

//   const onUpdateEmail = () => {
//     if (email) mutation.mutate({ email: email });
//     else {
//       setErrors({ email: 'Please enter a email' });
//     }
//   };

//   const mutationVerify = useVerifyEmail({
//     onSuccess: (response) => {
//       if (response?.data?.success) toast.success(response?.data?.message);
//       setEmailVerified(true);
//       setEmail(response?.data?.user?.email);
//     },
//     onError: (error) => {
//       const message = error.response?.data?.errors?.[0]?.description;
//       if (message) toast.error(message);
//     },
//   });

//   const onVerifyEmail = () => {
//     if (email && otp)
//       mutationVerify.mutate({
//         email: email,
//         token: otp,
//       });
//     else {
//       if (!email && !otp)
//         setErrors({
//           email: 'Please enter a email',
//           otp: 'Please enter a valid otp',
//         });
//       else if (!email) setErrors({ email: 'Please enter a email' });
//       else if (!otp) setErrors({ otp: 'Please enter a valid otp' });
//     }
//   };

//   if (!userDetails) {
//     return <MainLoader className="w-32" />;
//   }

//   return (
//     <div>
//       {!isEmailVerified ? (
//         <div>
//           <div className="mt-4">
//             <div className=" grid grid-cols-1 gap-6 desktop:gap-4 uppertab:gap-3">
//               <div className="">
//                 <label className="mb-1 block text-base font-normal text-steelTeal-1000">
//                   Email
//                   <p className="text-sm text-steelTeal-1000">
//                     (you will receive bonus email per week)
//                   </p>
//                 </label>
//                 <div className="relative w-full">
//                   <input
//                     value={email}
//                     type="email"
//                     name="email"
//                     placeholder="Email Address"
//                     onChange={(e) => setEmail(e.target.value)}
//                     className={`text-white w-full rounded-md bg-maastrichtBlue-1000 p-3 text-base font-normal ${errors.newPassword ? 'border border-solid border-primary-1000' : 'border border-solid border-maastrichtBlue-1000'}  focus:border focus:border-solid focus:border-borderColor-100`}
//                   />

//                   {email && email.length > 0 && (
//                     <IconButton className="absolute right-2 top-1/2 -translate-y-1/2">
//                       <CloseIcon
//                         className="h-5 w-5  cursor-pointer fill-primary-1000"
//                         alt="Toggle visibility"
//                         onClick={() => setEmail('')}
//                       />
//                     </IconButton>
//                   )}
//                 </div>
//                 {errors.email && !email && (
//                   <p className="mt-1 text-xs text-red-500">{errors.email}</p>
//                 )}
//               </div>
//             </div>
//             <div className="mt-[30px] flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
//               <PrimaryButton
//                 type="submit"
//                 onClick={() => onUpdateEmail()}
//                 isLoading={mutation?.isLoading}
//               >
//                 Send
//               </PrimaryButton>
//             </div>
//           </div>

//           <div className="mt-8 border-t border-solid border-steelTeal-1000 pt-6">
//             <div className=" grid grid-cols-1 gap-6 desktop:gap-4 uppertab:gap-3">
//               <div className="">
//                 <label className="mb-1 block text-base font-normal text-steelTeal-1000">
//                   Verification Code
//                   <p className="text-sm text-steelTeal-1000">
//                     (haven't receive? please check junk email)
//                   </p>
//                 </label>
//                 <div className="relative w-full">
//                   <input
//                     value={otp}
//                     type="text"
//                     name="otp"
//                     placeholder=""
//                     onChange={(e) => setOtp(e.target.value)}
//                     className={`text-white w-full rounded-md bg-maastrichtBlue-1000 p-3 text-base font-normal ${errors.newPassword ? 'border border-solid border-primary-1000' : 'border border-solid border-maastrichtBlue-1000'}  focus:border focus:border-solid focus:border-borderColor-100`}
//                   />

//                   {otp && otp.length > 0 && (
//                     <IconButton className="absolute right-2 top-1/2 -translate-y-1/2">
//                       <CloseIcon
//                         className="h-5 w-5  cursor-pointer fill-primary-1000"
//                         alt="Toggle visibility"
//                       />
//                     </IconButton>
//                   )}
//                 </div>
//                 {errors.otp && !otp && (
//                   <p className="mt-1 text-xs text-red-500">{errors.otp}</p>
//                 )}
//               </div>
//             </div>
//             <div className="mt-[30px] flex flex-col items-center justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
//               <p className="text-center text-sm text-steelTeal-1000">
//                 If you don't receive the email, you can check it in spam
//               </p>
//               <PrimaryButton
//                 type="submit"
//                 onClick={() => onVerifyEmail()}
//                 isLoading={mutationVerify?.isLoading}
//               >
//                 Submit
//               </PrimaryButton>
//             </div>
//           </div>
//         </div>
//       ) : (
//         <div className="mt-4">
//           <div className="grid grid-cols-1 gap-6 desktop:gap-4 uppertab:gap-3">
//             <div className="">
//               <label className="mb-1 block text-base font-normal text-white-1000">
//                 Current Email
//                 <p className="text-sm text-steelTeal-1000">
//                   (Please check weekly airdrop email every weekend. Don't miss
//                   your bonus.)
//                 </p>
//               </label>
//               <div className="relative w-full">
//                 <input
//                   type="email"
//                   name="newPassword"
//                   value={email}
//                   disabled={true}
//                   placeholder="Vine*****@****"
//                   className={`text-white pointer-events-none w-full rounded-md border border-solid border-tiber-1000 bg-tiber-1000 p-[11px_12px] text-base font-normal`}
//                 />
//               </div>
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }

// export default EmailPage;
