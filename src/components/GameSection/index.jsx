'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import MainLoader from '@/components/Common/Loader/MainLoader';
import PlayCard from '@/components/PlayCard';
import CarouselSection from '@/components/Common/CarouselSection';
import useGeneralStore from '@/store/useGeneralStore';

function GameSection({
  title,
  icon,
  games,
  isLoading,
  isCarousel = false,
  emptyMessage,
  onGameClick,
  renderGame,
  loadMoreButton,
  viewAllPath,
}) {
  const router = useRouter();
  const { openMenu, openChat } = useGeneralStore();

  if (isLoading && !isCarousel) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        <MainLoader className="w-32" />
      </div>
    );
  }

  if (!games || games.length === 0) {
    return emptyMessage && !isCarousel ? (
      <div className="flex h-96 items-center justify-center px-5">
        {emptyMessage}
      </div>
    ) : null;
  }

  return (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 shadow-container max-lg:px-0">
      {!isCarousel && (
        <div className="flex items-center justify-between">
          <div className="mb-4 flex items-center gap-1">
            {icon}
            <h6 className="bg-cardBorderGradient bg-clip-text text-sm font-semibold uppercase text-transparent">
              {title}
            </h6>
          </div>
        </div>
      )}

      {isCarousel ? (
        <CarouselSection
          title={title}
          titleIcon={icon}
          navigationStyle="default"
          showViewAll={!!viewAllPath}
          showNavigation
          viewAll="View All"
          onViewAllClick={() => viewAllPath && router.push(viewAllPath)}
          cardGap="gap-0"
          containerMargin="mt-3 mb-10"
          titleClassName="text-[14px] font-[600] text-white-1000"
          emblaOptions={{ loop: false, align: 'start', slidesToScroll: 'auto' }}
        >
          {games.map((game) => {
            const g = renderGame(game);
            return (
              <div
                key={g.id}
                className={`flex-[0_0_calc(100%/6)] pl-3  max-3xl:flex-[0_0_calc(100%/5)]
          max-xxl:flex-[0_0_calc(100%/4)]
         max-xl:flex-[0_0_calc(100%/5)]         
        max-lg:flex-[0_0_calc(100%/4.3)] 
        max-lmd:flex-[0_0_calc(100%/4)]
        max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]
        ${openMenu && openChat ? 'max-xxl:flex-[0_0_calc(100%/5)] max-2xl:flex-[0_0_calc(100%/4)]' : openMenu ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]' : openChat ? 'max-2xl:flex-[0_0_calc(100%/3.5)]' : ' max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/5)]'}
        `}
              >
                <PlayCard
                  gameId={g.id}
                  gameImage={g.image}
                  gameName={g.name}
                  isFavorite={g.isFavorite}
                  providerName={g.provider}
                  sizeVariant="fixed"
                  onClick={() => onGameClick(g.provider, g.name)}
                />
              </div>
            );
          })}
        </CarouselSection>
      ) : (
        <div
          className={`grid grid-cols-6 gap-4 max-3xl:grid-cols-5 max-xxl:grid-cols-4 max-xxl:gap-[.5625rem] max-2xl:grid-cols-4  max-xl:grid-cols-5  max-lg:grid-cols-4 max-lmd:grid-cols-4 max-md:grid-cols-4 max-sm:grid-cols-2
          ${openMenu && openChat ? 'max-2xl:flex-grid-cols-4  max-xxl:grid-cols-5' : openMenu ? 'max-xxl:grid-cols-6  max-2xl:grid-cols-6' : openChat ? 'max-2xl:grid-cols-4' : ' max-xxl:grid-cols-6 max-2xl:grid-cols-5'}
                
            `}
        >
          {games.map((game) => {
            const g = renderGame(game);
            return (
              <PlayCard
                key={g.id}
                gameId={g.id}
                gameImage={g.image}
                gameName={g.name}
                isFavorite={g.isFavorite}
                providerName={g.provider}
                sizeVariant="fixed"
                onClick={() => onGameClick(g.provider, g.name)}
              />
            );
          })}
        </div>
      )}

      {!isCarousel && loadMoreButton}
    </section>
  );
}

export default GameSection;
