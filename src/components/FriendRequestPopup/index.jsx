'use client';

import { Circle<PERSON>he<PERSON>, CircleX } from 'lucide-react';
import React from 'react';

const FriendRequestPopup = ({ text, onConfirm, onCancel }) => {
  return (
    <div
      className="absolute right-0 top-full z-50 mt-2 flex flex-nowrap items-center gap-5 whitespace-nowrap rounded-lg bg-black-1000 px-4 py-3 text-steelTeal-200 shadow-lg 
     before:absolute before:right-[115px] before:top-0 before:-translate-y-full before:border-x-8 before:border-b-8 before:border-black-1000 before:border-x-transparent before:content-['']
    "
    >
      <span className="text-sm font-medium">{text}</span>
      <button
        onClick={onConfirm}
        className="flex items-center gap-1 text-sm font-semibold"
      >
        <CircleCheck className="text-green-600" /> Yes
      </button>
      <button
        onClick={onCancel}
        className="flex items-center gap-1 text-sm font-semibold"
      >
        <CircleX className="text-scarlet-900" /> Go Back
      </button>
    </div>
  );
};

export default FriendRequestPopup;
