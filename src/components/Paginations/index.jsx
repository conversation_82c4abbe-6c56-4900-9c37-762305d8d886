import React, { useMemo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

export default function Pagination({ currentPage, totalPages, onPageChange }) {
  const visiblePages = 4;

  const pagesToShow = useMemo(() => {
    let start = Math.max(1, currentPage - Math.floor(visiblePages / 2));
    let end = start + visiblePages - 1;

    if (end >= totalPages) {
      end = totalPages;
      start = Math.max(1, end - visiblePages + 1);
    }

    const pages = [];
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  }, [currentPage, totalPages]);

  return (
    <div className="flex items-center justify-center gap-2">
      {/* Previous */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`flex h-10 w-10 items-center justify-center rounded bg-primaryBorder ${currentPage === 1 ? 'cursor-not-allowed opacity-30' : 'hover:bg-inputBgColor'
          }`}
      >
        <ChevronLeft size={18} />
      </button>

      {/* Page Numbers */}
      {pagesToShow.map((page) => (
        <button
          key={page}
          onClick={() => onPageChange(page)}
          className={`flex h-10 w-10 items-center justify-center rounded border text-lg transition-colors
      ${currentPage === page
              ? 'bg-steelTeal-800 text-white border-steelTeal-400 font-bold'
              : 'bg-transparent text-white hover:bg-inputBgColor border-primaryBorder'
            }`}
        >
          {page}
        </button>
      ))}

      {/* Ellipsis if not near the end */}
      {pagesToShow[pagesToShow.length - 1] < totalPages && (
        <>
          <span className="text-white">...</span>
          <button
            onClick={() => onPageChange(totalPages)}
            className={`flex h-10 w-10 items-center justify-center rounded border text-lg hover:bg-inputBgColor text-white`}
          >
            {totalPages}
          </button>
        </>
      )}

      {/* Next */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`flex h-10 w-10 items-center justify-center rounded bg-primaryBorder ${currentPage === totalPages ? 'cursor-not-allowed opacity-30' : 'hover:bg-inputBgColor'
          }`}
      >
        <ChevronRight size={18} />
      </button>
    </div>
  );
}
