'use client';
import React, { useEffect, useRef, useCallback } from 'react';
import { useGetIncomingMessagesQuery } from '@/reactQuery/chatWindowQuery';
import MessageIcon from '@/assets/icons/MessageIcon';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import FriendRequestItem from '../ChatWindow/RecentChat/Accordion/FriendRequestItem';

const IncomingMessages = ({ search, isAuthenticated, setTotalMsg }) => {
    const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore((state) => state);

    const {
        data,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        isLoading: isMessagesLoading,
        refetch,
    } = useGetIncomingMessagesQuery({
        params: { search, limit: 20 },
        enabled: !!isAuthenticated,
    });

    const allMessages = data?.pages || [];

    const lastMessageRef = useRef(null);
    const observerRef = useRef(null);

    useEffect(() => {
        const totalMessages = data?.unreadCount || 0;
        setTotalMsg(totalMessages);
    }, [data, setTotalMsg]);

    const handleIntersection = useCallback(
        (entries) => {
            const [entry] = entries;
            if (entry.isIntersecting && hasNextPage && !isFetchingNextPage) {
                fetchNextPage();
            }
        },
        [hasNextPage, isFetchingNextPage, fetchNextPage],
    );

    useEffect(() => {
        if (observerRef.current) {
            observerRef.current.disconnect();
        }

        observerRef.current = new IntersectionObserver(handleIntersection, {
            root: null,
            rootMargin: '100px',
            threshold: 0.1,
        });

        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, [handleIntersection]);

    useEffect(() => {
        const currentObserver = observerRef.current;
        const currentRef = lastMessageRef.current;

        if (currentRef && currentObserver) {
            currentObserver.observe(currentRef);
        }

        return () => {
            if (currentRef && currentObserver) {
                currentObserver.unobserve(currentRef);
            }
        };
    }, [allMessages]);

    const openChat = (userId) => {
        setUserId(userId);
        setIsPrivateChatOpen(true);
    };

    return (
        <div className="space-y-2">
            {isMessagesLoading && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                    Loading messages...
                </p>
            )}
            {!isMessagesLoading && allMessages.length === 0 && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                    No Messages Found
                </p>
            )}
            {allMessages.map((message, idx) => {
                const isLastMessage = idx === allMessages.length - 1;
                return (
                    <div
                        key={message.id || idx}
                        ref={isLastMessage ? lastMessageRef : null}
                        className="min-h-[40px]"
                    >
                        <FriendRequestItem
                            req={message}
                            customIcon={
                                <div className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-1 md:px-3 py-2 gap-3">
                                    {!message.hasRead && (
                                        <span className="flex h-7 w-fit capitalize cursor-pointer items-center justify-center rounded-lg px-3 py-1.5 text-xs font-semibold transition-all duration-300 active:scale-90 !text-cetaceanBlue-1000 bg-TintGoldGradient mt-1">
                                            New
                                        </span>
                                    )}
                                    <div
                                        className="cursor-pointer self-center"
                                        onClick={() => openChat(message.id)}
                                    >
                                        <MessageIcon className="h-[20px] w-[20px] text-steelTeal-1000" />
                                    </div>
                                </div>
                            }
                            onClick={() => openChat(message.id)}
                        />
                    </div>
                );
            })}
            {isFetchingNextPage && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                    Loading more messages...
                </p>
            )}
        </div>
    );
};

export default IncomingMessages;