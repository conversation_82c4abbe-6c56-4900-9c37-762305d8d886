'use client';
import { useState } from 'react';
import Draggable from 'react-draggable';
import useDraggableStore from '@/store/useDraggableGamePopup';


export default function DraggableGamePopup({ initialPosition = { x: 0, y: 0 } }) {
  const [position, setPosition] = useState(initialPosition);
  const { component, gameName,isOpen, closeDraggablePopup } = useDraggableStore();

  const handleStop = (e, data) => {
    setPosition({ x: data.x, y: data.y });
  };

  // **Only render when isOpen is true and component exists**
  if (!isOpen || !component) return null;

  return (
    <Draggable position={position} onStop={handleStop} handle=".popup-header">
      <div className="fixed top-0 z-[9999] bg-black-800 border border-gray-700 rounded-xl shadow-xl w-[600px] max-w-full">
        <div className="popup-header flex justify-between items-center bg-gray-900 cursor-move p-3 rounded-t-xl">
          <h3 className="text-white font-semibold">{gameName}</h3>
          <button
            onClick={closeDraggablePopup}
            onTouchStart={closeDraggablePopup}
            className="text-white font-bold text-xl leading-none px-2"
          >
            &times;
          </button>
        </div>
        <div >{component}</div>
      </div>
    </Draggable>
  );
}
