'use client';

import React, { useEffect, useState, useCallback } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import PromotionImg1 from '../../assets/images/lobby/vip-img.webp';
import PromotionImg2 from '../../assets/images/lobby/new-img.webp';
import PromotionImg3 from '../../assets/images/lobby/coin-img.webp';
import RouletterImg from '../../assets/webp/roulette.webp';
import PrimaryButton from '../Common/Button/PrimaryButton';
import useSocialLogin from '@/hooks/useSocialLogin';
import useModalStore from '@/store/useModalStore';
import useAuthTab from '@/store/useAuthTab';
import Auth from '../Auth';
import useGeneralStore from '@/store/useGeneralStore';
import useAuthStore from '@/store/useAuthStore';
import { useRouter } from 'next/navigation';
import WalletModal from '@/app/account/WalletModal/WalletModal';
import useAuthModalStore from '@/store/useAuthModalStore';

function HeroSlider() {
  const { openChat, setOpenChat, setOpenMenu, openMenu } = useGeneralStore();
  const { openModal } = useModalStore((state) => state);
  const { openModal: AuthModalOpen } = useAuthModalStore();
  const { userDetails } = useAuthStore();
  const { setSelectedTab } = useAuthTab();
  const [welcomeBanner, setWelcomeBanner] = useState(false);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
    align: 'start',
  });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const router = useRouter();

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  // Autoplay logic
  useEffect(() => {
    if (!emblaApi) return;

    const autoplay = () => {
      if (emblaApi.canScrollNext()) {
        emblaApi.scrollNext();
      } else {
        emblaApi.scrollTo(0);
      }
    };

    const autoplayInterval = setInterval(autoplay, 400000); // Adjust the interval time as needed

    return () => {
      clearInterval(autoplayInterval);
    };
  }, [emblaApi]);

  const handleAuth = useCallback(
    (tab) => {
      setSelectedTab(tab);
      localStorage.setItem('activeTab', tab);
      AuthModalOpen(<Auth />);
    },
    [setSelectedTab, AuthModalOpen],
  );

  useEffect(() => {
    const isShow = sessionStorage.getItem('welcomeShown');
    if (isShow === 'true') {
      setWelcomeBanner(true);
    }
  }, []);

  const {
    isLoading: socialLoading,
    handleGoogleLogin: socialGoogleLogin,
    handleFacebookLogin: socialFacebookLogin,
    handleDiscordLogin: socialDiscordLogin,
    handleTwitchLogin: socialTwitchLogin,
  } = useSocialLogin();

  return (
    <div
      className="embla mb-6 overflow-hidden md:mb-9 max-sm:mb-2"
      ref={emblaRef}
    >
      <div
        className={`
          embla__container
          [&>.embla-slide]:bg-charcoalBlack-700 -ml-2
          flex
          md:-ml-4 [&>.embla-slide]:relative
          [&>.embla-slide]:min-w-0
          [&>.embla-slide]:overflow-hidden
          [&>.embla-slide]:pl-2
          md:[&>.embla-slide]:pl-4

          ${openChat
            ? `
                [&>.embla-slide]:flex-[0_0_100%]
                sm:[&>.embla-slide]:flex-[0_0_50%]
                lg:[&>.embla-slide]:flex-[0_0_33.3333%]
              `
            : `
                [&>.embla-slide]:flex-[0_0_94%]
                md:[&>.embla-slide]:flex-[0_0_50%]
                lg:[&>.embla-slide]:flex-[0_0_33.3333%]
              `
          }
        `}
      >
        <div className="embla__slide embla-slide text-white max-w-[340px] md:max-w-full">
          <div className="relative inline-flex w-full overflow-hidden">
            <Image
              src={PromotionImg2}
              className="h-full w-full rounded-3xl object-cover "
            />
            <div className="absolute inset-0 flex h-full w-full flex-col justify-between p-4 3xl:p-6">
              <div>
                <p className="text-[13px] font-semibold uppercase text-steelTeal-200 sm:text-xs 3xl:text-[13px]">
                  FIND YOUR New Favorite
                </p>
                <h1 className="-mt-0.5 w-full max-w-[16.9375rem] font-obviously text-lg font-bold uppercase !leading-none sm:text-lg md:mt-0.5 lg:text-xl 3xl:mt-1.5 3xl:text-[1.5rem]">
                  {welcomeBanner ? (
                    <>
                      Welcome <br />
                      {userDetails?.firstName ?? userDetails?.username}
                    </>
                  ) : (
                    <>
                      Newest <br /> Releases
                    </>
                  )}
                </h1>
              </div>
              <div onClick={()=>router.push('/category/new-releases')}>
              <PrimaryButton
                variant="secondary"
                className="mt-1 !h-7 !w-fit !text-xs"
              >
                New Releases
              </PrimaryButton>
              </div>
            </div>
          </div>
        </div>
        <div className="embla__slide embla-slide text-white max-w-[340px] md:max-w-full">
          <div className="relative inline-flex w-full overflow-hidden">
            <Image
              src={PromotionImg1}
              className="h-full w-full rounded-3xl object-cover"
            />
            <div className="absolute inset-0 flex h-full w-full flex-col justify-between p-4 3xl:p-6">
              <div>
                <p className="text-[13px] font-semibold uppercase text-steelTeal-200 sm:text-xs 3xl:text-[13px]">
                  GET ALL-STAR TREATMENT
                </p>
                <h1 className="mt-0.5 w-full max-w-[16.9375rem] font-obviously text-lg font-bold uppercase !leading-none sm:text-lg md:mt-0.5 lg:text-xl 3xl:mt-1.5 3xl:text-[1.5rem]">
                  great{' '}
                  <span className="bg-textGradient bg-clip-text text-transparent">
                    vip
                  </span>{' '}
                  <br /> perks!
                </h1>
              </div>
              <PrimaryButton
                variant="secondary"
                className="mt-1 !h-7 !w-fit !text-xs"
                onClick={() => router.push('/vip')}
              >
                View VIP
              </PrimaryButton>
            </div>
          </div>
        </div>

        <div className="embla__slide embla-slide text-white max-w-[340px] md:max-w-full">
          <div className="relative inline-flex w-full overflow-hidden">
            <Image
              src={PromotionImg3}
              className="h-full w-full rounded-3xl object-cover "
            />
            <div className="absolute inset-0 flex h-full w-full flex-col justify-between p-4 3xl:p-6">
              <div>
                <p className="text-[13px] font-semibold uppercase text-steelTeal-200 sm:text-xs 3xl:text-[13px]">
                  CRYPTO ACCEPTED
                </p>
                <h1 className="mt-0.5 w-full max-w-[16.9375rem] font-obviously text-lg font-bold uppercase !leading-none sm:text-lg md:mt-0.5 lg:text-xl 3xl:mt-1.5 3xl:text-[1.5rem]">
                  FastEst <br /> Payouts
                </h1>
              </div>
              <PrimaryButton
                variant="secondary"
                className="mt-1 !h-7 !w-fit !text-xs"
                onClick={ ()=>openModal(<WalletModal />)}
              >
                Deposit Now
              </PrimaryButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HeroSlider;
