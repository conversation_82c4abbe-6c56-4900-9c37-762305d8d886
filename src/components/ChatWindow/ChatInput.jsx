import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { memo, useCallback } from 'react';
import IconButton from '../Common/Button/IconButton';
import PrimaryButton from '../Common/Button/PrimaryButton';
import SettingIcon from '@/assets/icons/Setting';
import GIFIcon from '@/assets/icons/GIF';
import EmojiIcon from '@/assets/icons/Emoji';
import Uk from '@/assets/webp/uk.webp';
import TextIcon from '@/assets/icons/TextIcon';
import CloseIcon from '@/assets/icons/CloseIcon';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import dynamic from 'next/dynamic';
import InfoIcon from '@/assets/icons/Info';
import useAuthModalStore from '@/store/useAuthModalStore';
const Auth = dynamic(() => import('../Auth'), { ssr: false });

export const ChatInput = memo(
  ({
    message,
    gifMessage,
    setGifMessage,
    showSuggestions,
    selectedSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    livePlayersCount,
    section,
    isAuthenticated,
    groupId = null,
    receiverId = null,
    emojiButtonRef = null,
    gifButtonRef = null,
  }) => {
    const router = useRouter();
    const { setSelectedTab } = useAuthTab((state) => state);
    const { openModal } = useAuthModalStore();
    const handleSetting = useCallback(() => {
      // router.push('/user/prefrences');
    }, [router]);

    return (
      <div className="relative bg-steelTeal-800 px-[0.625rem] py-[.4375rem] shadow-chatInputShadow">
        {showSuggestions && (
          <ul className="absolute bottom-28 mt-1 max-h-60 w-11/12 overflow-y-auto rounded-md bg-maastrichtBlue-1000 shadow-lg">
            {section === 'PublicChat' &&
              tagSuggestion?.map((suggestion, index) => (
                <button
                  type="button"
                  key={suggestion?.username}
                  ref={(el) => (suggestionsRef.current[index] = el)}
                  className={`block w-full cursor-pointer px-4 py-2 ${
                    index === selectedSuggestion ? 'text-white bg-blue-500' : ''
                  }`}
                  onClick={() => selectSuggestion(index)}
                >
                  {suggestion?.username}
                </button>
              ))}
          </ul>
        )}
        <div className="flex items-center rounded-[0.625rem] bg-cetaceanBlue-1000 px-1">
          {gifMessage && (
            <div className="mb-2 flex items-center">
              <Image
                src={gifMessage}
                width={10000}
                height={10000}
                className="w-[5.875rem] max-w-full rounded-lg"
                alt="Selected GIF"
              />
              <IconButton
                onClick={() => setGifMessage(null)}
                className="ml-2 h-6 w-6 min-w-6"
              >
                <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          )}

          <input
            ref={inputRef}
            className={`hover:bg-cetaceanBlue-400 h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 pl-5 placeholder:text-steelTeal-500 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
            placeholder={
              isAuthenticated
                ? 'Type your message'
                : section === 'GroupChat'
                  ? 'Join group to message'
                  : 'Login to chat'
            }
            value={message}
            readOnly={!isAuthenticated}
            onChange={handleInputChange}
            onKeyDown={(e) => handleKeyDown({ e, groupId, receiverId })}
            style={{ opacity: gifMessage ? '0' : '1' }}
          />

          {/* <SendHorizontal
            onClick={handleSendMessage}
            className="h-8 w-8 cursor-pointer rounded-[0.313rem] bg-primary-1000 p-1"
          /> */}
          {isAuthenticated ? (
            <PrimaryButton
              className="h-[1.875rem] !rounded-[.3125rem] bg-primary-900 px-[0.313rem] py-[.125rem] text-[.8125rem] !font-bold"
              onClick={handleSendMessage}
            >
              Send
            </PrimaryButton>
          ) : section === 'GroupChat' ? (
            <button
              className="flex h-[1.875rem] items-center justify-center gap-2 rounded-[.3125rem] bg-red-500 px-3 text-[14px] font-semibold capitalize leading-none text-black-1000 transition duration-200 hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-700"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedTab(0);
                localStorage.setItem('activeTab', '0');
                openModal(<Auth />);
              }}
            >
              Join
            </button>
          ) : (
            <PrimaryButton
              className="h-[1.875rem] !rounded-[.3125rem] bg-primary-900 px-[0.313rem] py-[.125rem] text-[.8125rem] !font-bold"
              onClick={handleSendMessage}
            >
              Send
            </PrimaryButton>
          )}
        </div>
        {section == 'PublicChat' ? (
          <div className="mt-[0.625rem] flex items-center justify-between gap-4">
            <div className="flex items-center gap-[0.313rem] text-[0.813rem] font-normal leading-none text-white-1000">
              <span className="mb-1 inline-block h-[0.313rem] w-[0.313rem] rounded-full bg-green-1000" />
              <span>Online: {livePlayersCount || '-'}</span>
            </div>

            <div className="flex items-center justify-end gap-[0.313rem]">
              <button type="button">
                <Image src={Uk} alt="Uk" className="h-4 w-4" />
              </button>
              <button
                onClick={handleSetting}
                type="button"
                className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              >
                <InfoIcon className="h-4 w-4 fill-steelTeal-200 transition-all duration-300 group-hover:fill-white-1000" />
              </button>
              <button
                ref={gifButtonRef}
                type="button"
                onClick={handleGifPickerToggle}
                className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              >
                <GIFIcon className="h-4 w-4 fill-steelTeal-200 transition-all duration-300 group-hover:fill-white-1000" />
              </button>
              <button
                id="emoji-btn"
                ref={emojiButtonRef}
                onClick={handleEmojiPickerToggle}
                type="button"
                className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              >
                <EmojiIcon className="h-[1.0625rem] w-[1.0625rem] fill-steelTeal-200 transition-all duration-300 group-hover:fill-white-1000" />
              </button>
              <p className="text-white  ml-5 text-[.8125rem] font-normal">
                200
              </p>
            </div>
          </div>
        ) : (
          <div className="mt-[0.625rem] flex items-center justify-between gap-4">
            <div className="flex items-center justify-center gap-3">
              <button type="button">
                <Image src={Uk} alt="Uk" className="h-4 w-4" />
              </button>
              <button
                onClick={handleSetting}
                type="button"
                className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              >
                <InfoIcon className="h-4 w-4 fill-steelTeal-200 transition-all duration-300 group-hover:fill-white-1000" />
              </button>
            </div>
            <div className="flex items-center justify-end gap-[0.313rem]">
              <button
                ref={gifButtonRef}
                type="button"
                onClick={handleGifPickerToggle}
                className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              >
                <GIFIcon className="h-4 w-4 fill-steelTeal-200 transition-all duration-300 group-hover:fill-white-1000" />
              </button>
              <button
                ref={emojiButtonRef}
                onClick={handleEmojiPickerToggle}
                type="button"
                className="group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              >
                <EmojiIcon className="h-[1.0625rem] w-[1.0625rem] fill-steelTeal-200 transition-all duration-300 group-hover:fill-white-1000" />
              </button>
              <div className="flex items-center gap-1">
                <TextIcon />
                <p className="text-white  text-[.8125rem] font-normal">200</p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  },
);
ChatInput.displayName = 'ChatInput';
