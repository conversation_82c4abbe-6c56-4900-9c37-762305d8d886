import CallIcon from '@/assets/icons/CallIcon';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import IconButton from '@/components/Common/Button/IconButton';
import useGroupCall from '@/hooks/useGroupCall';
import usePrivateCall from '@/hooks/usePrivateCall';
import { Mic } from 'lucide-react';
import { MicOff } from 'lucide-react';
import React from 'react';
import { rtc } from '@/hooks/usePrivateCall';
import useCallStore from '@/store/useCallStore';
const PrivateChatCallIcons = ({ user, isMic = false }) => {
  const { isCallActive: isGroupCallActive } = useGroupCall();
  const { isMuted, setIsMuted, initiateCall, disconnectCall } =
    usePrivateCall();
  const { toggleMuted, setToggleMuted } = useCallStore();

  const toggleMicrophone = async () => {
    if (rtc.localAudioTrack) {
      if (toggleMuted) {
        await rtc.localAudioTrack.setEnabled(true);
      } else {
        await rtc.localAudioTrack.setEnabled(false);
      }
      setToggleMuted(!toggleMuted);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {!isGroupCallActive && user?.areFriends && (
        <>
          {!isCallActive ? (
            <IconButton
              onClick={() => initiateCall(user?.userId)}
              className="h-6 w-6"
            >
              <CallIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
            </IconButton>
          ) : (
            // )
            callId == user?.userId && (
              <>
                <IconButton onClick={disconnectCall} className="h-6 w-6">
                  <DisconnectIcon
                    className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                    fill="red"
                  />
                </IconButton>
                {isMic && (
                  <IconButton onClick={toggleMicrophone} className="h-6 w-6">
                    {toggleMuted ? (
                      <MicOff className="h-5 w-5 text-red-500 transition-all duration-300 hover:text-red-600" />
                    ) : (
                      <Mic className="h-5 w-5 text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                    )}
                  </IconButton>
                )}
              </>
            )
          )}
        </>
      )}

      <IconButton onClick={closePrivateChatModal} className="h-6 w-6">
        <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
      </IconButton>
    </div>
  );
};

export default PrivateChatCallIcons;
