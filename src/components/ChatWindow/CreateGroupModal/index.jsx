'use client';

// Add useEffect to check required fields on form submission attempt
import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import IconButton from '@/components/Common/Button/IconButton';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateGroupSchema } from '@/schemas/CreateGroupSchema';
import useModalStore from '@/store/useModalStore';
import { X, ChevronDown, Camera } from 'lucide-react';
import { useGroupChatMutation } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import MultiSelect from '@/components/ChatSelect';
import UploadImg from '@/assets/webp/upload-img.webp';
import SwitchButton from '../Friends/SwitchButton';
import GroupProfileModal from '@/components/Common/Modal/GroupProfileModal';
import GroupBannerModal from '@/components/Common/Modal/GroupBannerModal';

// File validation constants
const MAX_FILE_SIZE = {
  profile: 300 * 1024, // 300 KB
  banner: 1 * 1024 * 1024, // 1 MB
};

const ACCEPTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

function ToggleSwitch({ label, name, register, setValue }) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-white text-xs font-semibold">{label}</span>
      <label className="relative inline-flex cursor-pointer items-center ">
        <input
          value={'true'}
          type="checkbox"
          {...register(name)}
          className="peer hidden"
          onChange={(e) => setValue(e.target.checked)}
        />
        <div className="relative h-6 w-11 rounded-full bg-gray-600 after:absolute after:left-1 after:top-1 after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-gray-300 after:transition-all after:content-[''] peer-checked:bg-red-700 peer-checked:after:translate-x-5 peer-focus:ring-4 peer-focus:ring-steelTeal-300" />
      </label>
    </div>
  );
}

function CreateGroupModal() {
  const [groupImage, setGroupImage] = useState(null);
  const [groupImagePreview, setGroupImagePreview] = useState(null);
  const [bannerImage, setBannerImage] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);
  const [profileError, setProfileError] = useState('');
  const [bannerError, setBannerError] = useState('');
  const [profileRequired, setProfileRequired] = useState(false);
  const [bannerRequired, setBannerRequired] = useState(false);
  const [onlyAdminCanAddMembers, setOnlyAdminCanAddMembers] = useState(false);
  const [onlyAdminCanUpdateGroupDetails, setOnlyAdminCanUpdateGroupDetails] =
    useState(false);
  const [selectedDefaultProfile, setSelectedDefaultProfile] = useState(null);
  const [selectedDefaultBanner, setSelectedDefaultBanner] = useState(null);
  const modalContentRef = useRef(null);
  const { openModal } = useModalStore();
  const scrollToTop = () => {
    if (modalContentRef.current) {
      modalContentRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };
  const casinoBanners = [
    '/assets/group-photos/casino-banner-1.webp',
    '/assets/group-photos/casino-banner-2.webp',
    '/assets/group-photos/casino-banner-3.webp',
    '/assets/group-photos/casino-banner-4.webp',
    '/assets/group-photos/casino-banner-5.webp',
    '/assets/group-photos/casino-banner-6.webp',
    '/assets/group-photos/casino-banner-7.webp',
    '/assets/group-photos/casino-banner-8.webp',
    '/assets/group-photos/casino-banner-9.webp',
    '/assets/group-photos/casino-banner-10.webp',
    '/assets/group-photos/casino-banner-11.webp',
    '/assets/group-photos/casino-banner-12.webp',
    '/assets/group-photos/casino-banner-13.webp',
    '/assets/group-photos/casino-banner-14.webp',
  ];

  const sportsBanners = [
    '/assets/group-photos/sports-banner-1.webp',
    '/assets/group-photos/sports-banner-2.webp',
    '/assets/group-photos/sports-banner-3.webp',
    '/assets/group-photos/sports-banner-4.webp',
    '/assets/group-photos/sports-banner-5.webp',
    '/assets/group-photos/sports-banner-6.webp',
  ];

  const genericBanners = [
    '/assets/group-photos/generic-banner-1.webp',
    '/assets/group-photos/generic-banner-2.webp',
    '/assets/group-photos/generic-banner-3.webp',
    '/assets/group-photos/generic-banner-4.webp',
    '/assets/group-photos/generic-banner-5.webp',
    '/assets/group-photos/generic-banner-6.webp',
    '/assets/group-photos/generic-banner-7.webp',
    '/assets/group-photos/generic-banner-8.webp',
    '/assets/group-photos/generic-banner-9.webp',
    '/assets/group-photos/generic-banner-10.webp',
    '/assets/group-photos/generic-banner-11.webp',
    '/assets/group-photos/generic-banner-12.webp',
    '/assets/group-photos/generic-banner-13.webp',
    '/assets/group-photos/generic-banner-14.webp',
    '/assets/group-photos/generic-banner-15.webp',
    '/assets/group-photos/generic-banner-16.webp',
    '/assets/group-photos/generic-banner-17.webp',
    '/assets/group-photos/generic-banner-18.webp',
    '/assets/group-photos/generic-banner-19.webp',
    '/assets/group-photos/generic-banner-20.webp',
  ];

  useEffect(() => {
    if (profileRequired && (groupImage || selectedDefaultProfile)) {
      setProfileRequired(false);
      setProfileError('');
    }

    if (bannerRequired && (bannerImage || selectedDefaultBanner)) {
      setBannerRequired(false);
      setBannerError('');
    }
  }, [groupImage, bannerImage, profileRequired, bannerRequired]);

  useEffect(() => {
    if (profileError || bannerError) {
      scrollToTop();
    }
  }, [profileError, bannerError]);

  const {
    register,
    watch,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(CreateGroupSchema),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      // onlyAdminCanCall: false,
      onlyAdminCanAddMembers: false,
      // onlySubAdminCanAddMembers: false,
      onlyAdminCanUpdateGroupDetails: true,
      groupType: 'public',
      profile: null,
      groupBanner: null,
    },
  });
  const groupType = watch('groupType');
  const [selectedUsers, setSelectedUsers] = useState([]); // Manage selected users' IDs

  const queryClient = useQueryClient();
  const { closeModal } = useModalStore();

  const groupChatMutation = useGroupChatMutation({
    onSuccess: (response) => {
      toast.success('Group created successfully!');
      closeModal();
      queryClient.invalidateQueries({ queryKey: ['GET_GROUP_LIST_QUERY'] });
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign up';
      toast.error(message);
    },
  });

  const validateImageFile = (file, setErrorFn, type, isRequired = false) => {
    setErrorFn('');

    if (!file) {
      if (isRequired) {
        setErrorFn('This field is required');
        return false;
      }
      return true;
    }

    // Type check
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      setErrorFn('Only JPG, JPEG, PNG and WebP formats are supported');
      return false;
    }

    // Size check by type
    if (type === 'profile') {
      if (file.size > 300 * 1024) {
        setErrorFn('Profile image must be ≤ 300KB');
        return false;
      }
      if (file.size < 100 * 1024) {
        setErrorFn('Profile image must be at least 100KB (too low quality)');
        return false;
      }
    }

    if (type === 'banner') {
      if (file.size > 1024 * 1024) {
        setErrorFn('Banner image must be ≤ 1MB');
        return false;
      }
      if (file.size < 100 * 1024) {
        setErrorFn('Banner image should be at least 100KB (too low quality)');
        return false;
      }
    }

    // Dimension / ratio check
    return new Promise((resolve) => {
      const img = new window.Image();
      img.src = URL.createObjectURL(file);
      img.onload = () => {
        const { width, height } = img;

        if (type === 'profile' && Math.abs(width - height) > 10) {
          setErrorFn('Profile image must be square (1:1 ratio)');
          return resolve(false);
        }

        if (type === 'banner') {
          const ratio = width / height;
          if (ratio < 3.8 || ratio > 4.2) {
            setErrorFn('Banner should have ~4:1 aspect ratio (e.g. 1584x396)');
            return resolve(false);
          }
        }

        resolve(true);
      };
    });
  };


  // const handleGroupImageChange = (e) => {
  //   const file = e.target.files[0];
  //   if (file) {
  //     if (validateImageFile(file, setProfileError)) {
  //       setGroupImage(file);
  //       const reader = new FileReader();
  //       reader.onloadend = () => {
  //         setGroupImagePreview(reader.result);
  //       };
  //       reader.readAsDataURL(file);
  //     } else {
  //       // Reset preview if validation fails
  //       setGroupImage(null);
  //       setGroupImagePreview(null);
  //     }
  //   }
  // };

  const handleGroupImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const isValid = await validateImageFile(file, setProfileError, 'profile');
      if (isValid) {
        setGroupImage(file);
        setSelectedDefaultProfile(null);

        const reader = new FileReader();
        reader.onloadend = () => {
          setGroupImagePreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        setGroupImage(null);
        setGroupImagePreview(null);
      }
    }
  };

  const handleBannerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (validateImageFile(file, setBannerError, 'banner')) {
        setBannerImage(file);
        setSelectedDefaultBanner(null);
        const reader = new FileReader();
        reader.onloadend = () => {
          setBannerPreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Reset preview if validation fails
        setBannerImage(null);
        setBannerPreview(null);
      }
    }
  };

  const fetchImageAsBlob = async (imagePath) => {
    try {
      const response = await fetch(imagePath);
      if (!response.ok) throw new Error('Failed to fetch image');
      const blob = await response.blob();
      return blob;
    } catch (error) {
      console.error('Error fetching image as Blob:', error);
      throw error;
    }
  };

  const onSubmit = async (data) => {
    console.log('🚀 ~ onSubmit ~ data:', data);
    try {
      let hasErrors = false;
      if (!groupImage && !selectedDefaultProfile) {
        setProfileError('Profile image is required');
        setProfileRequired(true);
        hasErrors = true;
      }
      if (!bannerImage && !selectedDefaultBanner) {
        setBannerError('Banner image is required');
        setBannerRequired(true);
        hasErrors = true;
      }
      let isProfileValid = true;
      if (groupImage) {
        isProfileValid = validateImageFile(groupImage, setProfileError, false);
      }

      let isBannerValid = true;
      if (bannerImage) {
        isBannerValid = validateImageFile(bannerImage, setBannerError, false);
      }

      if (!isProfileValid || !isBannerValid) {
        hasErrors = true;
      }
      if (hasErrors) {
        scrollToTop();
        return;
      }
      // Proceed with form submission
      const formData = new FormData();
      formData.append('groupName', data.groupName);
      formData.append('groupDescription', data.groupDescription);
      formData.append('onlyAdminCanAddMembers', onlyAdminCanAddMembers);
      formData.append(
        'onlyAdminCanUpdateGroupDetails',
        onlyAdminCanUpdateGroupDetails,
      );
      formData.append('groupType', data.groupType);
      selectedUsers.forEach((user, index) => {
        formData.append(`members[${index}]`, user);
      });
      if (groupImage) {
        formData.append('profile', groupImage);
      } else if (selectedDefaultProfile) {
        const profileBlob = await fetchImageAsBlob(selectedDefaultProfile);
        formData.append('profile', profileBlob, `profile-${Date.now()}.png`);
      }
      if (bannerImage) {
        formData.append('groupBanner', bannerImage);
      } else if (selectedDefaultBanner) {
        const bannerBlob = await fetchImageAsBlob(selectedDefaultBanner);
        formData.append('groupBanner', bannerBlob, `banner-${Date.now()}.png`);
      }

      await groupChatMutation.mutateAsync(formData);
    } catch (error) {
      toast.error('Failed to create group!');
    }
  };

  const bannersArray = Array.from(
    { length: 20 },
    (_, i) => `/assets/group-photos/profile-${i + 1}.webp`
  );

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="scrollbar-hide relative max-h-[calc(100%-120px)] w-full  max-w-[42.5rem]   px-3   py-4 md:min-w-[42.5rem] md:px-7">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="sticky top-0 z-10 flex items-center justify-between bg-steelTeal-800 px-7 py-4 pb-2">
            <h4 className="text-white text-[.9375rem] font-semibold">
              Create Group
            </h4>
            <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
              <X className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
            </IconButton>
          </div>
          <div
            ref={modalContentRef}
            className="scrollbar-hide max-h-[calc(100vh-200px)] overflow-y-auto  p-4 pt-2"
          >
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="">
                <div className="mb-2.5 flex items-center gap-4">
                  <div>
                    <label
                      htmlFor="group-img"
                      className={`relative block cursor-pointer ${profileRequired ? 'rounded-full ring-2 ring-red-500' : ''}`}
                    >
                      {groupImagePreview ? (
                        <Image
                          src={groupImagePreview}
                          width={80}
                          height={80}
                          alt="Group Profile"
                          className="bg-white h-20 w-20 rounded-full object-cover object-center"
                        />
                      ) : (
                        <div className="flex h-20 w-20 items-center justify-center rounded-full border-[1px] border-steelTeal-1000 bg-black-1000">
                          <Camera className="h-8 w-8 " />
                        </div>
                      )}
                      <input
                        type="file"
                        id="group-img"
                        onChange={handleGroupImageChange}
                        className="hidden"
                        accept="image/png, image/jpeg, image/jpg, image/webp"
                      />
                    </label>
                    {profileError && (
                      <p className="mt-1 max-w-[80px] text-center text-xs text-red-500">
                        {profileError}
                      </p>
                    )}
                  </div>
                  <div className="grow">
                    <label
                      className={`relative mb-1 flex gap-2 text-xs font-semibold`}
                    >
                      Group name
                    </label>
                    <input
                      type="text"
                      {...register('groupName')}
                      placeholder="Enter group name"
                      className={`w-full rounded-md border ${errors.groupName ? 'border-red-500' : 'border-solid border-richBlack-1000'} text-white bg-inputBgColor px-3 py-2 placeholder-gray-400 placeholder:text-[15px] placeholder:font-semibold
                      focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none
                      `}
                    />
                    {errors.groupName && (
                      <p className="mt-0.5 text-xs text-red-500">
                        {errors.groupName.message}
                      </p>
                    )}
                  </div>
                </div>
                <button
                  type="button"
                  className="mb-3 text-xs font-medium text-gray-400 hover:cursor-pointer"
                  onClick={() =>
                    openModal(
                      <GroupProfileModal
                        banners={bannersArray}
                        onSelect={(banner) => {
                          setGroupImagePreview(banner);
                          setSelectedDefaultProfile(banner);
                          setGroupImage(null);
                          setBannerError('');
                          setBannerRequired(false);
                        }}
                      />
                    )
                  }
                >
                  Select Default
                </button>

                {/* Banner Image Upload Section */}

                <div className="mb-4">
                  <div className="mb-1 flex items-center justify-between">
                    <label
                      className={`relative flex gap-2 text-xs font-semibold`}
                    >
                      Group Banner
                    </label>
                    <div
                      className="hover:text-white text-sm text-gray-400 transition-colors hover:cursor-pointer"
                      onClick={() =>
                        openModal(
                          <GroupBannerModal
                            casinoBanners={casinoBanners}
                            sportsBanners={sportsBanners}
                            genericBanners={genericBanners}
                            onSelect={(banner) => {
                              setBannerPreview(banner);
                              setSelectedDefaultBanner(banner);
                              setBannerImage(null);
                              setBannerError('');
                              setBannerRequired(false);
                            }}
                          />,
                        )
                      }
                    >
                      Select New
                    </div>
                  </div>
                  <div
                    className={`relative w-full rounded-[0.625rem] border 
                      ${bannerRequired ? 'border-red-500 ring-2 ring-red-500' : 'border-richBlack-1000'} 
                      border-[2px] border-dashed border-steelTeal-620 bg-inputBgColor`}
                  >
                    {bannerPreview ? (
                      <label
                        htmlFor="banner-img"
                        className="relative block aspect-[21/9] w-full cursor-pointer overflow-hidden rounded-lg"
                      >
                        <Image
                          src={bannerPreview}
                          alt="Banner Preview"
                          fill
                          className="rounded-lg object-cover"
                        />
                        <input
                          type="file"
                          id="banner-img"
                          onChange={handleBannerImageChange}
                          className="hidden"
                          accept="image/png, image/jpeg, image/jpg, image/webp"
                        />
                      </label>
                    ) : (
                      <div className="flex flex-col items-center justify-center p-6">
                        {/* Upload Section */}
                        <label
                          htmlFor="banner-img"
                          className="flex cursor-pointer flex-col items-center justify-center"
                        >
                          <Image
                            src={UploadImg}
                            className="mx-auto mb-4 w-9"
                            alt="Upload"
                          />
                          <p className="text-[.9375rem] font-semibold text-white-360">
                            Upload Banner Image
                          </p>
                          <input
                            type="file"
                            id="banner-img"
                            onChange={handleBannerImageChange}
                            className="hidden"
                            accept="image/png, image/jpeg, image/jpg, image/webp"
                          />
                        </label>

                        <p className="my-1 text-xs font-medium text-gray-400">
                          Or
                        </p>

                        <p
                          onClick={() =>
                            openModal(
                              <GroupBannerModal
                                casinoBanners={casinoBanners}
                                sportsBanners={sportsBanners}
                                genericBanners={genericBanners}
                                onSelect={(banner) => {
                                  setBannerPreview(banner);
                                  setSelectedDefaultBanner(banner);
                                  setBannerImage(null);
                                  setBannerError('');
                                  setBannerRequired(false);
                                }}
                              />,
                            )
                          }
                          className="cursor-pointer text-xs font-semibold text-white-360 underline underline-offset-4"
                        >
                          Select from Defaults
                        </p>
                      </div>
                    )}
                  </div>
                  {bannerError && (
                    <p className="mt-1 text-xs text-red-500">{bannerError}</p>
                  )}
                </div>

                {/* Group Type Selection */}
                <div className="mb-4">
                  <label
                    className={`relative mb-1 flex gap-2 text-xs font-semibold`}
                  >
                    Group Type
                  </label>
                  <div className="relative">
                    <select
                      className="text-white w-full appearance-none rounded-md border border-solid border-richBlack-1000 bg-inputBgColor px-3 py-2 focus:border-steelTeal-1000  focus:outline-none"
                      {...register('groupType')}
                      name="groupType"
                    >
                      <option value="private">Private</option>
                      <option value="public">Public</option>
                    </select>
                    <div className="text-white pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                      <ChevronDown className="h-4 w-4" />
                    </div>
                  </div>
                  {/* <div className="theme-select-wrap mb-5 w-full">
                    <label className="mb-1 flex gap-2 text-sm font-semibold text-white-1000">
                      Group type
                    </label>
                    <ThemeSelect
                      options={options}
                      value={selected}
                      register={{ register }}
                    
                      onChange={setSelected}
                      placeholder="Select Label"
                      className="theme-select-wrap placeholder:!font-semibold"
                    />
                  </div> */}
                </div>

                <div className="mb-4">
                  <label
                    className={`relative mb-1 flex gap-2 text-xs font-semibold`}
                  >
                    Group Members
                  </label>
                  <MultiSelect
                    className="chatMulti-select bg-inputBgColor"
                    classNamePrefix="chatMulti-inner-select"
                    onChange={setSelectedUsers}
                    value={selectedUsers}
                    isSearchable={true}
                    placeholder="Search and select friends..."
                  />
                </div>

                <div className="mt-3">
                  <label
                    className={`relative mb-1 flex gap-2 text-xs font-semibold`}
                  >
                    Group Description
                  </label>
                  <textarea
                    {...register('groupDescription')}
                    placeholder="Enter group description"
                    className={`w-full resize-none rounded-md border ${errors.groupDescription ? 'border-red-500' : 'border-solid border-richBlack-1000'} text-white bg-inputBgColor p-3 placeholder-gray-400 placeholder:text-[15px] placeholder:font-semibold
                        focus:border-steelTeal-1000 
                    `}
                  />
                  {errors.groupDescription && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.groupDescription.message}
                    </p>
                  )}
                </div>
                <div className="mt-4 flex flex-col gap-3">
                  {/* <ToggleSwitch
                    label="Only Admin Can Call"
                    name="onlyAdminCanCall"
                    register={register}
                  /> */}
                  <SwitchButton
                    leftLabel="Only Admin Can Add Members"
                    name="onlyAdminCanAddMembers"
                    register={register}
                    setValue={setOnlyAdminCanAddMembers}
                    showRightLabel={false}
                    onKnobColor="bg-textGradient"
                    offKnobColor="bg-erieBlack-300"
                    onBgColor="bg-transparent"
                    offBgColor="bg-textGradient"
                    switchWidth="w-[2.125rem]"
                    switchHeight="h-5"
                    knobSize="h-[.875rem] w-[.875rem]"
                  />

                  {/* <ToggleSwitch
                    label="Only Admin Can Add Members"
                    name="onlyAdminCanAddMembers"
                    register={register}
                    setValue={setOnlyAdminCanAddMembers}
                  /> */}
                  {/* <ToggleSwitch
                    label="Only Subadmins Can Add Members"
                    name="onlySubAdminCanAddMembers"
                    register={register}
                  /> */}
                  {groupType === 'private' && (
                    // <ToggleSwitch
                    //   label="Only Admin Can Update Group Details"
                    //   name="onlyAdminCanUpdateGroupDetails"
                    //   register={register}
                    //   setValue={setOnlyAdminCanUpdateGroupDetails}
                    // />
                    <SwitchButton
                      leftLabel="Only Admin Can Update Group Details"
                      name="onlyAdminCanUpdateGroupDetails"
                      register={register}
                      setValue={setOnlyAdminCanUpdateGroupDetails}
                      showRightLabel={false}
                      onKnobColor="bg-textGradient"
                      offKnobColor="bg-erieBlack-300"
                      onBgColor="bg-transparent"
                      offBgColor="bg-textGradient"
                      switchWidth="w-[2.125rem]"
                      switchHeight="h-5"
                      knobSize="h-[.875rem] w-[.875rem]"
                    />
                  )}
                </div>
              </div>
              <div className="mt-6 flex justify-center">
                <button
                  type="submit"
                  disabled={
                    groupChatMutation.isPending || profileError || bannerError
                  }
                  className="rounded-[10px] bg-TintGoldGradient px-3 py-2 text-[15px] font-semibold text-black-1000"
                >
                  {groupChatMutation.isPending ? (
                    <div className="flex items-center gap-2">
                      <span>Creating...</span>
                    </div>
                  ) : (
                    'Create Group'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateGroupModal;
