'use client';

import React, { useEffect, useRef, useState } from 'react';
import { X } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import IconButton from '@/components/Common/Button/IconButton';
import CameraImg from '@/assets/images/svg-images/camera.svg';
import Image from 'next/image';
import InputField from '@/components/Common/InputField';
import UploadImg from '@/assets/webp/upload-img.webp';
import ThemeSelect from '@/components/Common/InputField/ThemeSelect';
import SwitchButton from '../Friends/SwitchButton';
import { useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import { useGroupChatMutation } from '@/reactQuery/chatWindowQuery';
import { zodResolver } from '@hookform/resolvers/zod';
import { CreateGroupSchema } from '@/schemas/CreateGroupSchema';
import { Camera } from 'lucide-react';
import MultiSelect from '@/components/ChatSelect';
import { AlertCircle } from 'lucide-react';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];
function CreateGroupModal() {
  const { isOpen, handleCloseModal, closeModal, components } = useModalStore();
  const options = [
    { value: 'Public', label: 'Public' },
    { value: 'Private', label: 'Private' },
  ];
  const [selected, setSelected] = React.useState(null);
  const [groupImage, setGroupImage] = useState(null);
  const [groupImagePreview, setGroupImagePreview] = useState(null);
  const [bannerImage, setBannerImage] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);
  const [profileError, setProfileError] = useState('');
  const [bannerError, setBannerError] = useState('');
  const [profileRequired, setProfileRequired] = useState(false);
  const [bannerRequired, setBannerRequired] = useState(false);

  const modalContentRef = useRef(null);

  const scrollToTop = () => {
    if (modalContentRef.current) {
      modalContentRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    if (profileRequired && groupImage) {
      setProfileRequired(false);
      setProfileError('');
    }

    if (bannerRequired && bannerImage) {
      setBannerRequired(false);
      setBannerError('');
    }
  }, [groupImage, bannerImage, profileRequired, bannerRequired]);

  useEffect(() => {
    if (profileError || bannerError) {
      scrollToTop();
    }
  }, [profileError, bannerError]);

  const {
    register,
    watch,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(CreateGroupSchema),
    defaultValues: {
      groupName: '',
      groupDescription: '',
      // onlyAdminCanCall: false,
      onlyAdminCanAddMembers: false,
      // onlySubAdminCanAddMembers: false,
      onlyAdminCanUpdateGroupDetails: true,
      groupType: 'public',
      profile: null,
      groupBanner: null,
    },
  });
  const groupType = watch('groupType');
  const [selectedUsers, setSelectedUsers] = useState([]); // Manage selected users' IDs

  const queryClient = useQueryClient();

  const groupChatMutation = useGroupChatMutation({
    onSuccess: (response) => {
      toast.success('Group created successfully!');
      queryClient.invalidateQueries({ queryKey: ['GET_GROUP_LIST_QUERY'] });
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign up';
      toast.error(message);
    },
  });

  const validateImageFile = (file, setErrorFn, isRequired = false) => {
    // Reset any previous errors
    setErrorFn('');

    // Check if file exists
    if (!file) {
      if (isRequired) {
        setErrorFn('This field is required');
        return false;
      }
      return true;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      setErrorFn('File size should be less than 5MB');
      return false;
    }

    // Check file type
    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      setErrorFn('Only JPG, JPEG, PNG and WebP formats are supported');
      return false;
    }

    return true;
  };

  const handleGroupImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (validateImageFile(file, setProfileError)) {
        setGroupImage(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setGroupImagePreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Reset preview if validation fails
        setGroupImage(null);
        setGroupImagePreview(null);
      }
    }
  };

  const handleBannerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (validateImageFile(file, setBannerError)) {
        setBannerImage(file);
        const reader = new FileReader();
        reader.onloadend = () => {
          setBannerPreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Reset preview if validation fails
        setBannerImage(null);
        setBannerPreview(null);
      }
    }
  };

  const onSubmit = async (data) => {
    try {
      let hasErrors = false;

      if (!groupImage) {
        setProfileError('Profile image is required');
        setProfileRequired(true);
        hasErrors = true;
      }
      if (!bannerImage) {
        setBannerError('Banner image is required');
        setBannerRequired(true);
        hasErrors = true;
      }

      const isProfileValid = validateImageFile(
        groupImage,
        setProfileError,
        true,
      );
      const isBannerValid = validateImageFile(
        bannerImage,
        setBannerError,
        true,
      );

      if (!isProfileValid || !isBannerValid) {
        hasErrors = true;
      }

      if (selectedUsers.length === 0) {
        toast.error('Please select at least one member for the group');
        hasErrors = true;
      }

      if (hasErrors) {
        scrollToTop();
        return;
      }

      // Proceed with form submission
      const formData = new FormData();

      formData.append('groupName', data.groupName);
      formData.append('groupDescription', data.groupDescription);
      formData.append(
        'onlyAdminCanAddMembers',
        String(data.onlyAdminCanAddMembers),
      );
      formData.append(
        'onlyAdminCanUpdateGroupDetails',
        String(data.onlyAdminCanUpdateGroupDetails),
      );
      formData.append('groupType', data.groupType);

      selectedUsers.forEach((user, index) => {
        formData.append(`members[${index}]`, user);
      });

      if (bannerImage) {
        formData.append('groupBanner', bannerImage);
      }

      if (groupImage) {
        formData.append('profile', groupImage);
      }

      groupChatMutation.mutate(formData);
    } catch (error) {
      toast.error('Failed to create group!');
    }
  };
  return (
    <div className="relative w-full max-w-[38.25rem] p-0 md:p-4">
      <div className="relative flex max-h-[90vh] flex-col overflow-hidden rounded-lg bg-steelTeal-800">
        {/* Header */}
        <div className="sticky top-0 z-10 flex items-center justify-between bg-steelTeal-800 px-7 py-4 pb-2">
          <h4 className="text-white text-[.9375rem] font-semibold">
            Create Group
          </h4>
          <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
            <X className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
          </IconButton>
        </div>
        <div
          ref={modalContentRef}
          className="max-h-[calc(100vh-200px)] overflow-y-auto p-4 pt-2 max-xl:pb-16"
        >
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Scrollable Content */}
            <div className="overflow-y-auto px-7 pb-6 pt-2 max-sm:px-4">
              {/* Group Name */}
              <div className="mb-4 flex items-center gap-4">
                <div className="bg-black flex h-[5.625rem] w-[5.625rem] min-w-[5.625rem] cursor-pointer items-center justify-center rounded-full border border-white-350">
                  <label
                    htmlFor="group-img"
                    className={`relative block cursor-pointer ${profileRequired ? 'rounded-full ring-2 ring-red-500' : ''}`}
                  >
                    {groupImagePreview ? (
                      <Image
                        src={groupImagePreview}
                        width={80}
                        height={80}
                        alt="Group Profile"
                        className="bg-white h-20 w-20 rounded-full object-cover object-center"
                      />
                    ) : (
                      <div className="flex h-20 w-20 items-center justify-center rounded-full  border-steelTeal-1000">
                        <Image
                          src={CameraImg}
                          alt="CameraImg"
                          className="w-[2.5rem]"
                        />
                      </div>
                    )}
                    <input
                      type="file"
                      id="group-img"
                      onChange={handleGroupImageChange}
                      className="hidden"
                      accept="image/png, image/jpeg, image/jpg, image/webp"
                    />
                  </label>
                  {profileError && (
                    <p className="mt-1 max-w-[80px] text-center text-xs text-red-500">
                      {profileError}
                    </p>
                  )}
                </div>
                <InputField
                  type="text"
                  name="userName"
                  placeholder="Enter Group Name"
                  label="Group Name"
                  color="text-white-1000"
                  className="rounded-[0.625rem] bg-inputBgColor "
                  register={register}
                  registerName={'groupName'}
                />
              </div>
              {/* Cover Image */}
              <div className="relative mb-5">
                <label className="text-white mb-1 block text-sm">
                  Cover Image
                </label>
                <div className="mb-5 flex h-[9.375rem] cursor-pointer flex-col items-center justify-center rounded-md border-[2px] border-dashed border-steelTeal-620 bg-inputBgColor">
                  <div
                    className={`relative h-32 w-full overflow-hidden rounded-lg border ${bannerRequired ? 'border-red-500 ring-2 ring-red-500' : 'border-richBlack-1000'} bg-black-1000`}
                  >
                    {bannerPreview ? (
                      <Image
                        src={bannerPreview}
                        alt="Banner Preview"
                        layout="fill"
                        objectFit="cover"
                        className="h-full w-full"
                      />
                    ) : (
                      <div className="relative z-[1]">
                        <Image
                          src={UploadImg}
                          className="mx-auto mb-4 w-9"
                          alt="Upload"
                        />
                        <p className="text-[.9375rem] font-semibold text-white-360">
                          Upload Banner Image
                        </p>
                      </div>
                    )}
                    <input
                      type="file"
                      id="banner-img"
                      onChange={handleBannerImageChange}
                      className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
                      accept="image/png, image/jpeg, image/jpg, image/webp"
                    />
                  </div>
                  {bannerError && (
                    <p className="mt-1 text-xs text-red-500">{bannerError}</p>
                  )}
                  {/* <input
                    type="file"
                    className="absolute left-0 top-0 z-[2] h-full w-full cursor-pointer opacity-0"
                  /> */}
                </div>
              </div>
              {/* Group Type */}
              <div className="theme-select-wrap mb-5 w-full">
                <label className="mb-1 flex gap-2 text-sm font-semibold text-white-1000">
                  Group type
                </label>
                <ThemeSelect
                  options={options}
                  value={selected}
                  onChange={setSelected}
                  placeholder="Select Label"
                  className="theme-select-wrap placeholder:!font-semibold"
                />
              </div>
              {/* Group Members */}
              <div className="mb-5">
                <label className="mb-1 text-base font-normal text-steelTeal-1000">
                  Group Members <span className="text-red-500">*</span>
                </label>
                <MultiSelect
                  className="chatMulti-select"
                  classNamePrefix="chatMulti-inner-select"
                  onChange={setSelectedUsers}
                  value={selectedUsers}
                  isSearchable={true}
                  placeholder="Search and select friends..."
                />
                {selectedUsers.length === 0 && (
                  <p className="mt-1 flex items-center text-xs text-yellow-500">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    Please select at least one member
                  </p>
                )}
              </div>
              {/* <div className="mb-5">
                <InputField
                  type="text"
                  name="userName"
                  placeholder="Invite Friends"
                  label="Group Members"
                  color="text-white-1000"
                  className="rounded-[0.625rem] bg-inputBgColor placeholder:!text-white-350"
                  register={register}
                  registerName={'groupName'}
                />
              </div> */}
              {/* Group Description */}
              <div>
                <label className="mb-1 flex gap-2 text-sm font-semibold text-white-1000">
                  Group Description
                </label>
                <textarea
                  placeholder="Enter Group Description"
                  rows="3"
                  className="text-white w-full resize-none rounded-[0.625rem] border border-transparent bg-inputBgColor px-3 py-2 placeholder:!text-white-350 focus:border-steelTeal-1000 focus:outline-none"
                />
              </div>
              {/* Switch Buttons */}
              <div className="">
                <SwitchButton
                  leftLabel="Only Admin Can Add Members "
                  showRightLabel={false}
                  onKnobColor="bg-textGradient"
                  offKnobColor="bg-erieBlack-300"
                  onBgColor="bg-transparent"
                  offBgColor="bg-textGradient"
                  switchWidth="w-[2.125rem]"
                  switchHeight="h-5"
                  knobSize="h-[.875rem] w-[.875rem]"
                />
              </div>
              <div className="pb-3">
                <SwitchButton
                  leftLabel="Allow Self-Play Slots In Connected Play"
                  showRightLabel={false}
                  onKnobColor="bg-textGradient"
                  offKnobColor="bg-erieBlack-300"
                  onBgColor="bg-transparent"
                  offBgColor="bg-textGradient"
                  switchWidth="w-[2.125rem]"
                  switchHeight="h-5"
                  knobSize="h-[.875rem] w-[.875rem]"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-center">
              <PrimaryButton
                type="submit"
                disabled={
                  groupChatMutation.isPending || profileError || bannerError
                }
              >
                {groupChatMutation.isPending ? (
                  <div className="flex items-center gap-2">
                    <span>Creating...</span>
                  </div>
                ) : (
                  'Create Group'
                )}
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default CreateGroupModal;
