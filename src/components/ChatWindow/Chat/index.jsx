'use client';
import ChatAvatar from '@/components/ChatAvatar';
import useAuthStore from '@/store/useAuthStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { formatTo12Hour, isValidURL } from '@/utils/helper';
import { ChevronLeft } from 'lucide-react';
import Image from 'next/image';
import FriendCall from '../Friends/FriendCall';
import { useCallback, useEffect, useRef } from 'react';
import { useUpdateMsgRequest } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

const Chat = () => {
    const { setIsPrivateChatOpen, recipientUser, privateChat } = usePrivateChatStore();
    const { userDetails } = useAuthStore();
    const prevPrivateChatLength = useRef(0); 
    const prevRecipientId = useRef(null); 
    const queryClient = useQueryClient();

    console.log('🚀 ~ Chat ~ recipientUser, privateChat:', recipientUser, privateChat);

    const mutationMsgRequest = useUpdateMsgRequest({
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['GET_INCOMING_MESSAGE_LIST'] });
        },
        onError: (error) => {
            toast.error(error.response.data.errors.map((e) => e.description));
        },
    });

    const closePrivateChatModal = useCallback(() => {
        setIsPrivateChatOpen(false);
        prevPrivateChatLength.current = 0; 
        prevRecipientId.current = null; 
    }, [setIsPrivateChatOpen]);

    useEffect(() => {
        const hasRecipientChanged = recipientUser?.recipientId !== prevRecipientId.current;
        const hasPrivateChatChanged = privateChat?.length !== prevPrivateChatLength.current;

        if (
            recipientUser?.recipientId &&
            privateChat?.length > 0 &&
            (recipientUser.recipientId === privateChat[0]?.userId || recipientUser.recipientId === privateChat[0]?.recipientId) &&
            (hasRecipientChanged || hasPrivateChatChanged)
        ) {
            mutationMsgRequest.mutate({
                id: recipientUser.recipientId,
            });
            prevRecipientId.current = recipientUser.recipientId;
            prevPrivateChatLength.current = privateChat.length;
        }
    }, [recipientUser, privateChat, mutationMsgRequest]);

    return (
        <>
            <div className="flex items-center justify-between rounded-sm bg-erieBlack-300 p-1 px-2">
                <div className="flex items-center">
                    <button onClick={closePrivateChatModal}>
                        <ChevronLeft />
                    </button>
                    <div className="flex items-center justify-center gap-1">
                        <div className="h-[1.875rem] w-[1.875rem] overflow-hidden rounded-full">
                            <ChatAvatar
                                profileImage={recipientUser?.recipientProfileImage}
                                userName={recipientUser?.recipientUsername}
                                imageClassName="h-full w-full rounded-full object-cover"
                                avatarSize={30}
                            />
                        </div>
                        <h4 className="text-xs font-bold text-slate-200">
                            {recipientUser?.recipientUsername}
                        </h4>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    {recipientUser?.areFriends && (
                        <FriendCall
                            userId={recipientUser?.recipientId}
                            user={{
                                username: recipientUser?.recipientUsername,
                                profileImage: recipientUser?.recipientProfileImage,
                            }}
                        />
                    )}
                </div>
            </div>
            <div className="px-[0.625rem] py-[.375rem]">
                {privateChat?.map((chat) => {
                    return chat?.userId === userDetails?.id ? (
                        <div className="my-[0.313rem] flex justify-end">
                            <div className="text-white relative inline-block w-auto max-w-[16.4375rem] rounded-[.25rem] bg-secondaryBtnBg px-[0.625rem] py-[.4375rem]">
                                {!chat?.rainDrop && !chat?.moreDetails ? (
                                    isValidURL(chat?.message) ? (
                                        <Image
                                            src={chat?.message}
                                            width={10000}
                                            height={10000}
                                            className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                                            alt="GIF"
                                        />
                                    ) : (
                                        <p className="text-[.8125rem] break-words">{chat?.message}</p>
                                    )
                                ) : null}
                                <div className="flex justify-end">
                                    <span className="text-[0.625rem] text-steelTeal-500">
                                        {formatTo12Hour(chat?.createdAt)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="text-white relative my-[0.313rem] flex max-w-[16.625rem] items-center rounded-[.25rem] bg-erieBlack-200 px-[0.625rem] py-[.4375rem]">
                            {!chat?.rainDrop && !chat?.moreDetails ? (
                                isValidURL(chat?.message) ? (
                                    <Image
                                        src={chat?.message}
                                        width={10000}
                                        height={10000}
                                        className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                                        alt="GIF"
                                    />
                                ) : (
                                    <p className="min-w-[12.75rem] max-w-[12.75rem] text-[.8125rem]">
                                        {chat?.message}
                                    </p>
                                )
                            ) : null}
                            <span className="text-[0.625rem] text-steelTeal-500">
                                {formatTo12Hour(chat?.createdAt)}
                            </span>
                        </div>
                    );
                })}
            </div>
        </>
    );
};

export default Chat;