'use client';

import React from 'react';
import { X } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import IconButton from '@/components/Common/Button/IconButton';
import CameraImg from '@/assets/images/svg-images/camera.svg';
import Image from 'next/image';
import InputField from '@/components/Common/InputField';
import UploadImg from '@/assets/webp/upload-img.webp';
import ThemeSelect from '@/components/Common/InputField/ThemeSelect';
import SwitchButton from '../Friends/SwitchButton';
import AddUserModal from '@/assets/icons/AddUserModal';
import Link from 'next/link';
import User from '@/assets/webp/user-1.webp';

function GroupNameModal() {
  const { isOpen, handleCloseModal, closeModal, components } = useModalStore();
  const options = [
    { value: 'Public', label: 'Public' },
    { value: 'Private', label: 'Private' },
  ];
  const [selected, setSelected] = React.useState(null);

  return (
    <div className="relative w-full max-w-[38.25rem] p-0 md:p-4">
      <div className="relative flex max-h-[90vh] min-h-[34.875rem] flex-col overflow-hidden rounded-lg bg-steelTeal-800 ">
        {/* Scrollable Content */}
        <div className="overflow-y-auto px-7 py-[1.5rem] max-sm:px-4  max-sm:py-[1.5rem]">
          {/* Header */}
          <div className="mb-2 flex items-center justify-between bg-steelTeal-800">
            <h4 className="text-white text-[1.25rem] font-semibold">
              Group Name Here
            </h4>
            <div className="flex items-center gap-4">
              <Link
                href="javascript:void(0);"
                className="text-white text-xs font-semibold"
              >
                Update
              </Link>
              <AddUserModal className="cursor-pointer" />

              <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
                <X className="hover:text-white h-[1.75rem] w-[1.75rem] text-white-450 transition-all duration-300" />
              </IconButton>
            </div>
          </div>
          {/* Group Name */}
          <div className="mb-4 flex items-center gap-[1.4375rem]">
            <InputField
              type="text"
              name="userName"
              placeholder="Enter Group Name"
              label=""
              color="text-white-1000"
              className="rounded-[0.625rem] bg-inputBgColor "
            />
          </div>
          <div className="max-h-[calc(100vh-320px)] overflow-y-auto max-sm:max-h-[calc(100vh-410px)]">
            <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
              <div className="flex items-center gap-[0.313rem]">
                <Image
                  src={User}
                  alt="User"
                  className="h-12 w-12 rounded-full object-cover"
                />
                <h4 className="text-sm font-bold">Kimberly Mastrangelo</h4>
              </div>
              <AddUserModal className="cursor-pointer" />
            </div>
            <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
              <div className="flex items-center gap-[0.313rem]">
                <Image
                  src={User}
                  alt="User"
                  className="h-12 w-12 rounded-full object-cover"
                />
                <h4 className="text-sm font-bold">Kimberly Mastrangelo</h4>
              </div>
              <AddUserModal className="cursor-pointer" />
            </div>
            <div className="mb-[.4375rem] flex items-center justify-between rounded-xl bg-erieBlack-400 px-4 py-[.6875rem]">
              <div className="flex items-center gap-[0.313rem]">
                <Image
                  src={User}
                  alt="User"
                  className="h-12 w-12 rounded-full object-cover"
                />
                <h4 className="text-sm font-bold">Kimberly Mastrangelo</h4>
              </div>
              <AddUserModal className="cursor-pointer" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GroupNameModal;
