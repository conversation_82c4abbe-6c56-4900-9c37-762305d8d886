'use client';

import React from 'react';
import { X } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import IconButton from '@/components/Common/Button/IconButton';
import InputField from '@/components/Common/InputField';
import SwitchButton from '../Friends/SwitchButton';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';

function GroupDetailsModal() {
  const { closeModal } = useModalStore();

  return (
    <div className="relative w-full max-w-[38.25rem] p-0 md:p-4">
      <div className="relative flex max-h-[90vh] flex-col overflow-hidden rounded-lg bg-steelTeal-800 ">
        {/* Scrollable Content */}
        <div className="overflow-y-auto px-7 py-[3.125rem] max-sm:px-6 max-sm:py-[1.5rem]">
          {/* Header */}
          <div className="mb-[1.8125rem] flex items-center justify-between bg-steelTeal-800">
            <h4 className="text-white  text-[1.25rem] font-semibold">
              Update Group Details
            </h4>
            <div className="flex items-center gap-4">
              <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
                <X className="hover:text-white h-[1.75rem] w-[1.75rem] text-white-450 transition-all duration-300" />
              </IconButton>
            </div>
          </div>
          {/* Group Name */}
          <div className="lex mb-5 items-center gap-[1.4375rem]">
            <InputField
              type="text"
              name="userName"
              placeholder="Kimberly Mastrangelo"
              label="Update Group Details"
              color="text-white-1000"
              className="rounded-[0.625rem] bg-inputBgColor "
            />
          </div>
          <div className="mb-5 flex items-center gap-[1.4375rem]">
            <InputField
              type="text"
              name="userName"
              placeholder="Kimberly Mastrangelo"
              label="Group Description"
              color="text-white-1000"
              className="rounded-[0.625rem] bg-inputBgColor "
            />
          </div>
          <div className="pb-3">
            <SwitchButton
              leftLabel="Only Admin Can Add Members"
              showRightLabel={false}
              onKnobColor="bg-textGradient"
              offKnobColor="bg-erieBlack-300"
              onBgColor="bg-transparent"
              offBgColor="bg-textGradient"
              switchWidth="w-[2.125rem]"
              switchHeight="h-5"
              knobSize="h-[.875rem] w-[.875rem]"
            />
          </div>
          <div className="flex justify-center">
            <PrimaryButton
              variant="secondary"
              className="mt-1 !h-[1.5625rem] !w-fit rounded-[.9375rem] !text-[.8125rem] font-bold"
            >
              Update Group
            </PrimaryButton>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GroupDetailsModal;
