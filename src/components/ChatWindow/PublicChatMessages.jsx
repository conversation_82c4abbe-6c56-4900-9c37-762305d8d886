import { isValidURL } from '@/utils/helper';
import ChatAvatar from '../ChatAvatar';
import { memo } from 'react';
import Image from 'next/image';

export const ChatMessage = memo(
  ({ chat, handleOpenUserInfoModal, handleGrab, userDetails, openModal }) => {
    if (!chat) return null;

    return (
      <>
        {chat?.userId != userDetails?.id && (
          <div className="flex  gap-[0.625rem] rounded-[4px] bg-erieBlack-200 px-[0.625rem] py-1">
            <div
              className="relative !h-[1.875rem] !min-w-[1.875rem] cursor-pointer rounded-full "
              onClick={() => handleOpenUserInfoModal(chat?.userId)}
            >
              {/* <CustomImage
                src={chat?.user?.imageUrl || profile}
                alt="Profile"
                width={30}
                height={30}
                className="h-full w-full max-w-full rounded-full object-cover object-center"
                skeletonWidth={30}
                skeletonHeight={30}
              /> */}

              <ChatAvatar
                key={chat?.id}
                src={chat?.user?.imageUrl}
                firstName={chat.user?.firstName}
                lastName={chat.user?.lastName}
                userName={chat.user?.username}
                imageClassName="h-full w-full max-w-full rounded-full object-cover object-center"
                imageWidth={30}
                imageHeight={30}
                avatarSize={30}
              />
            </div>

            <div className="w-[calc(100%_-_3.625rem)]  justify-center gap-3">
              <div
                className=" mr-[5px]
    inline-flex
    max-w-full
    cursor-pointer
    items-center
    gap-[5.5794px]
    truncate
    align-baseline"
              >
                <span className="inline-block truncate text-sm  font-bold text-steelTeal-200">
                  {chat?.user?.username}:
                </span>
              </div>
              {/* <span className="inline-block">
                {formatDateTime(chat?.createdAt)}
              </span> */}
              {!chat?.rainDrop && !chat?.moreDetails ? (
                isValidURL(chat?.message) ? (
                  <Image
                    src={chat?.message}
                    width={10000}
                    height={10000}
                    className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                    alt="GIF"
                  />
                ) : (
                  <p className="inline-block align-baseline  text-sm  text-white-1000">
                    {chat?.message}
                  </p>
                )
              ) : null}

              {/* {chat?.moreDetails && chat.message === 'tip' && (
                <div className="relative rounded-lg bg-blue-500 p-2">
                  <p className="mb-1 text-lg">
                    <span
                      className="cursor-pointer font-semibold tracking-wide text-teal-300"
                      onClick={() =>
                        handleOpenUserInfoModal(chat?.moreDetails?.receiverId)
                      }
                    >
                      @{chat?.moreDetails?.receiverName}
                    </span>{' '}
                    just recieved a TIP
                  </p>
                  <div className="rounded-lg bg-blue-600 p-1 shadow-lg">
                    <p className="flex items-center gap-2 font-bold">
                      <Image
                        src={coinAc}
                        width={10000}
                        height={10000}
                        className="w-5 max-w-full xl:w-5"
                        alt="Coin"
                      />{' '}
                      <span className="mt-1 flex items-center">
                        {chat?.moreDetails?.amount} AC
                      </span>
                    </p>
                  </div>
                </div>
              )}

              {chat?.rainDrop && (
                <div className="rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-2 py-2">
                  <p className="my-1">{chat?.rainDrop?.message}</p>
                  <div className="relative overflow-hidden rounded-lg">
                    <Image
                      src={coinsGift}
                      width={10000}
                      height={10000}
                      className="h-auto w-full max-w-full"
                      alt="Chat Image"
                    />
                    <div className="absolute right-3 top-3">
                      <p className="text-white mb-1 text-lg font-bold">
                        Coin Drops
                      </p>
                      {chat?.rainDrop?.status === 'complete' ? (
                        <p
                          onClick={() =>
                            openModal(
                              <RainCompleteModal
                                rainDrop={chat?.rainDrop}
                                chat={chat}
                              />,
                            )
                          }
                          className="text-white cursor-pointer rounded-lg bg-red-500 px-2 py-1 font-bold"
                        >
                          Completed
                        </p>
                      ) : chat?.rainDrop?.userId !== userDetails?.userId &&
                        chat?.rainDrop?.grabbedStatus ? (
                        <p className="text-white rounded-lg bg-green-500 px-2 py-1 font-bold">
                          Grabbed
                        </p>
                      ) : chat?.rainDrop?.userId !== userDetails?.userId ? (
                        <button
                          onClick={() => handleGrab(chat)}
                          className="text-white rounded-lg bg-red-500 px-2 py-1 font-bold"
                        >
                          Grab
                        </button>
                      ) : null}
                    </div>
                  </div>
                </div>
              )} */}
            </div>
          </div>
        )}
        {/* RECIVER */}
        {chat?.userId == userDetails?.id && (
          <div className="flex justify-end">
            <div className="text-white inline-block w-auto max-w-[16.4375rem] rounded-[.25rem] bg-secondaryBtnBg px-[0.625rem] py-[.4375rem]">
              {/* <p className="text-[.8125rem]">{chat?.message}</p> */}

              {!chat?.rainDrop && !chat?.moreDetails ? (
                isValidURL(chat?.message) ? (
                  <Image
                    src={chat?.message}
                    width={10000}
                    height={10000}
                    className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                    alt="GIF"
                  />
                ) : (
                  <p className="text-[.8125rem]">{chat?.message}</p>
                )
              ) : null}
            </div>
          </div>
        )}
      </>
    );
  },
);

ChatMessage.displayName = 'ChatMessage';
