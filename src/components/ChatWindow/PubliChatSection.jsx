import { useGrabRainDrop } from '@/hooks/useGrabRainDrop';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import { useCallback, useState } from 'react';
import UserInfo from '../UserInfoModal';
import { ChatMessage } from './PublicChatMessages';
import useChatWindow from '@/hooks/useChatWindow';
import useUserInfoStore from '@/store/useUserInfoStore';

const PubliChatSection = () => {
  const [privateChatUserDetails, setPrivateChatUserDetails] = useState(null);

  const { userDetails, isAuthenticated } = useAuthStore();
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useModalStore();

  const { grabRainDrop } = useGrabRainDrop();

  const {
    publicChats,

    setGrabbedChat,
    updateGrabbedRainChat,
  } = useChatWindow();

  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(
        <UserInfo setPrivateChatUserDetails={setPrivateChatUserDetails} />,
      );
    },
    [openUserInfoModal, openModal],
  );

  const handleGrab = useCallback(
    (chat) => {
      if (!userDetails?.userId) return;

      grabRainDrop({
        userId: userDetails.userId,
        rainDrop: {
          rainId: chat?.rainDrop?.rainId,
          amountType: chat?.rainDrop?.amountType,
          amount: chat?.rainDrop?.amount,
          playerNo: chat?.rainDrop?.playerNo,
          playerType: chat?.rainDrop?.playerType,
        },
      });

      updateGrabbedRainChat(chat, userDetails.userId);
      setGrabbedChat({ userId: userDetails.userId, chat });
    },
    [grabRainDrop, updateGrabbedRainChat, setGrabbedChat, userDetails],
  );
  return (
    <div className="flex flex-col gap-[0.625rem] px-[0.625rem] pb-4">
      {publicChats?.map((chat) => (
        <ChatMessage
          key={chat?.userChatId}
          chat={chat}
          handleOpenUserInfoModal={handleOpenUserInfoModal}
          handleGrab={handleGrab}
          userDetails={userDetails}
          openModal={openModal}
        />
      ))}
    </div>
  );
};

export default PubliChatSection;
