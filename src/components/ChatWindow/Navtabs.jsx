import useAuthTab from '@/store/useAuthTab';
import useGeneralStore from '@/store/useGeneralStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { memo, useCallback } from 'react';
import Auth from '../Auth';
import IconButton from '../Common/Button/IconButton';
import FriendIcon from '@/assets/icons/FriendsIcon';
import GroupChatIcon from '@/assets/icons/GroupChatIcon';
import PublicChatIcon from '@/assets/icons/PublicChatIcon';
import SearchIcon from '@/assets/icons/Search';
import CrossIcon from '@/assets/icons/CrossIcon';
import useAuthModalStore from '@/store/useAuthModalStore';

export const NavTabs = memo(
  ({ section, setSection, newMessagesCount, groupTotal, isAuthenticated }) => {
    const { setIsPrivateChatOpen } = usePrivateChatStore();
    const { setIsGroupChatOpen } = useGroupChatStore();
    const { setSelectedTab } = useAuthTab((state) => state);
    const { openModal } = useAuthModalStore();
    const { setOpenChat, openChat, setActiveMenu, activePreviousState } =
      useGeneralStore();

    const handleTabChange = useCallback(
      (newSection) => {
        if (newSection === 'PrivateChat' && !isAuthenticated) {
          localStorage.setItem('activeTab', 0);
          setSelectedTab(0);
          openModal(<Auth />);
          return;
        }

        setSection(newSection);
        setIsPrivateChatOpen(false);
      },
      [setSection, setIsPrivateChatOpen, setIsGroupChatOpen, isAuthenticated],
    );

    return (
      <>
        <div className="flex items-center justify-between gap-3 border-b  border-white-300 bg-steelTeal-800 shadow-chat-header md:h-[3.25rem]">
          <div className="flex h-full w-full justify-between">
            <button
              type="button"
              className={`flex h-full w-full items-center justify-center gap-1 border-b-[2px] border-transparent ${section === 'PrivateChat' ? '!border-red-1000 bg-erieBlack-300' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-sm text-white-1000`}
              onClick={() => handleTabChange('PrivateChat')}
            >
              <IconButton>
                <FriendIcon
                  className={`${section === 'PrivateChat' ? '[&>path]:fill-red-1000' : 'fill-steelTeal-200 group-hover:fill-white-1000'} h-[1.5rem] w-5 transition-all duration-300`}
                />
              </IconButton>
              <span>Friends</span>
            </button>
            <button
              type="button"
              className={`flex h-full w-full items-center justify-center gap-1 border-b-[2px] border-transparent ${section === 'GroupChat' ? '!border-red-1000 bg-erieBlack-300' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-sm text-white-1000`}
              onClick={() => handleTabChange('GroupChat')}
            >
              <IconButton>
                <GroupChatIcon
                  className={`${section === 'GroupChat' ? 'fill-red-1000' : 'fill-steelTeal-200  group-hover:fill-white-1000'} w-4 transition-all duration-300`}
                />
              </IconButton>
              <span>Groups</span>
              {/* <span
                className={`text-white flex h-6 w-6 items-center justify-center rounded-full text-xs ${section === 'GroupChat' ? 'bg-maastrichtBlue-1000' : 'bg-black-1000'}`}
              >
                {groupTotal || 0}
              </span> */}
              {/* {newMessagesCount > 0 && (
                <div className="h-2 w-2 rounded-full bg-green-1000" />
              )} */}
            </button>
            <button
              type="button"
              className={`flex h-full w-full items-center justify-center gap-1 border-b-[2px] border-transparent   ${section === 'PublicChat' ? '!border-red-1000 bg-erieBlack-300' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-sm font-medium text-steelTeal-200`}
              onClick={() => handleTabChange('PublicChat')}
            >
              <IconButton>
                <PublicChatIcon
                  className={`${section === 'PublicChat' ? 'fill-red-1000' : 'fill-steelTeal-200 group-hover:fill-white-1000'} w-4 transition-all duration-300`}
                />
              </IconButton>
              <span>Public</span>
              {/* {newMessagesCount > 0 && (
                <div className="h-2 w-2 rounded-full bg-green-1000" />
              )} */}
            </button>
          </div>
          <div className="flex gap-3">
            {/* <SearchIcon className="h-4 w-4 [&>path]:fill-steelTeal-200 " /> */}
            <button
              className="me-2 flex items-center justify-center rounded-md border border-white-200 bg-cetaceanBlue-1000 p-2"
              onClick={() => {
                setOpenChat(false);
                setActiveMenu(activePreviousState);
              }}
            >
              <CrossIcon className=" h-3 w-3 cursor-pointer [&>path]:fill-steelTeal-200" />
            </button>
          </div>
        </div>
      </>
    );
  },
);
NavTabs.displayName = 'NavTabs';
