'use client';

import AddFriend from '@/assets/icons/AddFriend';
import DoubleUserIcon from '@/assets/icons/DoubleUserIcon';
import MessageIcon from '@/assets/icons/MessageIcon';
import ChatAvatar from '@/components/ChatAvatar';
import CreateGroupModal from '@/components/Models/CreateGroupModal';
import ShowGroupModal from '@/components/Models/ShowGroupModal';
import {
  useGetGroupListQuery,
  useGetGroupRequestListQuery,
  useGetPublicGroupListQuery,
  useJoinGroup,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import GroupImg1 from '../../../assets/images/demo-image/user-profile.jpg';
import GroupSectionHeader from '../Friends/GroupSectionHeader';
import GroupRequestItem from './GroupRequestItem';
import toast from 'react-hot-toast';
import GroupCall from './GroupCall';
import Auth from '@/components/Auth';
import useAuthTab from '@/store/useAuthTab';
import useAuthModalStore from '@/store/useAuthModalStore';
const images = [GroupImg1, GroupImg1, GroupImg1, GroupImg1];

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

const GroupChat = () => {
  const { openModal } = useModalStore((state) => state);
  const { openModal: AuthModalOpen } = useAuthModalStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const [searchInput, setSearchInput] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const {
    setIsGroupChatOpen,
    setGroupId,
    setGroupChat,
    setGroupName,
    selectedGroupTab,
  } = useGroupChatStore((state) => state);
  const { setSelectedGroupTab } = useGroupChatStore();

  // Create a stable debounced search function
  const debouncedSearchHandler = useMemo(
    () => debounce((value) => setDebouncedSearch(value), 1000),
    [],
  );
  const [isInviteSelected, setIsInviteSelected] = useState(false);
  // useEffect(() => {
  //   setGroupChat([]);
  // }, []);

  // Handle search input changes
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    debouncedSearchHandler(value);
  };

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch,
  } = useGetGroupListQuery({
    search: debouncedSearch,
    enabled: !!isAuthenticated,
  });
  const {
    data: publicGroups,
    fetchNextPage: fetchNextPagePublicGroups,
    hasNextPage: hasNextPagePublicGroups,
    isFetchingNextPage: isFetchingNextPagePublicGroups,
    status: statusPublicGroups,
    isLoading: isPublicGroupLoading,
  } = useGetPublicGroupListQuery({
    search: debouncedSearch,
    enabled: true,
    groupType: 'public',
  });
  const {
    data: dataGroupInvitation,
    fetchNextPage: fetchNextPageGroupInvitation,
    hasNextPage: hasNextPageGroupInvitation,
    isFetchingNextPage: isFetchingNextPageGroupInvitation,
    status: statusGroupInvitation,
  } = useGetGroupRequestListQuery({
    search: debouncedSearch,
    enabled: !!isAuthenticated,
    invitationType: 'invite',
  });
  const joinGroup = useJoinGroup({
    onSuccess: async (response) => {
      toast.success('Group Joined');
      queryClient.invalidateQueries({
        queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });
      localStorage.removeItem('pendingGroupJoin');
      refetch();
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });

  useEffect(() => {
    if (!isAuthenticated) {
      setSelectedGroupTab('PublicGroups');
    } else {
      const pendingGroupId = localStorage.getItem('pendingGroupJoin');
      if (pendingGroupId) {
        joinGroup.mutate(
          { groupId: pendingGroupId, action: 'join' }
        );
      }
      setSelectedGroupTab('MYGroups');
    }
  }, [isAuthenticated]);

  console.log('🚀 ~ GroupChat ~ dataGroupInvitation:', dataGroupInvitation);
  console.log('🚀 ~ GroupChat ~ publicGroups:', publicGroups);

  const queryClient = useQueryClient();

  const observerRef = useRef(null);
  const publicGroupsObserverRef = useRef(null);
  const invitesObserverRef = useRef(null);

  const lastGroupElementRef = useCallback(
    (node) => {
      if (observerRef.current) observerRef.current.disconnect();
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) observerRef.current.observe(node);
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage],
  );

  const lastPublicGroupElementRef = useCallback(
    (node) => {
      if (publicGroupsObserverRef.current)
        publicGroupsObserverRef.current.disconnect();
      publicGroupsObserverRef.current = new IntersectionObserver(
        (entries) => {
          if (
            entries[0].isIntersecting &&
            hasNextPagePublicGroups &&
            !isFetchingNextPagePublicGroups
          ) {
            fetchNextPagePublicGroups();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) publicGroupsObserverRef.current.observe(node);
    },
    [
      hasNextPagePublicGroups,
      isFetchingNextPagePublicGroups,
      fetchNextPagePublicGroups,
    ],
  );

  // Intersection Observer for Invites
  const lastInviteElementRef = useCallback(
    (node) => {
      if (invitesObserverRef.current) invitesObserverRef.current.disconnect();
      invitesObserverRef.current = new IntersectionObserver(
        (entries) => {
          if (
            entries[0].isIntersecting &&
            hasNextPageGroupInvitation &&
            !isFetchingNextPageGroupInvitation
          ) {
            fetchNextPageGroupInvitation();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) invitesObserverRef.current.observe(node);
    },
    [
      hasNextPageGroupInvitation,
      isFetchingNextPageGroupInvitation,
      fetchNextPageGroupInvitation,
    ],
  );

  const openCreateModal = () => {
    openModal(<CreateGroupModal />);
  };

  if (status === 'error') {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-red-500">Error loading groups</div>
      </div>
    );
  }

  const handleOpenModal = (e, id, group) => {
    e.stopPropagation();
    openProfileDetails(id, group?.groupName);
    openModal(<ShowGroupModal groupId={id} groupData={group} />);
  };

  const openChat = (groupId, groupName) => {
    queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
  };

  const openProfileDetails = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
  };

  return (
    <div className="">
      <GroupSectionHeader
        setIsInviteSelected={setIsInviteSelected}
        myGroupsLength={data?.total}
        publicGroupLength={publicGroups?.total}
        invitesLength={dataGroupInvitation?.total}
        handleSearchChange={handleSearchChange}
        searchInput={searchInput}
        isInviteSelected={isInviteSelected}
      />

      <div className="px-[0.625rem] py-2">
        {isInviteSelected ? (
          <>
            {statusGroupInvitation == 'pending' ? (
              <div className="flex h-full items-center justify-center">
                <div className="text-white">Loading...</div>
              </div>
            ) : dataGroupInvitation?.total == 0 ? (
              <p className="flex justify-center text-[13px] text-steelTeal-200">
                No Invitations
              </p>
            ) : (
              <>
                {dataGroupInvitation?.groups?.map((request, index) => (
                  <div
                    key={request.id || index}
                    ref={
                      index === dataGroupInvitation.groups.length - 1
                        ? lastInviteElementRef
                        : null
                    }
                  >
                    <GroupRequestItem req={request} />
                  </div>
                ))}
                {isFetchingNextPageGroupInvitation && (
                  <div className="text-white mt-4 text-center">
                    Loading more invites...
                  </div>
                )}
              </>
            )}
          </>
        ) : selectedGroupTab == 'MYGroups' ? (
          <>
            {status == 'pending' ? (
              <div className="flex h-full items-center justify-center">
                <div className="text-white">Loading...</div>
              </div>
            ) : data?.groups.length === 0 ? (
              <div className="flex justify-center text-[13px] text-steelTeal-200">
                No Groups Found
              </div>
            ) : (
              <>
                {data?.groups.map((group, index) => {
                  return (
                    <div
                      key={group.id || index}
                      className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-1 md:px-3 py-1"
                      onClick={() => openChat(group?.id, group?.groupName)}
                      ref={
                        index === data.groups.length - 1
                          ? lastGroupElementRef
                          : null
                      }
                    >
                      <div className="flex items-center gap-1.5">
                        <div
                          className="cursor-pointer"
                          onClick={(e) => handleOpenModal(e, group.id, group)}
                        >
                          <ChatAvatar
                            profileImage={group.profile}
                            firstName={group?.groupName}
                            lastName=""
                            userName={group?.groupName}
                            imageClassName="h-12 w-12 min-w-12 rounded-full"
                            imageWidth={48}
                            imageHeight={48}
                            avatarSize={48}
                          />
                        </div>
                        <div className="flex-col text-steelTeal-200">
                          <p className="mb-[0.2rem] pr-2 text-sm font-bold text-steelTeal-200">
                            {group?.groupName}
                          </p>
                          <div className="flex items-center gap-[0.625rem] text-[0.625rem]">
                            <span className="rounded-[1.875rem] bg-secondaryBtnBg px-2.5 py-0.5  uppercase">
                              {group?.groupType == 'private'
                                ? 'Private'
                                : 'Public'}
                            </span>
                            <div className="gap- flex items-center gap-1">
                              <DoubleUserIcon />
                              <p className="  font-black">
                                • {group?.groupMembersCount}
                              </p>
                            </div>
                            <div className="flex  items-start justify-start gap-[0.625rem]">
                              <p className=" font-black text-green-600">
                                • {group?.totalOnlineMembers}
                              </p>
                              <p className=" font-black text-steelTeal-600">
                                • 3
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-[18px] md:gap-1.5">
                        <GroupCall
                          className="h-5 w-5"
                          groupId={group.id}
                          groupName={group.groupName}
                        />
                        {/* <button>
                        <AddFriend className="h-4 w-4" />
                      </button> */}
                        <button className="relative">
                          {/* <Image
                            src={msgBadge}
                            alt="Savannah Williams"
                            className="absolute -right-1 top-[-0.3rem] h-[.6875rem] w-[.6875rem]"
                          /> */}
                          <MessageIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  );
                })}
                {isFetchingNextPage && (
                  <div className="text-white mt-4 text-center">
                    Loading more groups...
                  </div>
                )}
              </>
            )}
          </>
        ) : (
          <>
            {statusPublicGroups == 'pending' ? (
              <div className="flex h-full items-center justify-center">
                <div className="text-white">Loading...</div>
              </div>
            ) : publicGroups?.groups.length === 0 ? (
              <div className="flex justify-center text-[13px] text-steelTeal-200">
                No Groups Found
              </div>
            ) : (
              <>
                {publicGroups?.groups.map((group, index) => {
                  return (
                    <div
                      key={group.id || index}
                      className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-1 md:px-3 py-1"
                      onClick={() => openChat(group?.id, group?.groupName)}
                      ref={
                        index === publicGroups.groups.length - 1
                          ? lastPublicGroupElementRef
                          : null
                      }
                    >
                      <div className="flex items-center gap-1.5">
                        <div
                          className="cursor-pointer"
                          onClick={(e) => handleOpenModal(e, group.id, group)}
                        >
                          <ChatAvatar
                            profileImage={group.profile}
                            firstName={group?.groupName}
                            lastName=""
                            userName={group?.groupName}
                            imageClassName="h-12 w-12 min-w-12  rounded-full"
                            imageWidth={48}
                            imageHeight={48}
                            avatarSize={48}
                          />
                        </div>
                        <div className="flex-col text-steelTeal-200">
                          <p className="mb-[0.2rem] pr-2 text-sm font-bold text-steelTeal-200">
                            {group?.groupName}
                          </p>
                          <div className="flex items-center gap-[0.625rem]">
                            <span className="rounded-[1.875rem] bg-secondaryBtnBg px-2.5 py-0.5 text-[0.625rem] uppercase">
                              {group?.groupType == 'private'
                                ? 'Private'
                                : 'Public'}
                            </span>
                            <div className="gap- flex items-center gap-1">
                              <DoubleUserIcon />
                              <p className="text- text-[0.625rem] font-black">
                                • {group?.groupMembersCount}
                              </p>
                            </div>
                            <div className="flex  items-start justify-start gap-[0.625rem] text-[0.625rem]">
                              <p className=" font-black text-green-600">
                                • {group?.totalOnlineMembers}
                              </p>
                              <p className=" font-black text-steelTeal-600">
                                • 3
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-[18px] md:gap-1.5">
                        {group?.isUserExistInGroup ? (
                          <button>
                            <AddFriend className="h-4 w-4" />
                          </button>
                        ) : (
                          <button
                            disabled={joinGroup?.isPending}
                            className="flex items-center justify-center gap-2 rounded-[10px] bg-[#e81c24] px-2 py-2 text-[14px] font-semibold capitalize leading-none text-black-1000 transition duration-200 hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              if (!isAuthenticated) {
                                setSelectedTab(0);
                                localStorage.setItem('activeTab', 0);
                                localStorage.setItem('pendingGroupJoin', group.id);
                                AuthModalOpen(<Auth />);
                                return;
                              }
                              joinGroup.mutate({
                                groupId: group.id,
                                action: 'join',
                              });
                            }}
                          >
                            Join
                          </button>
                        )}
                        <button className="relative">
                          {/* <Image
                    src={msgBadge}
                    alt="Savannah Williams"
                    className="absolute -right-1 top-[-0.3rem] h-[.6875rem] w-[.6875rem]"
                  /> */}
                          <MessageIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  );
                })}
                {isFetchingNextPagePublicGroups && (
                  <div className="text-white mt-4 text-center">
                    Loading more groups...
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default GroupChat;
