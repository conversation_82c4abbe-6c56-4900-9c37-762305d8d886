import ChatAvatar from '@/components/ChatAvatar';
import {
  useAcceptDeclineGroupJonRequest,
  useUpdateFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import { CircleCheck, CircleX } from 'lucide-react';
import React from 'react';
import toast from 'react-hot-toast';

export default function GroupRequestItem({
  req,
  refetchFriendsRequestList,
  name,
  username,
  avatar,
}) {
  const queryClient = useQueryClient();
  const responseRequest = useAcceptDeclineGroupJonRequest({
    onSuccess: async (response, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_REQUEST_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });

      toast.success(
        variables?.requestStatus == 'ACCEPTED'
          ? 'Request accepted successfully!'
          : 'Request declined successfully',
      );
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });

  return (
    <div className="flex items-center justify-between rounded-[.25rem] bg-steelTeal-800 px-3 py-2 ">
      <div className="flex items-center gap-3">
        {/* <img
          src={avatar}
          alt={req?.userFriendRequester?.username}
          className="h-10 w-10 rounded-full object-cover"
        /> */}
        <ChatAvatar
          profileImage={req?.userChatGroup?.profile}
          userName={req?.userChatGroup?.groupName}
          imageClassName="h-10 w-10 rounded-full object-cover"
          avatarSize={40}
        />
        <div className="text-steelTeal-200">
          <p className="text-sm font-bold">{req?.userChatGroup?.groupName}</p>
          {/* <p className="text-[.8125rem]">
            {req?.userChatGroup?.firstName}
          </p> */}
        </div>
      </div>
      <div className="flex items-center gap-2">
        <button className="">
          <CircleCheck
            className="h-4 w-4 text-steelTeal-200"
            onClick={() => {
              responseRequest.mutate({
                groupId: req?.groupId,
                requestId: req?.id,
                requestStatus: 'ACCEPTED',
              });
            }}
            // onClick={() => handleFriendRequest(req.requesterUserId, 'accepted')}
          />
        </button>
        <button className="text-white ">
          <CircleX
            className="h-4 w-4 text-steelTeal-200"
            onClick={() => {
              responseRequest.mutate({
                groupId: req?.groupId,
                requestId: req?.id,
                requestStatus: 'DECLINE',
              });
            }}
            // onClick={() => handleFriendRequest(req.requesterUserId, 'rejected')}
          />
        </button>
      </div>
    </div>
  );
}
