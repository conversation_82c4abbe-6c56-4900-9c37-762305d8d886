import AddFriend from '@/assets/icons/AddFriend';
import useGroupCall from '@/hooks/useGroupCall';
import useCallModalStore from '@/store/useCallModalStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';

const GroupCall = ({
  groupId,
  groupName,
  className = '',
  buttonClassName = '',
  buttonName = '',
  setIsGroupCallClicked = () => {},
}) => {
  const { initiateCall, handleDeclineCall } = useGroupCall();
  const { voiceCall } = useVoiceCallStore();
  const { callInitialized } = useGroupChatStore();
  const setIsMinimized = useCallModalStore((state) => state.setIsMinimized);

  if (voiceCall) {
    if (voiceCall?.groupId == groupId)
      return (
        <button
          className={`${buttonClassName}`}
          disabled={
            callInitialized?.groupId != null
              ? callInitialized?.groupId == groupId
              : false
          }
          onClick={(e) => {
            e.stopPropagation();
            if (voiceCall?.groupId == groupId) {
              setIsMinimized(false);
            } else {
              initiateCall(groupId, groupName);
              setIsGroupCallClicked(true);
            }
          }}
        >
          <AddFriend
            fill={voiceCall ? voiceCall?.groupId == groupId : false}
            className={` h-5 w-5 ${className} `}
          />
          <span
            className={`${voiceCall?.groupId == groupId ? 'bg-TintGoldGradient bg-clip-text text-transparent' : 'white'}`}
          >
            {buttonName}
          </span>
        </button>
      );
  } else {
    return (
      <button
        className={`${buttonClassName}`}
        disabled={
          callInitialized?.groupId != null
            ? callInitialized?.groupId == groupId
            : false
        }
        onClick={(e) => {
          e.stopPropagation();
          if (voiceCall?.groupId == groupId) {
            setIsMinimized(false);
          } else {
            initiateCall({ groupId, groupName });
            setIsGroupCallClicked(true);
          }
        }}
      >
        <AddFriend
          fill={voiceCall ? voiceCall?.groupId == groupId : false}
          className={`h-5 w-5 ${className}`}
        />
        <span
          className={`${voiceCall?.groupId == groupId ? 'bg-TintGoldGradient bg-clip-text text-transparent' : 'white'}`}
        >
          {buttonName}
        </span>
      </button>
    );
  }
};

export default GroupCall;
