import AddFriend from '@/assets/icons/AddFriend';
import usePrivate<PERSON>all from '@/hooks/usePrivateCall';
import useCallModalStore from '@/store/useCallModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import React, { use } from 'react';

const FriendCall = ({
  userId,
  user,
  iconClassName = 'h-5 w-5 cursor-pointer',
}) => {
  const {
    initiateCall,
    callInitiated,
    handleDisconnectCall,
    checkMicrophonePermission,
  } = usePrivateCall();
  const { voiceCall } = useVoiceCallStore();
  const setIsMinimized = useCallModalStore((state) => state.setIsMinimized);

  if (voiceCall) {
    if (voiceCall?.userId == userId)
      return (
        <button
          disabled={callInitiated}
          onClick={(e) => {
            e.stopPropagation();
            if (voiceCall?.userId == userId) {
              setIsMinimized(false);
            } else {
              initiateCall({
                userId: userId,
                userName: user?.username,
                profileImage: user?.profileImage,
              });
            }
          }}
        >
          <AddFriend
            fill={voiceCall?.userId == userId}
            className={iconClassName}
          />
        </button>
      );
  } else {
    return (
      <button
        disabled={callInitiated}
        onClick={async (e) => {
          e.stopPropagation();
          if (voiceCall?.userId == userId) {
            setIsMinimized(false);
          } else {
            const micPermissionGranted = await checkMicrophonePermission();
            if (!micPermissionGranted) {
              alert('Please allow microphone access to join the call.');

              return; // Exit early if permission is not granted
            }

            initiateCall({
              userId: userId,
              userName: user?.username,
              profileImage: user?.profileImage,
            });
          }
        }}
      >
        <AddFriend
          fill={voiceCall?.userId == userId}
          className={iconClassName}
        />
      </button>
    );
  }
};

export default FriendCall;
