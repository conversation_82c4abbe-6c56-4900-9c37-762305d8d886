import React, { useEffect, useState } from 'react';
import User from '@/assets/webp/user-1.webp';
import Image from 'next/image';
import messageGradient from '@/assets/images/svg-images/gradient-message.svg';
import enterGradient from '@/assets/images/svg-images/enter.svg';
import msgBadge from '@/assets/webp/msg-badge.webp';
import DoubleUserIcon from '@/assets/icons/DoubleUserIcon';
import AddFriend from '@/assets/icons/AddFriend';
import MessageIcon from '@/assets/icons/MessageIcon';
import useGroupChatStore from '@/store/useGroupChatStore';
import AddChatGroupIcon from '@/assets/icons/AddUserChat';
import SearchIcon from '@/assets/icons/Search';
import InviteFriends from './InviteFriends';
import CreateGroupModal from '../CreateGroupModal';
import useModalStore from '@/store/useModalStore';
import GroupNameModal from '../GroupNameModal';
import GroupDetailsModal from '../GroupDetailsModal';

export const GroupFriends = () => {
  const { setIsGroupChatOpen } = useGroupChatStore();
  const [search, setSearch] = useState('');
  // const [isOpen, setIsOpen] = useState(false);
  const { isOpen, handleCloseModal, openModal } = useModalStore();

  return (
    <>
      <div className="flex items-center gap-1 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
        <div className="relative flex  items-center gap-2">
          <SearchIcon className="absolute left-[1.25rem] h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
          <input
            className="h-9 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 pl-[2.875rem]  placeholder:text-xs  placeholder:text-steelTeal-200 max-sm:placeholder:relative max-sm:placeholder:top-[-1px] max-sm:placeholder:text-[.8125rem]"
            placeholder="Search Users"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-1">
          <button
            className="create-group-btn flex h-9 min-w-[7.25rem] items-center justify-center gap-1 rounded-[0.625rem] bg-secondaryBtnBg text-[.8125rem]"
            onClick={() => openModal(<CreateGroupModal />)}
          >
            <AddChatGroupIcon className="h-5 w-5 [&_*]:fill-stone-200" />
            Create Group
          </button>
        </div>
      </div>
      <div>
        <InviteFriends />
      </div>
      <div className="px-[0.625rem] py-2">
        <div
          className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-3 py-1"
          onClick={() => setIsGroupChatOpen(true)}
        >
          <div className="flex items-center gap-3">
            <Image
              src={User}
              alt="Savannah Williams"
              className="h-12 w-12 rounded-full object-cover"
            />
            <div className="flex-col text-steelTeal-200">
              <p className="mb-[0.2rem] pr-2 text-sm font-bold text-steelTeal-200">
                My Group 1
              </p>
              <div className="flex items-center gap-[0.625rem]">
                <span className="rounded-[1.875rem] bg-secondaryBtnBg px-2.5 py-0 text-[0.625rem] font-semibold uppercase">
                  Public
                </span>
                <div className="gap- flex items-center gap-1">
                  <DoubleUserIcon />
                  <p className="text- text-[0.625rem] font-black">23</p>
                </div>
                <div class="flex  items-start justify-start gap-[0.625rem]">
                  <p class="text-xs font-black text-green-600">• 17</p>
                  <p class="text-xs font-black text-steelTeal-600">• 3</p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button>
              <Image src={enterGradient} alt="Add Friend" className="h-4 w-4" />
            </button>
            <button>
              <Image src={messageGradient} alt="Message" className="h-4 w-4" />
            </button>
          </div>
        </div>
        <div
          className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-3 py-1"
          onClick={() => setIsGroupChatOpen(true)}
        >
          <div className="flex items-center gap-1.5">
            <Image
              src={User}
              alt="Savannah Williams"
              className="h-12 w-12 min-w-12 rounded-full object-cover"
            />
            <div className="flex-col text-steelTeal-200">
              <p className="mb-[0.2rem] pr-2 text-sm font-bold text-steelTeal-200">
                My Group 1
              </p>
              <div className="flex items-center gap-[0.625rem]">
                <span className="rounded-[1.875rem] bg-secondaryBtnBg px-2.5 py-0.5 text-[0.625rem] uppercase">
                  Public
                </span>
                <div className="gap- flex items-center gap-1">
                  <DoubleUserIcon />
                  <p className="text- text-[0.625rem] font-black">23</p>
                </div>
                <div class="flex  items-start justify-start gap-[0.625rem]">
                  <p class="text-xs font-black text-green-600">• 17</p>
                  <p class="text-xs font-black text-steelTeal-600">• 3</p>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1.5">
            <button>
              <AddFriend className="h-4 w-4" />
            </button>
            <button className="relative">
              <Image
                src={msgBadge}
                alt="Savannah Williams"
                className="absolute -right-1 top-[-0.3rem] h-[.6875rem] w-[.6875rem]"
              />
              <MessageIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default GroupFriends;
