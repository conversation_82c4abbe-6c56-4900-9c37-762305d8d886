import React, { useEffect, useState } from 'react';
import User from '@/assets/webp/user-1.webp';
import Image from 'next/image';
import messageGradient from '@/assets/images/svg-images/gradient-message.svg';
import enterGradient from '@/assets/images/svg-images/enter.svg';
import msgBadge from '@/assets/webp/msg-badge.webp';
import DoubleUserIcon from '@/assets/icons/DoubleUserIcon';
import AddFriend from '@/assets/icons/AddFriend';
import MessageIcon from '@/assets/icons/MessageIcon';
import useGroupChatStore from '@/store/useGroupChatStore';
import AddChatGroupIcon from '@/assets/icons/AddUserChat';
import SearchIcon from '@/assets/icons/Search';
import InviteFriends from './InviteFriends';
import CreateGroupModal from '../CreateGroupModal';
import useModalStore from '@/store/useModalStore';
import GroupNameModal from '../GroupNameModal';
import GroupDetailsModal from '../GroupDetailsModal';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import Auth from '@/components/Auth';
import useAuthModalStore from '@/store/useAuthModalStore';

export const GroupSectionHeader = ({
  setIsInviteSelected,
  myGroupsLength,
  publicGroupLength,
  invitesLength,
  handleSearchChange,
  searchInput,
  isInviteSelected,
}) => {
  const { setIsGroupChatOpen } = useGroupChatStore();
  const [search, setSearch] = useState('');
  // const [isOpen, setIsOpen] = useState(false);
  const { isOpen, handleCloseModal, openModal } = useModalStore(); // ✅ single source of truth
  const { openModal:AuthModalOpen } = useAuthModalStore();
  const { isAuthenticated } = useAuthStore();
  const { setSelectedTab } = useAuthTab((state) => state);

  return (
    <>
      <div className="flex items-center justify-between gap-1 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
        <div className="relative flex  w-full items-center gap-2">
          <SearchIcon className="absolute left-[1.25rem] h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
          <input
            className="h-9 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 pl-[2.875rem]  placeholder:text-xs  placeholder:text-steelTeal-200 max-sm:placeholder:relative max-sm:placeholder:top-[-1px] max-sm:placeholder:text-[.8125rem]"
            placeholder="Search Groups"
            value={searchInput}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex items-center gap-1">
          <button
            className="create-group-btn flex h-9 min-w-[7.25rem] cursor-pointer items-center justify-center gap-1 rounded-[0.625rem] bg-secondaryBtnBg text-[.8125rem]"
            onClick={() => {
              if (!isAuthenticated) {
                localStorage.setItem('activeTab', 1);
                setSelectedTab(0);
                localStorage.setItem('activeTab', 0);
                AuthModalOpen(<Auth />);
                return false;
              }
              openModal(<CreateGroupModal />);
            }}
          >
            <AddChatGroupIcon className="h-5 w-5 [&_*]:fill-stone-200" />
            Create Group
          </button>
        </div>
      </div>
      <div>
        <InviteFriends
          setIsInviteSelected={setIsInviteSelected}
          myGroupsLength={myGroupsLength}
          publicGroupLength={publicGroupLength}
          invitesLength={invitesLength}
          isInviteSelected={isInviteSelected}
        />
      </div>
    </>
  );
};

export default GroupSectionHeader;
