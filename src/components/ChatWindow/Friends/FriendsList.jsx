'use client';
import AddFriend from '@/assets/icons/AddFriend';
import MessageIcon from '@/assets/icons/MessageIcon';
import ChatAvatar from '@/components/ChatAvatar';
import UserInfo from '@/components/UserInfoModal';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useCallback, useState } from 'react';
import FriendCall from './FriendCall';

export const FriendsList = ({ req }) => {
  const [privateChatUserDetails, setPrivateChatUserDetails] = useState(null);

  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore();
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useModalStore();
  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(
        <UserInfo setPrivateChatUserDetails={setPrivateChatUserDetails} />,
      );
    },
    [openUserInfoModal, openModal],
  );

  return (
    <>
      <div className="">
        <div
          className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-1 py-2  md:px-3"
          onClick={() => {
            setIsPrivateChatOpen(true);
            setUserId(req?.relationUserId);
          }}
        >
          <div className="flex items-center gap-3">
            {/* <Image
              src={User}
              alt="Savannah Williams"
              className="h-[2.75rem] w-[2.75rem] rounded-full object-cover"
            /> */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                handleOpenUserInfoModal(req?.relationUserId);
              }}
              className="cursor-pointer"
            >
              <ChatAvatar
                profileImage={req?.relationUser?.imageUrl}
                userName={req?.relationUser?.username}
                imageClassName="h-12 w-12 rounded-full object-cover"
                avatarSize={48}
              />
            </div>
            <div className="text-steelTeal-200">
              <p className="text-sm font-bold">
                {' '}
                {req?.relationUser?.username}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-[18px] md:gap-1.5">
            {/* <button
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <AddFriend />
            </button> */}
            <FriendCall
              userId={req?.relationUserId}
              user={{
                username: req?.relationUser?.username,
                profileImage: req?.relationUser?.imageUrl,
              }}
            />
            <button>
              <MessageIcon />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default FriendsList;
