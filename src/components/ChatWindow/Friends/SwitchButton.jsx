import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

export default function SwitchButton({
  leftLabel = 'My Groups (23)',
  rightLabel = 'Public (84)',
  activeColor = 'text-white', // active label color
  inactiveColor = 'text-white', // inactive label color
  knobColor = 'bg-white', // default knob color
  switchBg = 'bg-black', // default container background
  borderColor = 'border-gray-500', // border color
  switchWidth = 'w-11', // switch width
  switchHeight = 'h-5', // switch height
  knobSize = 'h-4 w-4', // knob size
  onToggle,
  defaultLeft = true,
  showLeftLabel = true,
  showRightLabel = true,
  useDynamicRightLabel = false,
  onBgColor, // ON container bg
  offBgColor, // OFF container bg
  onKnobColor, // ON knob
  offKnobColor, // OFF knob
  onBaseBgColor, // ON switch base background
  offBaseBgColor, // OFF switch base background
  wrapperClassName,
  leftToggleText = '',
  rightToggleText = '',
  labelTextSize = 'text-xs',
  setValue = () => {},
}) {
  const [isLeftActive, setIsLeftActive] = useState(defaultLeft);

  useEffect(() => {
    setIsLeftActive(defaultLeft);
  }, [defaultLeft]);

  const handleToggle = (side) => {
    if (onToggle) {
      const allowToggle = onToggle(side === 'left');
      if (allowToggle === false) return;
    }
    setValue(side !== 'left');
    setIsLeftActive(side === 'left');
  };

  // Dynamic right label logic
  const displayRightLabel = useDynamicRightLabel
    ? isLeftActive
      ? leftToggleText
      : rightToggleText
    : rightLabel;

  // Container background
  const currentBg = isLeftActive
    ? onBgColor || switchBg
    : offBgColor || switchBg;

  // Knob background
  const currentKnob = isLeftActive
    ? onKnobColor || knobColor
    : offKnobColor || knobColor;

  // Switch base background
  const currentBaseBg = isLeftActive
    ? onBaseBgColor || 'bg-transparent'
    : offBaseBgColor || 'bg-transparent';

  return (
    <div
      className={`main-wrap flex items-center justify-between gap-2 rounded-full text-sm max-sm:px-2 ${wrapperClassName}`}
    >
      {/* Left Label */}
      {showLeftLabel && (
        <span
          className={`cursor-pointer ${labelTextSize} font-semibold transition-colors duration-300 ${
            isLeftActive ? activeColor : inactiveColor
          }`}
          onClick={() => handleToggle('left')}
        >
          {leftLabel}
        </span>
      )}

      {/* Switch */}
      <div
        onClick={() => handleToggle(isLeftActive ? 'right' : 'left')}
        className={`relative ${currentBg} ${switchWidth} ${switchHeight} flex items-center ${currentBaseBg} rounded-full border ${borderColor} cursor-pointer`}
      >
        <motion.div
          layout
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 25,
          }}
          className={`absolute ${knobSize} ${currentKnob} rounded-full shadow-md`}
          style={{
            left: isLeftActive ? '2px' : 'calc(100% - 1.1rem)',
          }}
        />
      </div>

      {/* Right Label */}
      {showRightLabel && (
        <span
          className={`cursor-pointer ${labelTextSize} font-semibold transition-colors duration-300 ${
            !isLeftActive ? activeColor : inactiveColor
          }`}
          onClick={() => handleToggle('right')}
        >
          {displayRightLabel}
        </span>
      )}
    </div>
  );
}
