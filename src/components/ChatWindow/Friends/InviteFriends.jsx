import React from 'react';
import SwitchButton from './SwitchButton';
import useGroupChatStore from '@/store/useGroupChatStore';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import useAuthModalStore from '@/store/useAuthModalStore';

export default function InviteFriends({
  isInviteSelected,
  setIsInviteSelected,
  myGroupsLength,
  publicGroupLength,
  invitesLength=0,
}) {
  const { setSelectedGroupTab } = useGroupChatStore();
  const { isAuthenticated } = useAuthStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useAuthModalStore();
  const handleSwitchChange = (isLeftActive) => {
    setIsInviteSelected(false);

    if (isLeftActive) {
      if (!isAuthenticated) {
        localStorage.setItem('activeTab', 1);
        setSelectedTab(1);
        openModal(<Auth />);
        return false;
      }
      setSelectedGroupTab('MYGroups');
    } else {
      setSelectedGroupTab('PublicGroups');
    }

    console.log('Is Left Active?', isLeftActive);
    return true;
  };

  return (
    <div className="bg-maastrichtBlue-1000 p-[0.625rem]">
      <div className="flex items-center gap-3 border-t border-white-300 pt-[0.625rem] max-sm:gap-4">
       
      <button
          className={`min-w-16 text-xs font-semibold  transition-colors duration-300 ${isInviteSelected ? 'text-[#e81c24]' : 'text-steelTeal-200'
            }`}
          onClick={() => {
            setIsInviteSelected(true);     
            setSelectedGroupTab(null);     
          }}
        >
          Invites ({invitesLength})
        </button>

        <div className="w-full rounded-[0.625rem] bg-erieBlack-300 p-2 font-semibold">
          <SwitchButton
            leftLabel={`My Groups (${myGroupsLength || 0})`}
            rightLabel={`Public (${publicGroupLength || 0})`}
            onToggle={handleSwitchChange}
            knobColor={isInviteSelected ? 'bg-gray-500' : 'bg-[#e81c24]'}
            defaultLeft={isAuthenticated}
            activeColor={isInviteSelected ? 'text-steelTeal-200' : 'text-[#e81c24]'}
            inactiveColor="text-steelTeal-200"
          />
        </div>
      </div>
    </div>
  );
}
