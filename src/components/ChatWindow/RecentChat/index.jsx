/* eslint-disable no-nested-ternary */
'use client';

import AddChatGroupIcon from '@/assets/icons/AddUserChat';
import FriendIcon from '@/assets/icons/FriendsIcon';
import GroupUser from '@/assets/icons/GroupUser';
import SearchIcon from '@/assets/icons/Search';
import SapphireDiamond from '@/assets/webp/sapphire-diamond.webp';
import {
  useCreateFriendsRequest,
  useGetFriendsListQuery,
  useGetFriendsRequestListQuery,
  userListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { useEffect, useRef, useState, useCallback } from 'react';
import FriendsList from '../Friends/FriendsList';
import Accordion from './Accordion';
import FriendRequestItem from './Accordion/FriendRequestItem';
import FriendsRecentChat from './FriendsRecentChat';
import { UserRound, Clock } from 'lucide-react';
import UserRoundPlus from '@/assets/icons/UserRoundPlus';
import toast from 'react-hot-toast';
import MessageIcon from '@/assets/icons/MessageIcon';
import IncomingMessages from '@/components/IncomingMessages';

function RecentChat() {
  const [search, setSearch] = useState('');
  const [totalMsg, setTotalMsg] = useState(0)
  const { isAuthenticated } = useAuthStore((state) => state);

  const { setIsPrivateChatOpen, setUserId, setSelectedRecentChatTab, selectedRecentChatTab } =
    usePrivateChatStore((state) => state);

  const {
    data: friendsList,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({ params: { search }, enabled: !!isAuthenticated });

  const {
    data: friendsRequestList,
    refetch: refetchFriendsRequestList,
  } = useGetFriendsRequestListQuery({
    params: { search },
    enabled: !!isAuthenticated,
  });

  const {
    data: userData,
    isLoading: isUserDataLoading,
    isFetchingNextPage,
    fetchNextPage: fetchNextUserPage,
    hasNextPage: hasNextUserPage,
    refetch: refetchUserList,
  } = userListQuery({
    params: { limit: 20, search },
    enabled: isAuthenticated,
  });

  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
  };

  const lastUserRef = useRef(null);
  const observerRef = useRef(null);
  const handleIntersection = useCallback((entries) => {
    const [entry] = entries;
    if (entry.isIntersecting && hasNextUserPage && !isFetchingNextPage) {
      fetchNextUserPage();
    }
  }, [hasNextUserPage, isFetchingNextPage, fetchNextUserPage]);

  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(handleIntersection, {
      root: null,
      rootMargin: '100px',
      threshold: 0.1,
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [handleIntersection]);

  useEffect(() => {
    const currentObserver = observerRef.current;
    const currentRef = lastUserRef.current;

    if (currentRef && currentObserver) {
      currentObserver.observe(currentRef);
    }

    return () => {
      if (currentRef && currentObserver) {
        currentObserver.unobserve(currentRef);
      }
    };
  }, [userData]);

  useEffect(() => {
    refetchUserList();
  }, [search, refetchUserList]);

  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      refetchUserList()
      toast.success(response?.data?.message);
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
    },
  });

  const sendFriendRequest = (id) => {
    mutationRequest.mutate({
      requesteeId: +id,
    });
  };

  const allUsers = userData?.pages.flatMap((page) => page.data.allUsers.rows) || [];
  const totalUsers = userData?.pages[0]?.data.allUsers.count || 0;

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center gap-1 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
        <div className="relative flex w-full items-center gap-2">
          <SearchIcon className="absolute left-[1.25rem] h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
          <input
            className="h-9 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 pl-[2.875rem] placeholder:text-xs placeholder:text-steelTeal-200 max-sm:placeholder:relative max-sm:placeholder:top-[-1px] max-sm:placeholder:text-[.8125rem]"
            placeholder="Search Users"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <button
            className="flex h-9 w-9 items-center justify-center rounded-[0.625rem] bg-secondaryBtnBg p-2"
            onClick={() => setSelectedRecentChatTab(true)}
          >
            <FriendIcon
              className="h-[2.2rem] w-12"
              fill={selectedRecentChatTab ? '#FC3E3E' : ''}
            />
          </button>
          <button
            className="flex h-9 w-9 items-center justify-center rounded-[0.625rem] bg-secondaryBtnBg p-2"
            onClick={() => setSelectedRecentChatTab(false)}
          >
            <AddChatGroupIcon
              className="h-5 w-5"
              fill={!selectedRecentChatTab ? '#FC3E3E' : ''}
            />
          </button>
        </div>
      </div>
      {selectedRecentChatTab ? (
        <FriendsRecentChat />
      ) : (
        <div>
          <Accordion
            title="Friend Requests"
            icon={AddChatGroupIcon}
            count={friendsRequestList?.count}
          >
            <div className="space-y-2">
              {friendsRequestList?.rows?.map((req, idx) => (
                <FriendRequestItem
                  key={idx}
                  req={req}
                  refetchFriendsRequestList={refetchFriendsRequestList}
                  img={idx === 0 ? SapphireDiamond : req.img}
                />
              ))}
              {friendsRequestList?.count === 0 && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                  No Friend Requests
                </p>
              )}
            </div>
          </Accordion>
          <Accordion
            title="Friends"
            icon={GroupUser}
            count={friendsList?.count}
            defaultOpen={true}
          >
            <div className="space-y-2">
              {friendsList?.rows?.length === 0 && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                  No Friends Found
                </p>
              )}
              {friendsList?.rows?.map((req, idx) => (
                <FriendsList key={idx} req={req} />
              ))}
            </div>
          </Accordion>
          <Accordion title="Find Or Add Users" icon={AddChatGroupIcon}>
            <div className="space-y-2">
              {isUserDataLoading && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                  Loading users...
                </p>
              )}
              {!isUserDataLoading && allUsers.length === 0 && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                  No Users Found
                </p>
              )}
              {allUsers.map((user, idx) => {
                const isLastUser = idx === allUsers.length - 1;
                return (
                  <div
                    key={user.id}
                    ref={isLastUser ? lastUserRef : null}
                    className="min-h-[40px]"
                  >
                    <FriendRequestItem
                      req={user}
                      customIcon={
                        <div className="relative inline-block ">
                          {user?.friendRequestStatus === false && (
                            <div className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-1 md:px-3  py-2 gap-3">
                              <div className='cursor-pointer' onClick={() => sendFriendRequest(user?.id)} >
                                <UserRoundPlus
                                  id="UserRoundPlusFriend"
                                  className="h-[20px] w-[20px] text-steelTeal-1000 hover:text-white-1000"
                                />
                              </div>
                              <div className='cursor-pointer' onClick={() => {
                                setIsPrivateChatOpen(true);
                                setUserId(user?.id);
                              }}
                              ><MessageIcon /></div>
                            </div>
                          )}

                          {user?.friendRequestStatus === 'pending' && (
                            <div className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-1 md:px-3  py-2 gap-3">
                              <div className="relative inline-block">
                                <UserRound
                                  size={20}
                                  className="text-steelTeal-1000"
                                />
                                <Clock
                                  size={12}
                                  className="bg-black text-white absolute -bottom-1 -right-0.5 rounded-full"
                                />
                              </div>
                              <div className='cursor-pointer' onClick={() => {
                                setIsPrivateChatOpen(true);
                                setUserId(user?.id);
                              }}
                              ><MessageIcon /></div>
                            </div>
                          )}
                        </div>
                      }
                      onClick={() => openChat(user.id)}
                    />
                  </div>
                );
              })}
              {isFetchingNextPage && (
                <p className="flex justify-center text-[13px] text-steelTeal-200">
                  Loading more users...
                </p>
              )}
            </div>
          </Accordion>
          <Accordion title="Incoming Messages" count={`Not added (${totalMsg})`} icon={MessageIcon}>
           <IncomingMessages search={search} isAuthenticated={isAuthenticated} setTotalMsg={setTotalMsg}/>
          </Accordion>
        </div>
      )}
    </div>
  );
}

export default RecentChat;