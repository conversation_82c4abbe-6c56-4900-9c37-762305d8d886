import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';

export default function Accordion({
  title,
  count,
  children,
  icon: Icon,
  defaultOpen = false,
}) {
  const [open, setOpen] = useState(defaultOpen);
  const [maxHeight, setMaxHeight] = useState(defaultOpen ? 'none' : '0px');
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current) {
      if (open) {
        setMaxHeight(`${contentRef.current.scrollHeight}px`);
        const timer = setTimeout(() => {
          if (open) setMaxHeight('none');
        }, 300); 
        return () => clearTimeout(timer);
      } else {
        setMaxHeight('0px');
      }
    }
  }, [open, children]); 

  useEffect(() => {
    if (defaultOpen && contentRef.current) {
      setMaxHeight('none');
    }
  }, [defaultOpen]);

  const toggleAccordion = () => {
    if (contentRef.current && open) {
      setMaxHeight(`${contentRef.current.scrollHeight}px`);
      contentRef.current.offsetHeight;
      setTimeout(() => setMaxHeight('0px'), 10);
    }
    setOpen(!open);
  };

  return (
    <div className="text-white rounded-lg">
      {/* Accordion Header */}
      <div
        className="flex cursor-pointer items-center justify-between rounded-t-lg px-3 py-1.5"
        onClick={toggleAccordion}
      >
        <h4 className="flex items-center gap-2 text-[.8125rem] font-bold text-steelTeal-200 ">
          {Icon && <Icon className="h-5 w-5 [&_*]:fill-steelTeal-200" />}
          {title} {typeof count === 'number' ? `(${count})` : ''}
        </h4>
        <h4 className="flex items-center gap-2 text-[.8125rem] font-bold text-steelTeal-200" >
        {typeof count !== 'number' ? count : ''}
        </h4>
        {open ? (
          <ChevronDown className="text-steelTeal-200" />
        ) : (
          <ChevronUp className="text-steelTeal-200" />
        )}
      </div>

      {/* Accordion Body with Smooth Transition */}
      <div
        ref={contentRef}
        style={{
          maxHeight: maxHeight,
        }}
        className="overflow-hidden transition-all duration-300 ease-in-out"
      >
        <div className="px-3 py-2">{children}</div>
      </div>
    </div>
  );
}
