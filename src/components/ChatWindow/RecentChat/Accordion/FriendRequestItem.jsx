import ChatAvatar from '@/components/ChatAvatar';
import UserInfo from '@/components/UserInfoModal';
import { useUpdateFriendsRequest } from '@/reactQuery/chatWindowQuery';
import useModalStore from '@/store/useModalStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useQueryClient } from '@tanstack/react-query';
import { CircleCheck, CircleX } from 'lucide-react';
import React from 'react';
import toast from 'react-hot-toast';

export default function FriendRequestItem({
  req,
  refetchFriendsRequestList,
  name,
  username,
  avatar,
  showStatus = true,
  customIcon = null,
}) {
  const queryClient = useQueryClient();
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const { openModal } = useModalStore((state) => state);

  const handleOpenUserInfoModal = (userId) => {
    openUserInfoModal(userId);
    openModal(<UserInfo />);
  };

  const mutationUpdateRequest = useUpdateFriendsRequest({
    onSuccess: (response, variables) => {
      toast.success(response?.data?.message);
      // refetchFriendsList();
      refetchFriendsRequestList();
      queryClient.invalidateQueries({ queryKey: ['GET_FRIEND_LIST_QUERY'] });
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      // refetchFriendsList();
      refetchFriendsRequestList();
    },
  });
  const handleFriendRequest = (requestId, status) => {
    mutationUpdateRequest.mutate({
      requestId,
      status,
    });
  };
  return (
    <div className="flex items-center justify-between rounded-[.25rem] bg-steelTeal-800 px-3 py-2 ">
      <div className="flex items-center gap-3">
        {/* <img
          src={avatar}
          alt={req?.userFriendRequester?.username}
          className="h-10 w-10 rounded-full object-cover"
        /> */}
        <div
          className="cursor-pointer"
          onClick={() =>
            handleOpenUserInfoModal(req?.requesterUserId || req?.id)
          }
        >
          <ChatAvatar
            profileImage={
              req?.userFriendRequester?.imageUrl ||
              req?.userFriendRequestee?.imageUrl ||
              req?.imageUrl
            }
            userName={
              req?.userFriendRequester?.username ||
              req?.userFriendRequestee?.username ||
              req?.username
            }
            imageClassName="h-12 w-12 rounded-full object-cover"
            avatarSize={48}
          />
        </div>
        <div className="text-steelTeal-200">
          <p className="text-sm font-bold">
            {req?.userFriendRequester?.username ||
              req?.userFriendRequestee?.username ||
              req?.username}
          </p>
          <p className="text-[.8125rem]">
            {req?.userFriendRequester?.firstName ||
              req?.userFriendRequestee?.firstName ||
              req?.firstName}
          </p>
        </div>
      </div>

      {customIcon ? (
        <div className="flex items-center gap-2">{customIcon}</div>
      ) : (
        showStatus && (
          <div className="flex items-center gap-3">
            <button>
              <CircleCheck
                className="h-6 w-6 text-steelTeal-200"
                onClick={() => handleFriendRequest(req.id, 'accepted')}
              />
            </button>
            <button className="text-white ">
              <CircleX
                className="h-6 w-6 text-steelTeal-200"
                onClick={() => handleFriendRequest(req.id, 'rejected')}
              />
            </button>
          </div>
        )
      )}
    </div>
  );
}
