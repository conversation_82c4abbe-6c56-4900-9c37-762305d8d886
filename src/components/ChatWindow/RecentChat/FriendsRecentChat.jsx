import AddFriend from '@/assets/icons/AddFriend';
import MessageIcon from '@/assets/icons/MessageIcon';
import ChatAvatar from '@/components/ChatAvatar';
import { useGetRecentChatsQuery } from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useCallback, useEffect, useState } from 'react';
import UserInfo from '@/components/UserInfoModal';
import FriendCall from '../Friends/FriendCall';

const FriendsRecentChat = () => {
  const { isAuthenticated } = useAuthStore((state) => state);
  const [search, setSearch] = useState('');
  const [privateChatUserDetails, setPrivateChatUserDetails] = useState(null);

  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore();
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useModalStore();
  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(
        <UserInfo setPrivateChatUserDetails={setPrivateChatUserDetails} />,
      );
    },
    [openUserInfoModal, openModal],
  );
  const {
    data: recentChats,
    isLoading,
    refetch,
  } = useGetRecentChatsQuery({
    params: {
      page: 1,
      limit: 400,
      search: search || '',
    },
    enabled: isAuthenticated, // Query fetches only if authenticated
  });
  useEffect(() => {
    if (isAuthenticated) {
      refetch();
    }
  }, []);
  console.log('🚀 ~ FriendsRecentChat ~ recentChats:', recentChats);
  return (
    <div className="">
      {recentChats?.map((recentChat) => (
        <div
          className="mb-2 flex items-center justify-between rounded-[0.25rem] bg-steelTeal-800 px-3 py-2"
          onClick={() => {
            setIsPrivateChatOpen(true);
            setUserId(recentChat?.recipient_id);
          }}
        >
          <div className="flex items-center gap-3">
            {/* <Image
              src={User}
              alt="Savannah Williams"
              className="h-[2.75rem] w-[2.75rem] rounded-full object-cover"
            /> */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                handleOpenUserInfoModal(recentChat?.recipient_id);
              }}
              className="cursor-pointer"
            >
              <ChatAvatar
                profileImage={recentChat?.['recipientUser.image_url']}
                userName={recentChat?.['recipientUser.receiver_name']}
                imageClassName="h-[2.75rem] w-[2.75rem] rounded-full object-cover"
                avatarSize={44}
              />
            </div>
            <div className="text-steelTeal-200">
              <p className="text-sm font-bold">
                {' '}
                {recentChat?.['recipientUser.receiver_name']}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {recentChat?.areFriends && (
              <FriendCall
                userId={recentChat?.recipient_id}
                user={{
                  username: recentChat?.['recipientUser.receiver_name'],
                  profileImage: recentChat?.['recipientUser.image_url'],
                }}
              />
            )}

            <button onClick={()=>{
               setIsPrivateChatOpen(true);
               setUserId(recentChat?.recipient_id);
            }}><MessageIcon /></button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FriendsRecentChat;
