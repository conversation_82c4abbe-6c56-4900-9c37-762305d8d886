'use client';

import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { Grid } from '@giphy/react-components';
import { CircleArrowDown, MessageCircleMore } from 'lucide-react';
import Image from 'next/image';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useUserInfoStore from '@/store/useUserInfoStore';

import { useGrabRainDrop } from '@/hooks/useGrabRainDrop';
import { useGetGroupListQuery } from '@/reactQuery/chatWindowQuery';

import FriendsIcon from '@/assets/icons/Friends';
import countryFlag from '../../assets/images/stock-images/flag-uk.png';

import useGroupChatWindow from '@/hooks/useGroupChatWindow';
import useAuthTab from '@/store/useAuthTab';
import useChatWindow from '../../hooks/useChatWindow';
import Auth from '../Auth';
import IconButton from '../Common/Button/IconButton';
import ReactTooltip from '../Common/ReactTooltip';
import NotificationPopup from '../NotificationPopup';
import UserInfo from '../UserInfoModal';
import Chat from './Chat';
import { ChatInput } from './ChatInput';
import Friends from './Friends';
import GroupChat from './GroupChat';
import GroupChatMessage from './GroupChatMessages';
import { NavTabs } from './Navtabs';
import PubliChatSection from './PubliChatSection';
import RecentChat from './RecentChat';
import useAuthModalStore from '@/store/useAuthModalStore';

const ChatHeader = memo(
  ({ section, setSection, isAuthenticated, openModal }) => {
    const { setSelectedTab } = useAuthTab((state) => state);

    return (
      <div className="flex h-[3.75rem] items-center justify-between gap-2 bg-cetaceanBlue-1000 px-2.5 py-5 shadow-chat-header">
        <button
          type="button"
          className="flex items-center justify-center gap-2 rounded-md border-2 border-solid border-transparent bg-transparent px-2.5 py-1.5 text-[0.813rem] text-white-1000"
        >
          <span className="d-block h-5 w-5">
            <Image
              src={countryFlag}
              width={10000}
              height={10000}
              className="w-[1.875rem] max-w-full"
              alt="Country flag"
            />
          </span>
          <span>EN</span>
        </button>
        <div className="flex justify-end gap-2">
          <div className="flex gap-4">
            <NotificationPopup />

            <ReactTooltip message="Friends" id="friends-tooltip" />
            <IconButton
              id="friends-tooltip"
              onClick={() => {
                if (!isAuthenticated) {
                  localStorage.setItem('activeTab', 0);
                  setSelectedTab(0);
                  openModal(<Auth />);
                  return;
                }
                if (section === 'Friends') setSection('PublicChat');
                else setSection('Friends');
              }}
            >
              <FriendsIcon
                className={`${
                  section === 'Friends'
                    ? 'fill-white-1000'
                    : 'fill-steelTeal-1000'
                } transition-all duration-300 group-hover:fill-white-1000`}
              />
            </IconButton>
          </div>
        </div>
      </div>
    );
  },
);
ChatHeader.displayName = 'ChatHeader';

// Main component
export default function ChatWindow() {
  const [privateChatUserDetails, setPrivateChatUserDetails] = useState(null);

  const { userDetails, isAuthenticated } = useAuthStore();
  const { openChat, setOpenChat } = useGeneralStore();
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useAuthModalStore();
  const { isPrivateChatOpen } = usePrivateChatStore();
  const { isGroupChatOpen } = useGroupChatStore();
  const { grabRainDrop } = useGrabRainDrop();
  const pickerRef = useRef(null);

  const emojiButtonRef = useRef(null);
  const gifButtonRef = useRef(null);

  const {
    message,
    setMessage,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    showGifPicker,
    section,
    setSection,
    showSuggestions,
    selectedSuggestion,
    publicChats,
    isPublicChatsLoading,
    chatContainerRef,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    privateChat,
    recipientUser,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    livePlayersCount,
    newMessagesCount,
    scrollToBottom,
    handleGifSelect,
    fetchGifs,
    setGrabbedChat,
    updateGrabbedRainChat,
    setShowGifPicker,
  } = useChatWindow();

  useGroupChatWindow({
    isGroupChat: section === 'GroupChat',
    isGroupPage: true,
    isPrivatePage: true,
    isPrivateChat: section === 'PrivateChat',
  });

  const { data: groupListData } = useGetGroupListQuery({
    search: '',
    enabled: !!isAuthenticated,
  });

  useEffect(() => {
    if (gifMessage && inputRef.current) {
      inputRef.current.focus();
    }
  }, [gifMessage, inputRef]);

  const groupTotal = useMemo(() => groupListData?.total || 0, [groupListData]);

  const renderMainContent = useCallback(() => {
    // if (isPublicChatsLoading) {
    //   return (
    //     <div className="flex h-full w-full items-center justify-center">
    //       <Lottie
    //         animationData={CommonLoader}
    //         loop
    //         className="max-h-10 min-h-10 w-20"
    //       />
    //     </div>
    //   );
    // }
    useEffect(() => {
      function handleClickOutside(event) {
        if (pickerRef.current && !pickerRef.current.contains(event.target)) {
          if (
            showEmojiPicker &&
            emojiButtonRef &&
            !emojiButtonRef?.current.contains(event.target)
          ) {
            setShowEmojiPicker(false);
          }
          if (
            showGifPicker &&
            gifButtonRef &&
            !gifButtonRef?.current.contains(event.target)
          ) {
            setShowGifPicker(false);
          }
        }
      }

      if (showEmojiPicker || showGifPicker) {
        document.addEventListener('mousedown', handleClickOutside);
      } else {
        document.removeEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showEmojiPicker, setShowEmojiPicker, showGifPicker, setShowGifPicker]);
    return (
      <div className="flex h-full w-full flex-col">
        {/* <ChatHeader section={section} setSection={setSection}  isAuthenticated ={isAuthenticated} openModal= {openModal}/> */}
        <NavTabs
          section={section}
          setSection={setSection}
          newMessagesCount={newMessagesCount}
          groupTotal={groupTotal}
          isAuthenticated={isAuthenticated}
        />

        <div className="relative flex min-h-0 shrink grow basis-[0%] flex-col">
          <div
            ref={chatContainerRef}
            className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden bg-erieBlack-300 pt-[.125rem]"
          >
            {section === 'PublicChat' && <PubliChatSection />}
            {section === 'Friends' && <Friends />}
            {section === 'PrivateChat' &&
              (isPrivateChatOpen ? (
                // <PrivateChat
                //   privateChat={privateChat}
                //   recipientUser={recipientUser}
                //   privateChatUserDetails={privateChatUserDetails}
                // />
                <Chat />
              ) : (
                <RecentChat />
              ))}
            {section === 'GroupChat' &&
              (isGroupChatOpen ? (
                // <GroupChatConversion />
                <GroupChatMessage />
              ) : (
                <GroupChat />
              ))}
          </div>

          {showEmojiPicker && (
            <div ref={pickerRef}>
              <Picker
                autoFocus={false}
                data={data}
                onEmojiSelect={(emoji) => {
                  // setShowEmojiPicker(false);
                  if (gifMessage) {
                    setGifMessage(null);
                  }
                  setMessage(message + emoji.native);
                }}
                theme="dark"
              />
            </div>
          )}

          {showGifPicker && (
            <div className="absolute bottom-[2.5rem]  left-1/2 z-50   -translate-x-1/2 bg-black-1000 p-2">
              <div style={{ height: 300, overflowY: 'scroll' }}>
                <div ref={pickerRef}>
                  <Grid
                    fetchGifs={fetchGifs}
                    width={300}
                    className="mx-auto"
                    columns={2}
                    gutter={6}
                    onGifClick={handleGifSelect}
                  />
                </div>
              </div>
            </div>
          )}

          {(section === 'PublicChat' ||
            isPrivateChatOpen ||
            isGroupChatOpen) && (
            <ChatInput
              message={message}
              setMessage={setMessage}
              gifMessage={gifMessage}
              setGifMessage={setGifMessage}
              showEmojiPicker={showEmojiPicker}
              setShowEmojiPicker={setShowEmojiPicker}
              showGifPicker={showGifPicker}
              showSuggestions={showSuggestions}
              selectedSuggestion={selectedSuggestion}
              handleKeyDown={handleKeyDown}
              selectSuggestion={selectSuggestion}
              handleSendMessage={handleSendMessage}
              handleInputChange={handleInputChange}
              handleGifPickerToggle={handleGifPickerToggle}
              handleEmojiPickerToggle={handleEmojiPickerToggle}
              inputRef={inputRef}
              suggestionsRef={suggestionsRef}
              tagSuggestion={tagSuggestion}
              livePlayersCount={livePlayersCount}
              section={section}
              isAuthenticated={isAuthenticated}
              emojiButtonRef={emojiButtonRef}
              gifButtonRef={gifButtonRef}
            />
          )}

          {newMessagesCount > 0 && section === 'PublicChat' && (
            <button
              type="button"
              onClick={() => scrollToBottom(true)}
              className="absolute bottom-32 right-2 flex items-center gap-2 rounded-xl bg-tiber-1000 p-2"
            >
              <MessageCircleMore className="text-white-1000 transition-all duration-300" />
              New Messages
              <CircleArrowDown className="text-white-1000 transition-all duration-300" />
            </button>
          )}
        </div>
      </div>
    );
  }, [
    isPublicChatsLoading,
    section,
    setSection,
    newMessagesCount,
    groupTotal,
    publicChats,

    userDetails,
    openModal,
    privateChat,
    recipientUser,
    privateChatUserDetails,
    isPrivateChatOpen,
    isGroupChatOpen,
    showEmojiPicker,
    showGifPicker,
    message,
    setMessage,
    setShowEmojiPicker,
    fetchGifs,
    handleGifSelect,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    livePlayersCount,
    showSuggestions,
    selectedSuggestion,
    gifMessage,
    setGifMessage,
    chatContainerRef,
    scrollToBottom,
  ]);

  return (
    <div className="">
      {/* <button
        type="button"
        className={`${openChat ? 'right-[20.5rem]' : 'right-0'} group fixed bottom-[1.125rem] z-[41] hidden h-8 w-8 min-w-8 items-center justify-center rounded-l bg-oxfordBlue-1000 transition-all duration-300 hover:bg-primary-1000 active:scale-90 xl:flex`}
        onClick={() => setOpenChat(!openChat)}
      >
        {openChat ? (
          <ArrowRightIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        ) : (
          <ChatIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        )}
      </button> */}

      <section
        className={` ${openChat ? 'right-0 border-l-[1px] border-white-200' : 'right-[-100%] xl:right-[-20.5rem]'} blurColorChatWindow fixed top-0 z-20 !h-dvh w-full xs:max-w-chatWidth   md:top-headerHeight md:h-[calc(100dvh-var(--menu-footer-height)-var(--header-height))] lg:top-0 lg:h-dvh max-lg:!h-[calc(100dvh-50px)]`}
      >
        {renderMainContent()}
      </section>
    </div>
  );
}
