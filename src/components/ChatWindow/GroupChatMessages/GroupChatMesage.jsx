'use client';
import { CircleX } from 'lucide-react';
import { <PERSON><PERSON>he<PERSON> } from 'lucide-react';
import { ChevronLeft } from 'lucide-react';
import Image from 'next/image';
import React from 'react';
import DemoUser from '@/assets/webp/demo-user.webp';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import AddFriend from '@/assets/icons/AddFriend';
import MessageIcon from '@/assets/icons/MessageIcon';
import useGroupChatStore from '@/store/useGroupChatStore';

const GroupChatMessages = () => {
  const { setIsGroupChatOpen } = useGroupChatStore();
  return (
    <>
      <div className="bg-erieBlack-300 flex items-center justify-between rounded-sm p-1 px-2">
        <div className="flex items-center">
          <button className="" onClick={() => setIsGroupChatOpen(false)}>
            <ChevronLeft />
          </button>
          <div className="flex items-center justify-center gap-1">
            <div className="h-[1.875rem] w-[1.875rem] overflow-hidden rounded-full">
              <Image src={DemoUser} alt="User" className="h-full w-full" />
            </div>
            <h4 className="text-xs font-bold text-slate-200">New Group</h4>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button>
            <AddFriend />
          </button>
          <button>
            <MessageIcon />
          </button>
        </div>
      </div>
      <div className="px-[0.625rem] py-[.375rem]">
        {/* SENDOR */}
        <div className="text-white bg-erieBlack-200 relative  my-[0.313rem] flex max-w-[16.625rem] items-center rounded-[.25rem] px-[0.625rem] py-[.4375rem]">
          <p className="min-w-[12.75rem] max-w-[12.75rem]  text-[.8125rem]">
            Hello Dan lets play
          </p>
          <span className="text-[0.625rem] text-steelTeal-500">10:25 AM</span>
        </div>
        {/* RECIVER */}
        <div class="my-[0.313rem] flex justify-end">
          <div class="text-white relative inline-block w-auto max-w-[16.4375rem] rounded-[.25rem] bg-secondaryBtnBg px-[0.625rem] py-[.4375rem]">
            <p class="text-[.8125rem]">
              Greetings Mike, I am sending a long message here to show how it
              should look
            </p>
            <div className="flex justify-end">
              <span className="text-[0.625rem] text-steelTeal-500">
                10:25 AM
              </span>
            </div>
          </div>
        </div>
        {/* SENDOR */}
        <div className="text-white bg-erieBlack-200 relative my-[0.313rem] flex max-w-[16.625rem] items-center rounded-[.25rem] px-[0.625rem] py-[.4375rem]">
          <p className="min-w-[12.75rem] max-w-[12.75rem]  text-[.8125rem]">
            Hello Dan lets play
          </p>
          <span className="text-[0.625rem] text-steelTeal-500">10:25 AM</span>
        </div>
      </div>
    </>
  );
};

export default GroupChatMessages;
