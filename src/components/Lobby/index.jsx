'use client';

import { useEffect, useRef } from 'react';
import useGeneralStore from '@/store/useGeneralStore';
import LobbySection from '../HomePage/Lobby';

const Lobby = () => {
  const { openLobbyMenu, openMenu, openChat } = useGeneralStore();
  const asideRef = useRef(null);

  useEffect(() => {
    if (openLobbyMenu && asideRef.current) {
      asideRef.current.scrollTop = 0;
    }
  }, [openLobbyMenu]);

  return (
    <aside
      ref={asideRef}
      id="default-sidebar"
      className={`fixed top-[52px] z-10 h-[calc(100%-52px)] overflow-y-auto bg-black-1000 px-4 pt-0 transition-all duration-300 ease-in-out max-sm:!px-1 max-sm:pb-16 max-sm:pl-[0.625rem] max-sm:pt-2
        ${openLobbyMenu ? 'left-0 w-full' : 'left-[-100%] w-full'}
        ${openChat ? ' xl:mr-[20.5rem] xl:w-[calc(100%-14.75rem-20.5rem)]' : 'xl:mr-[0rem] xl:w-[calc(100%-14.75rem-0rem)]'}
        ${openMenu ? 'xl:ml-[3.6rem] xl:w-[calc(100%-14.75rem-9.5rem)]' : 'xl:ml-[15rem] xl:w-[calc(100%-14.75rem-21rem)] xl:px-4'}
        ${
          openMenu && openChat
            ? 'xl:w-[calc(100%-23.75rem)]'
            : openMenu
              ? '!mx-0 xl:!w-full'
              : openChat
                ? ''
                : 'xl:w-[calc(100%-15rem)]'
        }
        ${openLobbyMenu ? 'xl:left-0' : 'xl:left-[-100%]'}
        scrollbar-hide
      `}
      aria-label="Lobby"
    >
      <div
        className={`${openChat ? '' : 'mx-auto w-full max-w-containerWidth'}`}
      >
        <LobbySection />
      </div>
    </aside>
  );
};

export default Lobby;
