import React, { useState } from 'react';
import Select from 'react-select';
import Image from 'next/image';
import { useGetFriendsListQuery } from '@/reactQuery/chatWindowQuery';
import UserImg from '../../../public/assets/demo-image/group-img.jpg';
import ChatAvatar from '../ChatAvatar';

// Custom ValueContainer component
const CustomValueContainer = ({ children, ...props }) => {
  const [values, input] = children || [[], null];

  return (
    <div className="chatMulti-inner-select__group w-full rounded-md bg-inputBgColor">
      <div className='before:bg-chatSearchIcon chatMulti-inner-select__input-container-section relative mx-0 mb-0 mt-0 h-[42px] w-full basis-full rounded-lg  bg-inputBgColor px-9 before:absolute before:left-3 before:top-1/2 before:z-[1] before:h-5 before:w-5 before:-translate-y-1/2 before:rounded-md before:bg-center before:bg-no-repeat before:content-[""]'>
        {input}
      </div>
      {values && values.length > 0 && (
        <div
          className={`chatMulti-inner-select__multiOption flex flex-wrap gap-x-2 gap-y-1 bg-maastrichtBlue-1000 py-4`}
        >
          {values}
        </div>
      )}
    </div>
  );
};

// Define the main component
function MultiSelect({ className = '', classNamePrefix = '', onChange }) {
  const { data: friendsList } = useGetFriendsListQuery({
    enabled: true,
    params: { search: '' },
  });

  const [selectedValues, setSelectedValues] = useState([]);

  const handleChange = (selectedOptions) => {
    // Extract only the `value` (user IDs) from the selected options
    const selectedUserIds = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setSelectedValues(selectedOptions);
    onChange(selectedUserIds); // Pass the selected user IDs back to parent component
  };

  const dynamicOptions =
    friendsList && friendsList.rows
      ? friendsList.rows.map((friend) => ({
          value: friend.relationUserId,
          label: (
            <div className="flex items-center gap-1 ">
              {/* <Image
                src={friend.relationUser.profileImage || UserImg}
                alt={friend.relationUser.firstName}
                className="h-4 w-4 rounded-full"
                width="100"
                height="100"
              /> */}
              <ChatAvatar
                profileImage={friend?.relationUser?.imageUrl}
                userName={friend?.relationUser.username}
                imageClassName=" h-4 w-4 rounded-full object-cover"
                avatarSize={16}
              />
              <h6 className="pt-[3px] text-sm leading-none text-white-1000">
                {friend.relationUser.username}
              </h6>
            </div>
          ),
        }))
      : [];

  return (
    <Select
      closeMenuOnSelect={false}
      isMulti
      options={dynamicOptions}
      className={`${className}`}
      classNamePrefix={`${classNamePrefix}`}
      defaultMenuIsOpen={false}
      components={{
        ValueContainer: CustomValueContainer,
      }}
      value={selectedValues}
      onChange={handleChange}
    />
  );
}

export default MultiSelect;
