'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { useGetFavoritesGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useGameStore from '@/store/useGameStore';
import { slugify } from '@/utils/helper';
import FavoriteIcon from '@/assets/icons/Favorite';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import GameSection from '../GameSection';

function Favorites({ isCarousel = false }) {
  const router = useRouter();
  const searchQuery = useGameStore((state) => state.searchQuery);
  const { isAuthenticated } = useAuthStore((state) => state);

  const {
    data: favoritesGames,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetFavoritesGamesQuery({
    enabled: isAuthenticated,
    gameName: searchQuery,
    limit: 10,
  });

  return (
    <GameSection
      title="Favorites"
      icon={<FavoriteIcon activeMenu />}
      games={favoritesGames?.games}
      isLoading={isLoading}
      isCarousel={isCarousel}
      viewAllPath="/favorites"
      emptyMessage="Give a heart to the games you like anytime. Your favorite games will be shown here"
      renderGame={(game) => ({
        id: game?.casinoGame?.id,
        image: game?.casinoGame?.iconUrl ?? '',
        name: game?.casinoGame?.name?.EN || game?.casinoGame?.name,
        isFavorite: true,
        provider: game?.casinoGame?.casinoProvider?.name,
      })}
      onGameClick={(provider, name) =>
        router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`)
      }
      loadMoreButton={
        hasNextPage && (
          <div className="mt-8 flex items-center justify-center">
            <PrimaryButton
              onClick={() => fetchNextPage()}
              disabled={!hasNextPage || isFetchingNextPage}
              variant="shade"
            >
              {isFetchingNextPage
                ? 'Loading...'
                : hasNextPage
                  ? 'Load More'
                  : 'Nothing more to load'}
            </PrimaryButton>
          </div>
        )
      }
    />
  );
}

export default Favorites;
