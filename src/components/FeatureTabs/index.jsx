'use client';

import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import CasinoTabIcon from '@/assets/icons/CasinoTabIcon';
import SportTabIcon from '@/assets/icons/SportTabIcon';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import useGeneralStore from '@/store/useGeneralStore';

export default function CustomTabs() {
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState('casino');
  const { setOpenMenu } = useGeneralStore();
  const tabs = [
    { id: 'casino', label: 'Casino', href: '/casino' },
    { id: 'sports', label: 'Sports', href: '/sports' },
  ];

  useEffect(() => {
    if (pathname !== '/sports') setActiveTab('casino');
    else setActiveTab('sports');
  }, [pathname]);
  return (
    <div className="w-full max-w-full">
      <div className="relative inline-flex h-10 w-full overflow-hidden rounded-px_10 border-[1.5px] border-solid border-primaryBorder bg-sidebarBgColor">
        {tabs.map((tab) => {
          // const isActive = pathname === tab.href;

          return (
            <Link
              key={tab.id}
              href={tab.href}
              className={`relative z-10 w-full px-4 py-2 text-center text-sm font-semibold transition-colors duration-300 ${
                activeTab == tab.id ? 'text-black-1000' : 'text-white'
              }`}
            >
              {activeTab == tab.id && (
                <motion.div
                  layoutId="activeBackground"
                  className="absolute inset-0 z-[-1] bg-TintGoldGradient"
                  transition={{ stiffness: 500, damping: 30 }}
                />
              )}
              <span className="relative z-20">{tab.label}</span>

              {tab.id === 'casino' && (
                <CasinoTabIcon
                  className="absolute bottom-0 left-0"
                  opacity={activeTab == 'casino' ? '0.4' : '0.08'}
                />
              )}
              {tab.id === 'sports' && (
                <SportTabIcon
                  className="absolute bottom-0 left-0"
                  opacity={activeTab == 'sports' ? '0.4' : '0.08'}
                />
              )}
            </Link>
          );
        })}
      </div>
    </div>
  );
}
