'use client';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useEffect, useRef, useMemo, useCallback } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

import useSignUp from '@/hooks/useSignUp';
import { useSocketManager } from '@/hooks/useSocketManager';
import { useUserProfileQuery } from '@/reactQuery/authQuery';
import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import { playerActivitySocket } from '@/utils/socket';

import HeaderLogos from './HeaderLogos';
import back from '@/assets/images/svg-images/back.svg';
import { useGetRecentGamesQuery } from '@/reactQuery/gamesQuery';
import useGameStore from '@/store/useGameStore';

// --- Dynamic Components (loaded only when needed) ---
const HeaderLoginMenuItem = dynamic(() => import('./HeaderLoginMenuItem'), {
  ssr: false,
});
const HeaderAuthButtons = dynamic(() => import('./HeaderAuthButtons'), {
  ssr: false,
});
const HeaderMenuItems = dynamic(() => import('./HeaderMenuItems'), {
  ssr: false,
});

// const MIN_WIDE_SCREEN_WIDTH = 1280;

function Header() {
  const {
    isAuthenticated,
    setUserDetails,
    setUserWallet,
    hasRehydrated,
    userDetails,
  } = useAuthStore();

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  useSocketManager();
  const { twitchMutation, discordMutation } = useSignUp();

  const {
    openChat,
    setOpenChat,
    setActiveMenu,
    setOpenMenu,
    openMenu,
    openLobbyMenu,
    setOpenLobby,
    activePreviousState,
  } = useGeneralStore();
  const setRecentGames = useGameStore((state) => state.setRecentGames);

  const prevPath = useRef(pathname);

  // Track route changes for lobby/game handling
  useEffect(() => {
    if (prevPath.current !== pathname) {
      setOpenLobby(false);
      sessionStorage.setItem('lobby', 'false');

      if (prevPath.current.startsWith('/casino/games')) {
        playerActivitySocket.emit('USER_LEFT_GAMEPLAY', {
          userId: userDetails?.id,
        });
      }

      prevPath.current = pathname;
    }
  }, [pathname, setOpenLobby, userDetails]);

  // Fetch user profile when authenticated
  const { data: userProfile } = useUserProfileQuery({
    enabled: isAuthenticated,
  });

  // Fetch recent games when authenticated 
  const { data: recentGames, isLoading } = useGetRecentGamesQuery({
    enabled: isAuthenticated,
    search: "",
  });

  useEffect(() => {
    if (!isLoading) {
      setRecentGames(recentGames || []);
    }
  }, [recentGames, isLoading, setRecentGames]);


  // Handle Twitch/Discord signup callbacks
  useEffect(() => {
    const code = searchParams.get('code');
    if (!code) return;

    const timeout = setTimeout(() => {
      if (pathname === '/discord-login') {
        discordMutation.mutate({ code, isSignup: true });
      } else if (pathname === '/twitch-signup') {
        twitchMutation.mutate({ code, isSignup: true });
      }
    }, 500);

    return () => clearTimeout(timeout);
  }, [pathname, searchParams, discordMutation, twitchMutation]);

  // Clear category filter when leaving category pages
  useEffect(() => {
    if (!pathname.includes('category')) {
      localStorage.removeItem('selectedCategory');
    }
  }, [pathname]);

  // Restore lobby state from session
  useEffect(() => {
    if (sessionStorage.getItem('lobby') === 'true') {
      setOpenLobby(true);
    }
  }, [setOpenLobby]);

  // Handle welcome screen state
  useEffect(() => {
    const sessionKey = 'welcomeShown';

    if (isAuthenticated) {
      const hasSeenWelcome = sessionStorage.getItem(sessionKey);
      sessionStorage.setItem(sessionKey, hasSeenWelcome ? 'false' : 'true');
    } else {
      sessionStorage.removeItem(sessionKey);
    }
  }, [isAuthenticated, pathname]);

  // Update auth store when profile is fetched
  useEffect(() => {
    if (!userProfile) return;

    setUserDetails(userProfile);
    const defaultWallet = userProfile?.wallets?.find(
      (wallet) => wallet.default,
    );
    if (defaultWallet) setUserWallet(defaultWallet);
  }, [userProfile, setUserDetails, setUserWallet]);

  // Resize handler for chat panel
  // const handleResize = useCallback(() => {
  //   const isWideScreen = window.matchMedia(
  //     `(min-width: ${MIN_WIDE_SCREEN_WIDTH}px)`,
  //   ).matches;
  //   setOpenChat(isWideScreen);
  // }, [setOpenChat]);

  // useEffect(() => {
  //   handleResize(); // run on mount
  //   window.addEventListener('resize', handleResize);
  //   return () => window.removeEventListener('resize', handleResize);
  // }, [handleResize]);

  useEffect(() => {
    const isMobile = () => {
      return (
        window.innerWidth <= 768 || /Mobi|Android/i.test(navigator.userAgent)
      );
    };
    if (isMobile()) {
      setOpenChat(false);
    }
  }, []);

  // Prevent rendering until hydration completes
  if (!hasRehydrated) return null;

  const headerClasses = useMemo(() => {
    let base = `
      header-blur fixed left-0 top-0 z-50 flex h-[52px] w-full items-center justify-center 
      border-b border-solid border-white-200 bg-oxfordBlue-1000 px-4 transition-all duration-300 ease-in-out 
      md:h-headerHeight max-md:px-1 xl:z-40 xl:shadow-header
    `.trim();

    if (!isAuthenticated) base += ' !z-[42]';

    base += openChat
      ? ' xl:mr-[20.5rem] xl:w-[calc(100%-14.75rem-20.5rem)]'
      : ' xl:mr-0 xl:w-[calc(100%-14.75rem)]';

    base += openMenu
      ? ' xl:ml-[4rem] xl:w-[calc(100%-24rem)]'
      : ' xl:ml-[14.75rem] xl:w-[calc(100%-14.75rem)]';

    if (openMenu && openChat) {
      base += ' xl:w-[calc(100%-23.75rem)]';
    } else if (openMenu) {
      base += ' !mx-0 xl:w-full xl:pl-[5rem]';
    }

    return base;
  }, [isAuthenticated, openChat, openMenu]);

  return (
    <header className={headerClasses}>
      <div className="flex w-full max-w-containerWidth items-center justify-between">
        {/* Left side */}
        <div className="flex items-center gap-2">
          {(pathname !== '/' || openLobbyMenu || openMenu) && (
            <Image
              src={back}
              height={16}
              width={16}
              className="md:hidden"
              alt="back"
              onClick={() => {
                if (openLobbyMenu || openMenu) {
                  setOpenLobby(false);
                  setOpenMenu(false);
                  setActiveMenu(activePreviousState);
                } else {
                  router.back();
                }
              }}
            />
          )}
          <HeaderLogos />
        </div>

        {/* Center Menu */}
        <HeaderMenuItems />

        {/* Right side */}
        <div className="flex items-center gap-3">
          <HeaderLoginMenuItem />
          <HeaderAuthButtons />
        </div>
      </div>
    </header>
  );
}

Header.displayName = 'Header';

export default Header;
