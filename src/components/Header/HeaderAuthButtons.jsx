'use client';
import React, { useCallback } from 'react';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import PrimaryButton from '../Common/Button/PrimaryButton';
import Auth from '../Auth';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import useAuthModalStore from '@/store/useAuthModalStore';
const MOBILE_BUTTON_CLASSES = 'max-md:min-h-8 !w-fit';

const HeaderAuthButtons = () => {
  const { setSelectedTab } = useAuthTab();
  
  const { isAuthenticated } = useAuthStore();
  const { openModal } = useAuthModalStore();

  const handleAuth = useCallback(
    (tab) => {
      setSelectedTab(tab);
      localStorage.setItem('activeTab', tab);
      openModal(<Auth />);
    },
    [setSelectedTab, openModal],
  );
  if (!isAuthenticated)
    return (
      <div className="flex items-center justify-end">
        <div className="flex w-full items-center justify-end gap-2">
          <PrimaryButtonOutline
            className={MOBILE_BUTTON_CLASSES}
            onClick={() => handleAuth(1)}
          >
            Login
          </PrimaryButtonOutline>
          <PrimaryButton
            className={MOBILE_BUTTON_CLASSES}
            onClick={() => handleAuth(0)}
            variant="secondary"
          >
            Register
          </PrimaryButton>
        </div>
      </div>
    );
};

export default HeaderAuthButtons;
