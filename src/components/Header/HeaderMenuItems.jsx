'use client';
import BetIcon from '@/assets/icons/BetIcon';
import CasinoIcon from '@/assets/icons/CasinoIcon';
import ChatIcon from '@/assets/icons/Chat';
import FansbetIcon from '@/assets/icons/Fansbet';
import SportsIcon from '@/assets/icons/sports';
import useGeneralStore from '@/store/useGeneralStore';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';

const HeaderMenuItems = () => {
  const pathname = usePathname();
  const { setOpenChat, openChat, setOpenLobby, openLobbyMenu } =
    useGeneralStore();
  const router = useRouter();
  const handleRoute = (e, route) => {
    e.preventDefault();
    router.push(route);
  };
  if (window.innerWidth > 767)
    return (
      <div
        className="flex items-center justify-center gap-5 max-lg:gap-3 
          
        "
      >
        <Link
          href="/casino"
          onClick={(e) => {
            handleRoute(e, '/casino');
          }}
          className="px-2 py-1 text-center text-xs font-semibold capitalize text-steelTeal-200 md:px-1"
        >
          <CasinoIcon
            className="mx-auto mb-1 size-6"
            activeMenu={pathname == '/casino'}
          />
          <span
            className={
              pathname == '/casino'
                ? 'bg-TintGoldGradient bg-clip-text text-transparent'
                : ''
            }
          >
            casino
          </span>
        </Link>
        <Link
          href="/bets"
          onClick={(e) => {
            handleRoute(e, '/bets');
          }}
          className="px-2 py-1 text-center text-xs font-semibold capitalize text-steelTeal-200 md:px-1"
        >
          <BetIcon
            className="mx-auto mb-1 size-6"
            activeMenu={pathname == '/bets'}
          />
          <span
            className={
              pathname == '/bets'
                ? 'bg-TintGoldGradient bg-clip-text text-transparent'
                : ''
            }
          >
            Bets
          </span>
        </Link>
        <Link
          href="/sports"
          onClick={(e) => {
            handleRoute(e, '/sports');
          }}
          className="px-2 py-1 text-center text-xs font-semibold capitalize text-steelTeal-200 md:px-0"
        >
          <SportsIcon
            className="mx-auto mb-1 size-6"
            activeMenu={pathname == '/sports'}
            section={'topmenu'}
          />
          <span
            className={
              pathname == '/sports'
                ? 'bg-TintGoldGradient bg-clip-text text-transparent'
                : ''
            }
          >
            Sports
          </span>
        </Link>
        <Link
          href="/lobby"
          onClick={(e) => {
            e.preventDefault();
            setOpenLobby(!openLobbyMenu);
            sessionStorage.setItem('lobby', !openLobbyMenu);
          }}
          className="px-2 py-1 text-center text-xs font-semibold capitalize text-steelTeal-200 md:px-0"
        >
          <FansbetIcon
            className="mx-auto mb-1 size-6"
            activeMenu={openLobbyMenu}
          />
          <span
            className={
              openLobbyMenu
                ? 'bg-TintGoldGradient bg-clip-text text-transparent'
                : ''
            }
          >
            Lobby
          </span>
        </Link>
        <button
          className="px-2 py-1 text-center text-xs font-semibold capitalize text-steelTeal-200 md:px-0"
          onClick={() => {
            setOpenChat(!openChat);
          }}
        >
          <ChatIcon className="mx-auto mb-1 size-6" activeMenu={openChat} />
          <span
            className={
              openChat
                ? 'bg-TintGoldGradient bg-clip-text text-transparent'
                : ''
            }
          >
            Chats
          </span>
        </button>
      </div>
    );
};

export default HeaderMenuItems;
