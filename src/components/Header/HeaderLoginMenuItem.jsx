'use client';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import giftBox from '../../../src/assets/images/gift-box.webp';
import depositIcon from '../../../src/assets/images/svg-images/Deposit.svg';
import Enter from '../../../src/assets/images/svg-images/enter.svg';
import notificationIcon from '../../../src/assets/images/svg-images/Notifications.svg';
import searchIcon from '../../../src/assets/images/svg-images/Search.svg';
import userIcon from '../../../src/assets/images/svg-images/User.svg';
import useAuthStore from '@/store/useAuthStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import useConnectedPlay from '@/hooks/useConnecedPlay';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useCallModalStore from '@/store/useCallModalStore';
import CallPopup from '../Models/CallPopup';
import GroupCallPopup from '../Models/GroupCallPopup';
const HeaderLoginMenuItem = () => {
  const { isAuthenticated } = useAuthStore();
  const voiceCall = useVoiceCallStore((state) => state.voiceCall);
  const connectedPlay = useActiveGroupStore((state) => state.connectedPlay);
  const router = useRouter();
  const openModal = useCallModalStore((state) => state.openModal);
  const closeModal = useCallModalStore((state) => state.closeModal);
  const setIsMinimized = useCallModalStore((state) => state.setIsMinimized);

  const handleNavigation = () => {
    router.push('/account');
  };

  if (isAuthenticated)
    return (
      <div className="flex items-center gap-[2px] md:gap-[10px] max-sm:gap-2">
        {Object?.keys(connectedPlay || {}).length > 0 && (
          <div className="flex cursor-pointer items-center justify-center rounded-[10px] bg-primaryBorder p-[6px] md:p-2 max-sm:rounded-[.4375rem]">
            <Image
              src={Enter}
              width={1000}
              height={1000}
              className="w-[18px] min-w-[18px] md:w-6 md:min-w-6"
              alt="Connect"
              priority
              onClick={() => {
                setIsMinimized(false);
              }}
            />
          </div>
        )}
        <div
          className="relative me-[0.625rem] flex h-full cursor-pointer flex-col items-start justify-start rounded-[0.625rem] bg-primaryBorder p-[6px] !pr-[1.3rem]   md:p-2 max-sm:rounded-[.375rem]"
          onClick={() => {
            router.push('/vip');
          }}
        >
          <p className="text-[0.625rem] font-semibold text-steelTeal-600 md:text-xs">
            Bonus
          </p>
          <div class="w-ful h-[4px] min-w-[90px] rounded-full bg-barBg p-[1px] md:h-2.5 2xl:min-w-[150px]">
            <div
              class="h-full rounded-full bg-progressBarGradient"
              style={{ width: '40%' }}
            ></div>
            <Image
              src={giftBox}
              width={1000}
              height={1000}
              className="absolute right-0 top-0 w-[2.2rem] md:-right-[.8125rem] md:top-0 md:w-9 max-sm:-right-[0.6rem] max-sm:top-[-2px]"
              alt="Brand Logo"
              priority
            />
          </div>
        </div>
        {/* <div className="flex cursor-pointer items-center justify-center rounded-[10px] bg-primaryBorder p-[6px] md:p-2">
          <Image
            src={searchIcon}
            width={1000}
            height={1000}
            className="w-[18px] min-w-[18px] md:w-6 md:min-w-6"
            alt="Brand Logo"
            priority
          />
        </div> */}
        <div className="flex cursor-pointer items-center justify-center rounded-[10px] bg-primaryBorder p-[6px] md:p-2 max-sm:rounded-[.4375rem]">
          <Image
            src={depositIcon}
            width={1000}
            height={1000}
            className="w-[18px] min-w-[18px] md:w-6 md:min-w-6"
            alt="Brand Logo"
            priority
          />
        </div>
        <div className="flex cursor-pointer items-center justify-center rounded-[10px] bg-primaryBorder p-[6px] md:p-2 max-sm:rounded-[.4375rem]">
          <Image
            src={userIcon}
            width={1000}
            height={1000}
            className="w-[18px] min-w-[18px] md:w-6 md:min-w-6"
            alt="Brand Logo"
            priority
            onClick={handleNavigation}
          />
        </div>
        <div className="flex cursor-pointer items-center justify-center rounded-[10px] bg-primaryBorder p-[6px] md:p-2 max-sm:rounded-[.4375rem]">
          <Image
            src={notificationIcon}
            width={1000}
            height={1000}
            className="w-[18px] min-w-[18px] md:w-6 md:min-w-6"
            alt="Brand Logo"
            priority
          />
        </div>
      </div>
    );
};

export default HeaderLoginMenuItem;
