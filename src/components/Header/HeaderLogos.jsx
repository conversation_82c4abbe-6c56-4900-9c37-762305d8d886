'use client';
import useIsMobile from '@/hooks/useIsMobile';
import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import mobileLogo from '../../../src/assets/images/logo/logo-icon.svg';
import brandLogo from '../../assets/images/logo/brand-logo.svg';
import useActiveGroupStore from '@/store/useActiveGroupStore';

const HeaderLogos = () => {
  const { isAuthenticated } = useAuthStore();
  const isMobile = useIsMobile();
  const { setOpenLobby, setOpenMenu, setActiveMenu } = useGeneralStore();
  const connectedPlay = useActiveGroupStore((state) => state.connectedPlay);
  useEffect(() => {
    if (isMobile) {
      setOpenMenu(false);
    }
  }, []);

  const [windowWidth, setWindowWidth] = useState(0);
  const isConnected =
    isAuthenticated && Object.keys(connectedPlay || {}).length > 0;

  useEffect(() => {
    setWindowWidth(window.innerWidth);

    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const showDesktop =
    !isAuthenticated || // always show if not authenticated
    windowWidth > 420 ||
    (windowWidth >= 380 &&
      windowWidth <= 420 &&
      (!isAuthenticated || !isConnected));

  // Mobile shows **only if desktop is NOT showing**
  const showMobile =
    !showDesktop &&
    (windowWidth < 380 ||
      (windowWidth >= 380 && windowWidth <= 420 && isConnected));

  // Dynamic width for desktop
  let desktopWidth = '7.5rem'; // default
  if (windowWidth >= 640 && windowWidth < 768) desktopWidth = '9.75rem'; // sm
  if (windowWidth >= 768) desktopWidth = '8rem'; // md+

  // Dynamic width for mobile
  let mobileWidth = '2rem'; // max width for mobile
  return (
    <Link
      href="/"
      className="inline-block"
      onClick={() => {
        setOpenLobby(false);
        if (isMobile) {
          setOpenMenu(false);
        }
        setActiveMenu(false);
        sessionStorage.setItem('lobby', 'false');
      }}
    >
      {showDesktop && (
        <Image
          src={brandLogo}
          width={1000} // intrinsic size, can be real image width
          height={200} // intrinsic size
          style={{ width: desktopWidth, height: 'auto' }}
          alt="Brand Logo"
          priority
        />
      )}

      {showMobile && (
        <Image
          src={mobileLogo}
          width={1000}
          height={200}
          style={{ width: mobileWidth, height: 'auto' }}
          alt="Mobile Logo"
          priority
        />
      )}
    </Link>
  );
};

export default HeaderLogos;
