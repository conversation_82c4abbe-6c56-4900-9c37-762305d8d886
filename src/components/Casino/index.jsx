'use client';
import React, { useEffect, useState } from 'react';
import LobbyGames from '@/components/HomePage/LobbyGames';
import GameTabs from '../HomePage/GameTabs';
import LiveTable from '../HomePage/LiveTable';
import useAuthStore from '@/store/useAuthStore';
import ConnectedPlay from '../HomePage/ConnectedPlay';
import ProviderSlider from '../HomePage/ProviderSlider';
import Gamefilter from '../HomePage/GameFilter';
import Category from '../Category';
import useRecommendations from '@/hooks/useRecommendations';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import GameSlider from '../HomePage/GameSlider/Gameslider';
import Groups from '../HomePage/Groups';
import Favorites from '../Favorites';
import ContinuePlaying from '../ContinuePlaying';
import useActiveGroupStore from '@/store/useActiveGroupStore';

function Casino() {
  const { isAuthenticated } = useAuthStore();
  const connectedPlay = useActiveGroupStore((state) => state.connectedPlay);

  const [selectedTab, setSelectedTab] = useState(
    isAuthenticated ? 'For You' : 'Lobby',
  );
  const [icon, setIcon] = useState(<ForYouIcon size={16} activeMenu />);
  const { data, gamesLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useRecommendations({ limit: 50, enabled: selectedTab === 'For You' });

  useEffect(() => {
    if (isAuthenticated) {
      if (Object.keys(connectedPlay).length !== 0) {
        setSelectedTab('For You');
        setIcon(<ForYouIcon size={16} activeMenu />);
        if(connectedPlay?.scroll) window.scrollTo(0,0)
      } else {
        setSelectedTab('Lobby');
      }
    }
  }, [isAuthenticated, connectedPlay]);

  return (
    <>
      {isAuthenticated && <ConnectedPlay />}
      <Gamefilter
        onTabChange={setSelectedTab}
        setIcon={setIcon}
        defaultTab={selectedTab}
      />
      {selectedTab === 'Lobby' && (
        <>
          <GameSlider />
          <Groups />
        </>
      )}
      {selectedTab === 'Lobby' ? (
        <>
          {isAuthenticated && (
            <>
              <Category
                selectedTab="For You"
                externalGames={data}
                externalLoading={gamesLoading}
                externalFetchNextPage={fetchNextPage}
                externalHasNextPage={hasNextPage}
                externalIsFetchingNextPage={isFetchingNextPage}
                externalIcon={icon}
                isCarousel
              />
            </>
          )}
          <LobbyGames />
        </>
      ) : selectedTab === 'For You' ? (
        <Category
          selectedTab="For You"
          externalGames={data}
          externalLoading={gamesLoading}
          externalFetchNextPage={fetchNextPage}
          externalHasNextPage={hasNextPage}
          externalIsFetchingNextPage={isFetchingNextPage}
          externalIcon={icon}
        />
      ) : selectedTab === 'Favorites' ? (
        <>
          <Favorites />
        </>
      ) : selectedTab === 'Continue Playing' ? (
        <>
          <ContinuePlaying />
        </>
      ) : (
        <Category selectedTab={selectedTab} externalIcon={icon} />
      )}
      {!isAuthenticated && selectedTab === 'Lobby' && <ProviderSlider />}

      {selectedTab === 'Lobby' && (
        <>
          <GameTabs />
          <LiveTable />
        </>
      )}
    </>
  );
}

export default Casino;
