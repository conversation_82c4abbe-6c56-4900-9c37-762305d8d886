'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { useQueryClient } from '@tanstack/react-query';

import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import { useUpdateFavoriteMutation } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import toast from 'react-hot-toast';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import defaultImage from '../../assets/images/demo-image/card-image.png';
import useAuthModalStore from '@/store/useAuthModalStore';

const PlayCard = ({
  key,
  gameId,
  gameName,
  gameImage,
  sizeVariant = 'default',
  onClick = () => {},
  isFavorite = false,
  providerName = '',
  showBelowSection = true,
  shouldUpdate = false,
}) => {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal} = useAuthModalStore();
  const queryClient = useQueryClient();

  const [imgSrc, setImgSrc] = useState(defaultImage);
  const [fav, setFav] = useState(isFavorite);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (shouldUpdate) {
      setFav(isFavorite);
    }
  }, [isFavorite, shouldUpdate]);

  useEffect(() => {
    if (!gameImage) return;

    const img = new window.Image();
    img.src = gameImage;
    img.onload = () => setImgSrc(gameImage); // only swap once fully loaded
    img.onerror = () => setImgSrc(defaultImage);
  }, [gameImage]);

  const { mutate: updateFavorite } = useUpdateFavoriteMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['GET_FAVORITES_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_CUSTOM_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_SUB_CATEGORY_GAMES'] });
      queryClient.invalidateQueries({
        queryKey: ['GET_SUB_CATEGORY_LOBBY_GAMES'],
      });
      queryClient.invalidateQueries({ queryKey: ['GET_CATEGORY_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_RECOMMENDATION_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_ACTIVE_PLAYER_PUBLIC'] });
      queryClient.invalidateQueries({
        queryKey: ['GET_ACTIVE_PLAYER_MY_FRIENDS'],
      });
      queryClient.invalidateQueries({ queryKey: ['PROVIDER_RECOMMENDATIONS'] });
      setLoading(false);
    },
    onError: (error) => {
      setFav(!fav);
      setLoading(false);
      console.error('Error updating favorite', error);
    },
  });

  const toggleFav = (e) => {
    e.stopPropagation();
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 1);
      setSelectedTab(1);
      openModal(<Auth />);
      return;
    }
    setFav(!fav);
    setLoading(true);
    updateFavorite({ request: !fav, casinoGameId: gameId });
  };

  const cardSizeClasses =
    sizeVariant === 'fixed'
      ? 'max-sm:w-full '
      : 'min-w-[168px] max-w-[168px] md:max-w-[220px]  md:min-w-[200px]';

  return (
    <div
      className={`text-white cursor-pointer overflow-hidden rounded-xl border border-gameCardBorder bg-slateGray-600 ${cardSizeClasses}`}
      key={key}
      onClick={onClick}
    >
      <div className="relative w-full pb-[100%]">
        <Image
          src={imgSrc}
          alt={gameName}
          width={220}
          height={300}
          className="absolute left-0 top-0 h-full w-full"
          priority={true}
        />
      </div>

      <div className="flex justify-between px-[5px] pb-[6px] pt-2">
        <h3 className="line-clamp-2 min-h-[30px] w-full max-w-[9.75rem] text-xs font-bold leading-tight md:min-h-[31px]">
          {gameName}
        </h3>
        {showBelowSection && (
          <button
            disabled={loading}
            type="button"
            onClick={toggleFav}
            className="group flex h-6 w-6 items-center justify-center rounded-lg transition-all duration-300 active:scale-90"
          >
            {fav ? (
              <HeartFillIcon className="h-[.9094rem] w-[.9094rem] scale-110 transform fill-primary-1000 transition-transform duration-200" />
            ) : (
              <>
                <HeartStrokeIcon className="h-4 w-4 fill-steelTeal-1000 transition-opacity duration-200 lg:group-hover:hidden" />
                <HeartFillIcon className="hidden h-[.9094rem] w-[.9094rem] scale-110 transform fill-primary-1000 transition-transform duration-200 lg:group-hover:block" />
              </>
            )}
          </button>
        )}
      </div>

      <div className="grid grid-cols-2 rounded-[.6875rem] bg-slateGray-700 px-[0.313rem] py-[3px] xl:px-[10px]">
        {/* Left Section */}
        <div className="relative pe-2">
          <div className="flex w-full justify-between">
            <p className="text-[8px] font-semibold text-slateGray-900 lg:text-[10px]">
              {showBelowSection ? 'Slots' : '\u00A0'}
            </p>
            <p className="text-[8px] font-semibold text-white-1000 xl:text-[10px]">
              {showBelowSection ? '0.20 - 500' : '\u00A0'}
            </p>
          </div>
          <div className="flex w-full justify-between">
            <p className="text-[8px] font-semibold text-slateGray-900 lg:text-[10px]">
              {showBelowSection ? 'Max Win' : '\u00A0'}
            </p>
            <p className="text-[8px] font-semibold text-white-1000 xl:text-[10px]">
              {showBelowSection ? '25000' : '\u00A0'}
            </p>
          </div>
          <span className="absolute right-0 top-[2px] h-[21px] w-[1px] bg-cardBorderGradient md:h-[25px]"></span>
        </div>

        {/* Right Section */}
        <div className="flex flex-col items-center ps-2">
          <p className="max-w-20 truncate text-center text-[.5rem] font-semibold text-slateGray-900 lg:text-[0.625rem]">
            {showBelowSection
              ? providerName && typeof providerName === 'string'
                ? providerName
                : providerName?.EN
              : '\u00A0'}
          </p>
          <p className="w-full truncate text-center text-[.5rem] font-semibold text-white-1000 xl:text-[0.625rem]">
            {showBelowSection ? 'Join Play' : '\u00A0'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default PlayCard;
