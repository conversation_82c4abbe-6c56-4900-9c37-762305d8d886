'use client';

import ForYouIcon from '@/assets/icons/ForYoyIcon';
import Category from '@/components/Category';
import Gamefilter from '@/components/HomePage/GameFilter';
import { useEffect, useState } from 'react';

import useRecommendations from '@/hooks/useRecommendations';
import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import LobbyGames from '../HomePage/LobbyGames';
import ContinuePlaying from '../ContinuePlaying';
import Favorites from '../Favorites';

function NotFound({ error = false }) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const [selectedTab, setSelectedTab] = useState(
    isAuthenticated ? 'For You' : 'Lobby',
  );
  const [icon, setIcon] = useState(<ForYouIcon size={16} activeMenu />);
  const { data, gamesLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useRecommendations({
      limit: 50,
      enabled: selectedTab === 'For You' || selectedTab === 'Lobby',
    });
  const setOpenMenu = useGeneralStore((state) => state.setOpenMenu);
  const setOpenChat = useGeneralStore((state) => state.setOpenChat);
  useEffect(() => {
    setOpenMenu(true);
    setOpenChat(false);
  }, []);

  return (
    <>
      <div className="relative pb-[3.4375rem] pt-7 text-center before:absolute  before:-bottom-0 before:left-1/2 before:h-[1px] before:w-full before:-translate-x-1/2 before:bg-tabBottomBorder before:blur-[0.1rem]   before:content-[''] lg:before:w-[32.9375rem] max-sm:mx-auto max-sm:max-w-[18.625rem]">
        <h3 className="mb-2 text-2xl font-semibold capitalize text-white-1000 lg:text-[2.5rem]">
          {error
            ? 'Something went wrong'
            : '    The page you’re looking for can’t be found.'}
        </h3>
        <p className="text-base text-steelTeal-200 lg:text-2xl">
          But you can still have fun — check out these games we have!
        </p>
      </div>
      <div className="py-10">
        <Gamefilter
          onTabChange={setSelectedTab}
          setIcon={setIcon}
          defaultTab={selectedTab}
        />
      </div>
      <div>
        {selectedTab === 'Lobby' ? (
          <>
            {isAuthenticated && (
              <>
                <Category
                  selectedTab="For You"
                  externalGames={data}
                  externalLoading={gamesLoading}
                  externalFetchNextPage={fetchNextPage}
                  externalHasNextPage={hasNextPage}
                  externalIsFetchingNextPage={isFetchingNextPage}
                  externalIcon={<ForYouIcon size={16} activeMenu />}
                  isCarousel
                />
              </>
            )}
            <LobbyGames />
          </>
        ) : selectedTab === 'For You' ? (
          <Category
            selectedTab="For You"
            externalGames={data}
            externalLoading={gamesLoading}
            externalFetchNextPage={fetchNextPage}
            externalHasNextPage={hasNextPage}
            externalIsFetchingNextPage={isFetchingNextPage}
            externalIcon={<ForYouIcon size={16} activeMenu />}
          />
        ) : selectedTab === 'Favorites' ? (
          <>
            <Favorites />
          </>
        ) : selectedTab === 'Continue Playing' ? (
          <>
            <ContinuePlaying />
          </>
        ) : (
          <Category selectedTab={selectedTab} externalIcon={icon} />
        )}
      </div>
    </>
  );
}

export default NotFound;
