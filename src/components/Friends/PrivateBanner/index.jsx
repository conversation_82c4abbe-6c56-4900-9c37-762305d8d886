import React from 'react';
import Image from 'next/image';

import Banner<PERSON>oader from '@/components/BannerLoader';
import groupImage from '../../../../public/assets/demo-image/groupImage.webp';

export default function PrivateBanner({ groupData, isLoading }) {
  const getBannerImage = () =>
    groupData?.groupBanner || groupImage;

  return (
    <div className="relative mb-4 flex min-h-[200px] items-center justify-center rounded-[1.25rem]">
      <div className="h-full w-full rounded-xl bg-richBlack-610 text-2xl font-bold text-steelTeal-200 max-sm:text-lg">
        {isLoading ? (
          <BannerLoader />
        ) : (
          <>
            <Image
              src={getBannerImage()}
              alt={`${groupData?.username} banner`}
              height={400}
              width={1200}
              quality={90}
              priority
              className="aspect-[6/1] h-full w-full max-w-[1080px] object-cover object-center"
            />
            {/* <h1 className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-lg bg-black-500 px-3 py-1 text-3xl font-bold">
              {groupData?.username}
            </h1> */}
          </>
        )}
      </div>
    </div>
  );
}
