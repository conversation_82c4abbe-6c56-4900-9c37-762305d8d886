import ChatAvatar from '@/components/ChatAvatar';
import PlayCard from '@/components/PlayCard';
import { slugify } from '@/utils/helper';
import { useRouter } from 'next/navigation';
import React from 'react';

export default function About({ userDetails }) {
  const router = useRouter();
  const handleGameClick = (provider, name) => {
    router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`);
  };

  return (
    <div className="my-4 rounded-[.25rem] bg-erieBlack-200 px-2 py-4  md:p-4">
      <div className="mb-[0.625rem] flex items-center gap-3 border-b border-white-370 pb-[0.625rem]">
        <ChatAvatar
          profileImage={userDetails?.imageUrl}
          firstName={userDetails?.firstName}
          lastName={userDetails?.lastName}
          userName={userDetails?.username}
          imageClassName="h-10 w-10 rounded-full flex-shrink-0"
          imageWidth={40}
          imageHeight={40}
          avatarSize={40}
        />
        <h4 className="flex-1 text-xl font-medium">{userDetails?.username}</h4>
      </div>

      <div className="py-1.5">
        <h4 className="inline-block pr-2 text-base font-bold text-white-400">
          {'VIP Level: '}
        </h4>
        {/* <p className="inline-block text-sm">Very High</p> */}
      </div>
          <div className="flex flex-row gap-[10px]">
                {userDetails?.currentGamePlay && (
                  <Gamescard
                    title={'Now Playing'}
                    gameName={
                      userDetails?.currentGamePlay?.name?.EN ||
                      userDetails?.currentGamePlay?.name
                    }
                    gameId={userDetails?.currentGamePlay?.gameId}
                    gameImage={userDetails?.currentGamePlay?.imageUrl}
                    isFavorite={userDetails?.currentGamePlay?.isFavorite}
                    onClick={() => {
                      handleGameClick(
                        userDetails?.currentGamePlay?.providerName,
                        userDetails?.currentGamePlay?.name?.EN ||
                          userDetails?.currentGamePlay?.name,
                      );
                    }}
                    providerName={userDetails?.currentGamePlay?.providerName}
                  />
                )}
                {!userDetails?.currentGamePlay &&
                  userDetails?.recentGamePlay && (
                    <Gamescard
                      title={'Recently Played'}
                      gameName={
                        userDetails?.recentGamePlay?.casinoGame?.name?.EN
                      }
                      gameId={userDetails?.recentGamePlay?.gameId}
                      gameImage={
                        userDetails?.recentGamePlay?.casinoGame?.iconUrl
                      }
                      isFavorite={
                        userDetails?.recentGamePlaycasinoGame?.isFavorite
                      }
                      onClick={() =>
                        handleGameClick(
                          userDetails?.recentGamePlay?.casinoGame
                            ?.casinoProvider?.name,
                          userDetails?.recentGamePlay?.casinoGame?.name,
                        )
                      }
                      providerName={
                        userDetails?.recentGamePlay?.casinoGame?.casinoProvider
                          ?.name
                      }
                    />
                  )}

                {userDetails?.biggestWin?.length > 0 && (
                  <Gamescard
                    title={'Biggest Win'}
                    gameName={
                      userDetails?.biggestWin?.[0]?.name?.EN ||
                      userDetails?.biggestWin?.[0]?.name
                    }
                    gameId={userDetails?.biggestWin?.[0]?.gameId}
                    gameImage={userDetails?.biggestWin?.[0]?.imageUrl}
                    isFavorite={userDetails?.biggestWin?.[0]?.isFavorite}
                    onClick={() =>
                      handleGameClick(
                        userDetails?.biggestWin?.[0]?.providerName,
                        userDetails?.biggestWin?.[0]?.name?.EN ||
                          userDetails?.biggestWin?.[0]?.name,
                      )
                    }
                    providerName={userDetails?.biggestWin?.[0]?.providerName}
                  />
                )}
              </div>
    </div>
  );
}

const Gamescard = ({
  title,
  gameId,
  gameImage,
  gameName,
  isFavorite,
  onClick,
  providerName,
}) => {
  return (
    <>
      <div className="flex flex-col gap-[10px]">
        <p className="text-[15px] font-semibold">{title}</p>
        <PlayCard
          key={title}
          gameId={gameId}
          gameImage={gameImage}
          gameName={gameName}
          isFavorite={isFavorite}
          onClick={() => onClick(providerName, gameName)}
          providerName={providerName}
          sizeVariant={window?.innerWidth < 768 ? 'fixed' : 'default'}
        />
      </div>
    </>
  );
};
