'use client';

import React from 'react';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import Cookies from 'js-cookie'; // Import the js-cookie library
import useHelperHook from '@/hooks/useHelperHook';
import { removeAccessToken } from '@/utils/helper';
import LogoutIcon from '@/assets/icons/LogoutIcon';
import useGeneralStore from '@/store/useGeneralStore';

function LogoutButton() {
  const { logout, isLoading } = useHelperHook();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { setOpenMenu } = useGeneralStore();

  const handleLogout = async() => {
    if(!isLoading){
      logout();
      queryClient.removeQueries();
      // toast.success('Logged out successfully!');
      window.dispatchEvent(new Event('logout'));
      router.push('/');
      setOpenMenu(false)
    }
  };

  return (
    <button
      type="button"
      className="flex w-full items-center justify-start gap-2.5 rounded-md p-2.5 hover:bg-nav-gradient"
      onClick={handleLogout}
    >
      <LogoutIcon className="h-5 w-5 fill-steelTeal-1000" />
      <span className="text-base font-normal text-white-1000">Log Out</span>
    </button>
  );
}

export default LogoutButton;