'use client';

import React, { useEffect, useState, useCallback } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import PromotionImg1 from '../../assets/images/hero-img.jpg';
import RouletterImg from '../../assets/webp/roulette.webp';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import PrimaryButton from '../Common/Button/PrimaryButton';
import GoogleIcon from '@/assets/icons/GoogleIcon';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import TwitchIcon from '@/assets/icons/TwitchIcon';
import useSocialLogin from '@/hooks/useSocialLogin';
import useModalStore from '@/store/useModalStore';
import useAuthTab from '@/store/useAuthTab';
import Auth from '../Auth';
import useAuthStore from '@/store/useAuthStore';
import LineIcon from '@/assets/icons/LineIcon';
import useAuthModalStore from '@/store/useAuthModalStore';

function PromotionSlider() {
  const { openModal} = useAuthModalStore();
  const { setSelectedTab } = useAuthTab();
  const { isAuthenticated } = useAuthStore();

  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
    align: 'start',
  });
  const [selectedIndex, setSelectedIndex] = useState(0);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  // Autoplay logic
  useEffect(() => {
    if (!emblaApi) return;

    const autoplay = () => {
      if (emblaApi.canScrollNext()) {
        emblaApi.scrollNext();
      } else {
        emblaApi.scrollTo(0);
      }
    };

    const autoplayInterval = setInterval(autoplay, 400000); // Adjust the interval time as needed

    return () => {
      clearInterval(autoplayInterval);
    };
  }, [emblaApi]);

  const handleAuth = useCallback(
    (tab) => {
      setSelectedTab(tab);
      localStorage.setItem('activeTab', tab);
      openModal(<Auth />);
    },
    [setSelectedTab, openModal],
  );

  const {
    isLoading: socialLoading,
    handleGoogleLogin: socialGoogleLogin,
    handleFacebookLogin: socialFacebookLogin,
    handleDiscordLogin: socialDiscordLogin,
    handleTwitchLogin: socialTwitchLogin,
  } = useSocialLogin();

  if (isAuthenticated) return <></>;

  return (
    <div className="embla mb-6 overflow-hidden  md:mb-9" ref={emblaRef}>
      <div className="embla__container [&>.embla-slide]:bg-charcoalBlack-700 flex [&>.embla-slide]:relative [&>.embla-slide]:min-w-0 [&>.embla-slide]:flex-[0_0_100%] [&>.embla-slide]:overflow-hidden [&>.embla-slide]:rounded-2xl">
        <div className="embla__slide embla-slide text-white">
          <div className="max-sm:bg-heroMobBannerBg overflow-hidden rounded-2xl border border-solid border-heroBannerBorder bg-heroBannerBg bg-cover bg-left bg-no-repeat p-5 md:px-10 md:py-8">
            {/* <Image src={PromotionImg1} className="rounded-sm" /> */}
            <div className=" flex flex-col justify-center max-md:items-center max-md:text-center">
              <Image
                src={RouletterImg}
                className="hidden max-w-[180px] max-md:block"
              />
              <h2 className="text-white w-full max-w-[19rem] font-inter text-4xl font-bold uppercase leading-[3rem] sm:max-w-[24.5rem] max-xxl:text-[2rem]">
                Connect With Friends And Start Winning!
              </h2>
              <p className="mt-0.5 w-full max-w-[24.5rem] text-xl font-medium uppercase leading-none text-white-1000 max-xxl:text-base">
                Create Groups, Mic Up, And Play
              </p>

              <PrimaryButton
                variant="secondary"
                className="mt-2 max-w-56 !px-10 xxl:mt-5 max-sm:max-w-full"
                onClick={() => handleAuth(0)}
              >
                Register instantly
              </PrimaryButton>

              <span className="flex w-full max-w-52 items-center py-4 xxl:py-6">
                <span className="h-px flex-1 bg-white-400"></span>

                <span className="shrink-0 px-4 text-sm font-semibold uppercase text-white-400">
                  OR
                </span>

                <span className="h-px flex-1 bg-white-400"></span>
              </span>

              <div className="flex w-full max-w-52 items-center justify-between gap-2 max-sm:max-w-full max-xxs:justify-center [&>button:hover]:bg-richBlack-500 [&>button]:flex [&>button]:items-center [&>button]:justify-center [&>button]:rounded-md [&>button]:bg-white-400 [&>button]:p-2 [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out">
                <button
                  type="button"
                  className=" h-9 w-full"
                  onClick={socialGoogleLogin}
                  disabled={socialLoading.google}
                >
                  <GoogleIcon />
                </button>
                <button
                  type="button"
                  className="h-9 w-full"
                  onClick={socialFacebookLogin}
                  disabled={socialLoading.facebook}
                >
                  <FacebookIcon />
                </button>
                <button
                  type="button"
                  className="h-9 w-full"
                  onClick={socialDiscordLogin}
                  disabled={socialLoading.discord}
                >
                  <LineIcon />
                </button>
                <button
                  type="button"
                  className="h-9 w-full"
                  onClick={socialTwitchLogin}
                  disabled={socialLoading.twitch}
                >
                  <TwitchIcon />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PromotionSlider;
