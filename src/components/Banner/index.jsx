'use client';

import React from 'react';

import 'swiper/css';
import 'swiper/css/effect-fade';
import 'swiper/css/navigation';
import 'swiper/css/thumbs';
import { useRouter } from 'next/navigation';

export default function Banner() {
  const router = useRouter();
  // const { featuredGames, isFeaturedGamesLoading } = useFeaturedGames();


  // if (!featuredGames) {
  //   return;
  // }
  return (
    <section className="mb-10">
      {/* <Swiper
        style={{
          '--swiper-navigation-color': '#fff',
          '--swiper-pagination-color': '#fff',
        }}
        loop
        spaceBetween={10}
        effect="fade"
        navigation={false}
        thumbs={{ swiper: thumbsSwiper }}
        autoplay={{
          delay: 2000,
          disableOnInteraction: false,
        }}
        modules={[Autoplay, EffectFade, Navigation, Thumbs]}
        className="mySwiper2"
      >
        {featuredGames?.map((game) => {
          const bannerImage = game.MasterCasinoGamesThumbnails.find(
            (thumbnail) => {
              console.log('hhhh', thumbnail);
              return (
                thumbnail.thumbnailType === 'long' &&
                thumbnail.thumbnail.includes('banner')
              );
            },
          );
          console.log('bannerImage', bannerImage);

          return (
            <SwiperSlide key={game.masterCasinoGameId}>
              <div className="relative bg-richBlack-1000">
                <Image
                  src={bannerImage?.thumbnail}
                  width={10000}
                  height={10000}
                  className="w-full max-w-full"
                  alt={game.name}
                />
                <div className="absolute top-0 flex h-full w-4/5 px-4 pb-4 pt-4 md:pb-[5.625rem] xxl:w-3/5">
                  <div className="m-auto w-full pl-0 md:pl-4">
                    <h6 className="text-normal text-xs leading-[1] text-fluorescentBlue-1000 sm:text-lg">
                      {game.gameProviderType.toUpperCase()}
                    </h6>
                    <h2 className="font-digitalt text-2xl leading-[1] sm:text-5xl">
                      {game.name}
                    </h2>
                    <PrimaryButtonOutline
                      onClick={() => handleGameClick(game?.masterCasinoGameId)}
                      className="mt-2 min-h-6 px-4 py-1 text-xs sm:mt-3 sm:min-h-10 sm:px-6 sm:py-1.5 sm:text-base"
                    >
                      Play Now
                    </PrimaryButtonOutline>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper> */}

      {/* <Swiper
        onSwiper={setThumbsSwiper}
        loop
        spaceBetween={10}
        slidesPerView={7}
        watchSlidesProgress
        modules={[Navigation, Thumbs]}
        breakpoints={{
          475: {
            slidesPerView: 0,
          },
          640: {
            slidesPerView: 0,
          },
          768: {
            slidesPerView: 6,
          },
          1024: {
            slidesPerView: 7,
          },
          1280: {
            slidesPerView: 7,
          },
          1536: {
            slidesPerView: 7,
          },
        }}
        className="mySwiper -mt-16 !hidden !overflow-visible md:!block"
      >
        {featuredGames?.map((game) => {
          const thumbnailImage = game.MasterCasinoGamesThumbnails.find(
            (thumbnail) =>
              thumbnail.thumbnailType === 'long' &&
              thumbnail.thumbnail.includes('thumbnail'),
          );

          console.log('thumbnailImage', thumbnailImage);

          return (
            <SwiperSlide key={game.masterCasinoGameId}>
              <div className="cursor-pointer overflow-hidden rounded-xl bg-richBlack-1000">
                <Image
                  src={thumbnailImage?.thumbnail}
                  width={10000}
                  height={10000}
                  className="w-full max-w-full"
                  alt={game.name}
                />
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper> */}
    </section>
  );
}
