'use client';

import { Suspense, useEffect, useState, memo, use } from 'react';
import MainLoader from '../Common/Loader/MainLoader';
import useGeneralStore from '@/store/useGeneralStore';
import useAuthStore from '@/store/useAuthStore';
import { getAccessToken, removeAccessToken } from '@/utils/helper';
import axios from 'axios';

const FallbackUI = memo(() => (
  <div className="text-white bg-black relative flex min-h-screen items-center justify-center overflow-hidden">
    <div className="z-10 w-32">
      <MainLoader />
    </div>
    {/* <div className="absolute left-[-100px] top-0 h-full w-[200px] rounded-full bg-red-600 opacity-30 blur-[97px]" />
    <div className="absolute right-[-100px] top-0 h-full w-[200px] rounded-full bg-red-600 opacity-30 blur-[97px]" /> */}
  </div>
));

export default function ClientSuspenseWrapper({ children }) {
  const [isClient, setIsClient] = useState(false);
  const { setOpenChat } = useGeneralStore();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const setIsAuthenticated = useAuthStore((state) => state.setIsAuthenticated);

  const logout = useAuthStore((state) => state.logout);
  // const MIN_WIDE_SCREEN_WIDTH = 1280;

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        // Read token from cookies
        const token = getAccessToken();

        if (!token) {
          // Handle missing token (e.g., logout, show modal, etc.)
          console.log('No token found');
          return;
        }

        // Send request with token in Authorization header
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/user/validate-token?token=${token}`,
          {
            headers: {
              Authorization: `${token}`,
            },
          },
        );

        if (response?.data?.data?.success) {
          // Token is valid, perform your task here
          setIsAuthenticated(true);
          // e.g., set some state, redirect, etc.
        } else {
          logout();
          removeAccessToken();
          // Token invalid, perform another task
        }
      } catch (error) {
        logout();
        // Handle error (e.g., token expired, network error)
        console.log('Authentication error:', error);
      }
    };

    setIsClient(true);
    const isMobile = () => {
      return (
        window.innerWidth <= 768 || /Mobi|Android/i.test(navigator.userAgent)
      );
    };
    if (isMobile()) {
      setOpenChat(false);
    }
    return () => checkAuthentication();
    // const handleResize = () => {
    //   const isWideScreen = window.matchMedia(
    //     `(min-width: ${MIN_WIDE_SCREEN_WIDTH}px)`,
    //   ).matches;
    //   setOpenChat(isWideScreen);
    // };

    // handleResize();
    // window.addEventListener('resize', handleResize);

    // return () => {window.removeEventListener('resize', handleResize),checkAuthentication() }
  }, []);
  useEffect(() => {
    if (isAuthenticated) {
      window.scrollTo(0, 0);
    }
  }, [isAuthenticated]);

  // Don't render anything (including Suspense) until on client
  if (!isClient) return <FallbackUI />;

  return (
    <Suspense fallback={<FallbackUI />}>
      <div>{children}</div>
    </Suspense>
  );
}
