'use client';
import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useCategory from '@/hooks/useCategory';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
const Auth = dynamic(() => import('@/components/Auth'), { ssr: false });
import PlayCard from '@/components/PlayCard';
import Image from 'next/image';
import { slugify, unSlugify } from '@/utils/helper';
import useGameStore from '@/store/useGameStore';
import CarouselSection from '@/components/Common/CarouselSection';
import useGeneralStore from '@/store/useGeneralStore';

const Category = ({
  selectedTab,
  params = '',
  externalIcon = '',
  externalGames,
  externalLoading,
  externalFetchNextPage,
  externalHasNextPage,
  externalIsFetchingNextPage,
  isCarousel = false,
}) => {
  const gameName = params?.categoryName
    ? unSlugify(params.categoryName)
    : selectedTab;
  const searchQuery = useGameStore((state) => state.searchQuery);
  const [pageNo, setPageNo] = useState(1);
  const observerRef = useRef(null);

  const {
    casinoGames,
    gamesLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useCategory({
    limit: 14,
    categoryName: gameName,
    pageNo,
    gameName: searchQuery,
    enabled: selectedTab !== 'For You' && selectedTab !== 'Lobby',
  });

  const finalGames =
    externalGames ||
    (casinoGames?.pages
      ? casinoGames.pages.flatMap(
          (group) =>
            group?.data?.casinoGames?.rows.filter(
              (_, index) => group?.data?.casinoGames?.count > 0,
            ) || [],
        )
      : []);

  const loading = externalLoading ?? gamesLoading;
  const loadMore = externalFetchNextPage ?? fetchNextPage;
  const moreToLoad = externalHasNextPage ?? hasNextPage;
  const fetchingNext = externalIsFetchingNextPage ?? isFetchingNextPage;
  const icon =
    externalIcon ||
    casinoGames?.pages[0]?.data?.casinoGames?.rows[0]?.casinoCategory?.iconUrl;
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const router = useRouter();
  const { openMenu, openChat } = useGeneralStore();

  const handleGameClick = (provider, name) => {
    router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`);
  };

  useEffect(() => {
    if (!moreToLoad || fetchingNext) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: '600px',
        threshold: 1.0,
      },
    );

    const currentRef = observerRef.current;
    if (currentRef) observer.observe(currentRef);

    return () => {
      if (currentRef) observer.unobserve(currentRef);
    };
  }, [moreToLoad, fetchingNext, loadMore]);

  return loading && !selectedTab ? (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <MainLoader className="w-32" />
    </div>
  ) : (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 shadow-container">
      {!isCarousel && (
        <div className="mb-4 flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            {icon &&
              (typeof icon === 'string' ? (
                <Image
                  src={icon}
                  alt="Title Icon"
                  className="h-5 w-5 object-cover"
                  width={20}
                  height={20}
                />
              ) : (
                icon
              ))}

            {selectedTab ? (
              <p className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase leading-normal text-transparent">
                {gameName}
              </p>
            ) : (
              <h6 className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent">
                {gameName}
              </h6>
            )}
          </div>
        </div>
      )}

      {finalGames?.length > 0 ? (
        isCarousel ? (
          <CarouselSection
            title={gameName}
            titleIcon={icon}
            navigationStyle="default"
            showViewAll={true}
            showNavigation={true}
            viewAll="View All"
            onViewAllClick={() => router.push('/for-you')}
            cardGap="gap-0"
            containerMargin="mt-3 mb-10"
            titleClassName="text-[14px] font-[600] text-white-1000"
            emblaOptions={{
              loop: false,
              align: 'start',
              slidesToScroll: 'auto',
            }}
          >
            {finalGames.map((game, index) => (
              <div
                key={game?.id || index}
                className={` flex-[0_0_calc(100%/6)] pl-3  max-3xl:flex-[0_0_calc(100%/5)]
                max-xxl:flex-[0_0_calc(100%/4)]
                max-xl:flex-[0_0_calc(100%/5)]         
                max-lg:flex-[0_0_calc(100%/4.3)] 
                max-lmd:flex-[0_0_calc(100%/4)]
                max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]
                ${openMenu && openChat ? 'max-xxl:flex-[0_0_calc(100%/5)] max-2xl:flex-[0_0_calc(100%/4)]' : openMenu ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]' : openChat ? 'max-2xl:flex-[0_0_calc(100%/3.5)]' : ' max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/5)]'}
                `}
              >
                <PlayCard
                  gameId={game?.id || game?.masterCasinoGameId}
                  gameImage={game?.iconUrl ?? game?.imageUrl ?? ''}
                  gameName={game?.name?.EN || game?.name}
                  isFavorite={game?.isFavorite}
                  onClick={() =>
                    handleGameClick(
                      game?.casinoProvider?.name ?? game?.providerName,
                      game?.name?.EN || game?.name,
                    )
                  }
                  providerName={
                    game?.casinoProvider?.name ?? game?.providerName
                  }
                  sizeVariant="fixed"
                />
              </div>
            ))}
          </CarouselSection>
        ) : (
          <div
            className={`grid grid-cols-6 gap-3 max-3xl:grid-cols-5 max-xxl:grid-cols-4 max-xxl:gap-[.5625rem] max-xl:grid-cols-5 max-lg:grid-cols-4 max-3xl:max-lg:grid-cols-5 max-lmd:grid-cols-4 max-md:grid-cols-2 max-sm:grid-cols-2
              ${openMenu && openChat ? 'max-xxl:grid-cols-6 max-2xl:grid-cols-4' : openMenu ? 'max-xxl:grid-cols-6 max-2xl:grid-cols-6' : openChat ? 'max-2xl:grid-cols-4' : 'max-xxl:grid-cols-6 max-2xl:grid-cols-5'}
              `}
          >
            {finalGames.map((game, index) => (
              <PlayCard
                key={game?.id || index}
                gameId={game?.id || game?.masterCasinoGameId}
                gameImage={game?.iconUrl ?? game?.imageUrl ?? ''}
                gameName={game?.name?.EN || game?.name}
                isFavorite={game?.isFavorite}
                onClick={() =>
                  handleGameClick(
                    game?.casinoProvider?.name ?? game?.providerName,
                    game?.name?.EN || game?.name,
                  )
                }
                providerName={game?.casinoProvider?.name ?? game?.providerName}
                sizeVariant="fixed"
              />
            ))}
          </div>
        )
      ) : loading ? (
        <div className="flex h-96 items-center justify-center">
          <MainLoader className="w-32" />
        </div>
      ) : (
        !isCarousel && (
          <div className="text-white flex h-96 items-center justify-center">
            No games are available
          </div>
        )
      )}

      {!isCarousel && moreToLoad && (
        <div
          ref={observerRef}
          className="mt-8 flex h-10 items-center justify-center"
        >
          {/* {fetchingNext && <MainLoader className="w-16" />} */}
        </div>
      )}
    </section>
  );
};

export default Category;
