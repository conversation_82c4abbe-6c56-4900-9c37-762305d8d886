import React, { useEffect, useState } from 'react';
import { useGoogleLogin } from '@react-oauth/google';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useSignUp from '@/hooks/useSignUp';
import InputField from '@/components/Common/InputField';
import { signUpSchema } from '@/schemas/auth';
import ButtonLoader from '@/components/Common/Loader/ButtonLoader';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import TwitchIcon from '@/assets/icons/TwitchIcon';
import GoogleIcon from '@/assets/icons/GoogleIcon';
// import OnlyFans from '@/assets/icons/onlyfansIcon.svg';
import { useFacebookLoginMutation } from '@/reactQuery/authQuery';
import useModalStore from '@/store/useModalStore';
import ReferralCodeBox from '../ReferalInput';
import useSocialLogin from '@/hooks/useSocialLogin';

function SignUp() {
  const [formData, setFormData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
    referralCode: '',
    // over18: false,
  });

  const { openModal } = useModalStore((state) => state);

  const { signUp, isLoading, googleMutation } = useSignUp();

  useEffect(() => {
    // Load the Facebook SDK
    window.fbAsyncInit = function () {
      FB.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v11.0',
      });
      FB.AppEvents.logPageView();
    };

    (function (d, s, id) {
      let js;
      const fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'facebook-jssdk');
  }, []);

  const [formErrors, setFormErrors] = useState({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: '',
    referralCode: '',
    // over18: false,
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    try {
      signUpSchema.parse({ ...formData });
      signUp({
        ...formData,
        password: btoa(formData.password),
        confirmPassword: btoa(formData.confirmPassword),
      });
    } catch (validationError) {
      console.log('Validation error:', validationError);
      if (validationError.formErrors?.fieldErrors) {
        setFormErrors(validationError.formErrors.fieldErrors);
      } else if (validationError.errors) {
        // Handle Zod validation errors
        const errors = {};
        validationError.errors.forEach((error) => {
          errors[error.path[0]] = error.message;
        });
        setFormErrors(errors);
      }
    }
  };

  const facebookMutation = useFacebookLoginMutation({
    onSuccess: (response) => {
      console.log(response, ':::::::::facebook response');
    },
    onError: (error) => {
      console.log(error, '::::::::::::facebook error');
    },
  });
  const handleFaceBookLogin = async (userData) => {
    facebookMutation.mutate(userData);
  };

  const {
    isLoading: socialLoading,
    handleGoogleLogin: socialGoogleLogin,
    handleFacebookLogin: socialFacebookLogin,
    handleDiscordLogin: socialDiscordLogin,
    handleTwitchLogin: socialTwitchLogin,
  } = useSocialLogin();

  const handleChange = (e) => {
    const { name, value, checked } = e.target;

    if (name === 'over18' || name === 'acceptTerms') {
      setFormData({
        ...formData,
        [name]: checked,
      });
      setFormErrors({
        ...formErrors,
        [name]: false,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value.trim(),
      });
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 gap-4 max-xs:grid-cols-1">
        {/* <InputField
          type="text"
          name="firstName"
          value={formData.firstName}
          placeholder="First Name"
          onChange={handleChange}
          error={formErrors.firstName}
          label="First Name"
          color
        />
        <InputField
          type="text"
          name="lastName"
          value={formData.lastName}
          placeholder="Last Name"
          onChange={handleChange}
          error={formErrors.lastName}
          label="Last Name"
          color
        /> */}
        <InputField
          type="text"
          name="username"
          value={formData.username}
          placeholder="Create your username"
          onChange={handleChange}
          error={formErrors.username}
          label="Username"
          color
        />
        <InputField
          type="text"
          name="email"
          value={formData.email}
          placeholder="Enter your email"
          onChange={handleChange}
          error={formErrors.email}
          label="Email"
          color
        />
        <InputField
          type="password"
          name="password"
          value={formData.password}
          placeholder="Enter your password"
          onChange={handleChange}
          error={formErrors.password}
          label="Password"
          color
        />
        {/* <InputField
          type="password"
          name="confirmPassword"
          value={formData.confirmPassword}
          placeholder="Enter Confirm Password"
          onChange={handleChange}
          error={formErrors.confirmPassword}
          label="Confirm Your Password"
          color
        /> */}
      </div>

      <div className=" py-6">
        <div className="flex gap-2">
          <label htmlFor="acceptTerms" className="h-[24px]">
            <input
              type="checkbox"
              id="acceptTerms"
              name="acceptTerms"
              checked={formData.acceptTerms}
              onChange={handleChange}
              className="size-6 appearance-none rounded-md border border-solid border-[#848481]  bg-cetaceanBlue-2000 checked:bg-checkbox-check checked:bg-contain checked:bg-center checked:bg-no-repeat"
            />
          </label>

          <p className="text-sm font-medium leading-5 text-white-1000">
            I agree to the Terms and Conditions and Privacy Policy.
          </p>
        </div>
        {formErrors.acceptTerms && (
          <p className="mt-1 text-sm text-red-500">{formErrors.acceptTerms}</p>
        )}
      </div>

      <ReferralCodeBox
        value={formData.referralCode}
        onChange={handleChange}
        error={formErrors.referralCode}
      />

      {/* <div className="mt-3 flex gap-3">
        <input
          name="over18"
          id="over18"
          type="checkbox"
          checked={formData.over18}
          onChange={handleChange}
          className="border-white h-5 w-5 min-w-5 cursor-pointer appearance-none rounded border border-solid bg-transparent bg-contain bg-center bg-no-repeat checked:bg-primary-1000 checked:bg-checkbox-check"
        />
        <label
          htmlFor="over18"
          className="text-white text-[0.813rem] font-normal md:text-base !leading-tight"
        >
          I am at least 18 years old and not a resident of the restricted
          states.
        </label>
      </div> */}

      {/* <div className="mt-1 flex gap-3">
        <input
          name="acceptTerms"
          id="conditions"
          type="checkbox"
          checked={formData.acceptTerms}
          onChange={handleChange}
          className="border-white h-5 w-5 min-w-5 cursor-pointer appearance-none rounded border border-solid bg-transparent bg-contain bg-center bg-no-repeat checked:bg-primary-1000 checked:bg-checkbox-check"
        />
        <label
          htmlFor="conditions"
          className="text-white cursor-pointer text-[0.813rem] font-normal md:text-base !leading-tight"
        >
          I accept the Terms and Conditions
        </label>
      </div> */}

      {/* {(formErrors.acceptTerms || formErrors.over18) && (
        <p className="mt-1 text-red-500">
          {formErrors.acceptTerms || formErrors.over18}
        </p>
      )} */}
      {/* <div className="mt-6 flex justify-center md:justify-start">
        <PrimaryButton type="submit" disabled={isLoading}>
          {isLoading ? <ButtonLoader /> : 'Sign Up'}
        </PrimaryButton>
      </div> */}

      <div className="mt-6 flex w-full flex-wrap items-center justify-between gap-2 md:justify-between max-xxs:flex-col">
        <div className="flex w-full items-center gap-2 max-xxs:flex-col max-xxs:pr-0">
          <PrimaryButton variant="secondary" type="submit" disabled={isLoading}>
            {isLoading ? <ButtonLoader /> : 'Register'}
          </PrimaryButton>
        </div>
      </div>

      <span className="flex items-center pb-6 pt-6">
        <span className="h-px flex-1 bg-cetaceanBlue-2000"></span>

        <span className="shrink-0 px-4 text-sm font-semibold uppercase text-cetaceanBlue-4000">
          OR
        </span>

        <span className="h-px flex-1 bg-cetaceanBlue-2000"></span>
      </span>

      <div className="flex items-center justify-between gap-2 max-xxs:justify-center [&>button:hover]:bg-richBlack-500 [&>button]:flex [&>button]:items-center [&>button]:justify-center [&>button]:rounded-md [&>button]:bg-richBlack-1000 [&>button]:p-2 [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out">
        <button
          type="button"
          className="w-full"
          onClick={socialGoogleLogin}
          disabled={socialLoading.google}
        >
          <GoogleIcon />
        </button>
        <button
          type="button"
          className="w-full"
          onClick={socialFacebookLogin}
          disabled={socialLoading.facebook}
        >
          <FacebookIcon />
        </button>
        <button
          type="button"
          className="w-full"
          onClick={socialDiscordLogin}
          disabled={socialLoading.discord}
        >
          <DiscordIcon />
        </button>
        <button
          type="button"
          className="w-full"
          onClick={socialTwitchLogin}
          disabled={socialLoading.twitch}
        >
          <TwitchIcon />
        </button>
      </div>
    </form>
  );
}

export default SignUp;
