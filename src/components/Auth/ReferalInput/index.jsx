import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react"; // Optional: for icons

function ReferralCodeBox({ value, onChange, error }) {
  const [showInput, setShowInput] = useState(false);

  return (
    <div className="w-full max-w-md mx-auto">
      <button
        onClick={() => setShowInput(!showInput)}
        className="flex w-full items-center justify-between text-sm font-medium text-white-1000 transition-colors"
        type="button"
      >
        Referral Code (Optional)
        {showInput ? (
          <ChevronUp className="h-4 w-4 text-cetaceanBlue-3000" />
        ) : (
          <ChevronDown className="h-4 w-4 text-cetaceanBlue-3000" />
        )}
      </button>

      {!showInput  && <div className=" mt-3 h-px bg-cetaceanBlue-2000 w-full" />}

      <div
        className={`mt-3 grid transition-all duration-300 overflow-hidden ${showInput ? "grid-rows-[1fr] opacity-100" : "grid-rows-[0fr] opacity-0"
          }`}
      >
        <div className="overflow-hidden">
          <input
            type="text"
            name="referralCode"
            value={value || ''}
            onChange={onChange}
            placeholder="Enter your referral code"
            className="text-white w-full rounded-md border border-solid border-richBlack-1000 bg-richBlack-1000 ps-3 min-h-10 pr-10 text-base font-normal focus:border-steelTeal-1000 focus:outline-none"
          />
          {error && (
            <p className="text-red-500 text-sm mt-1">
              {error}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default ReferralCodeBox;
