'use client';

import React, { useState } from 'react';
import toast from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useSignIn from '@/hooks/useSignIn';
import useAuthStore from '@/store/useAuthStore';
import InputField from '@/components/Common/InputField';
import { signInSchema } from '@/schemas/auth';
import { useTwoFactorVerifyLoginOtp } from '@/reactQuery/authQuery';
import ForgotPassword from '../ForgotPassword';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import TwitchIcon from '@/assets/icons/TwitchIcon';
import GoogleIcon from '@/assets/icons/GoogleIcon';
import useSocialLogin from '@/hooks/useSocialLogin';
import EyeCloseIcon from '@/assets/icons/Eye-Close';
import EyeOpenIcon from '@/assets/icons/Eye-Open';

function SignIn() {
  const [formData, setFormData] = useState({
    userName: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState({
    userName: '',
    password: '',
    otp: '',
  });
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  const { signIn, isLoading, error } = useSignIn();
  const { setIsAuthenticated, userDetails, setUserDetails, setUserWallet } =
    useAuthStore((state) => state);
  const [otp, setOtp] = useState();
  const [showTooltip, setShowTooltip] = useState(false);

  // Social login hook
  const {
    isLoading: socialLoading,
    handleGoogleLogin,
    handleFacebookLogin,
    handleDiscordLogin,
    handleTwitchLogin,
  } = useSocialLogin();

  const handleSubmit = (e) => {
    e.preventDefault();
    try {
      signInSchema.parse({
        userName: formData.userName,
        password: formData.password,
      });
      signIn(formData.userName, formData.password);
    } catch (validationError) {
      setFormErrors(validationError.formErrors.fieldErrors);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    setFormErrors({
      ...formErrors,
      [name]: '',
    });
  };

  const handleForgotPasswordClick = () => {
    setIsForgotPassword(true);
  };

  const handleBackToSignInClick = () => {
    setIsForgotPassword(false);
    setFormData({ userName: '', password: '' });
    setFormErrors({ userName: '', password: '' });
  };

  const mutationVerfiy = useTwoFactorVerifyLoginOtp({
    onSuccess: (response) => {
      setOtp('');
      setIsAuthenticated(true);
      localStorage.setItem('isAuthenticated', true);
      toast.success(response.data.message);
      setUserDetails(response?.data?.user);
      setUserWallet(
        response?.data?.user?.wallets.find((wallet) => wallet.default),
      );
    },
    onError: (error) => {
      const message = error.response?.data?.errors?.[0]?.description;
      if (message) {
        console.log('error**', error);
        toast.error('Please a enter a valid otp');
      }
    },
  });

  const verfiyOtp = () => {
    if (otp) {
      mutationVerfiy.mutate({
        token: otp,
        email: userDetails?.user?.email,
        isMobile: false,
      });
      setFormErrors({ userName: '', password: '' });
    } else {
      setFormErrors({ ...formErrors, otp: 'Please enter a valid otp' });
    }
  };
  const [showPassword, setShowPassword] = useState(false);

  return isForgotPassword ? (
    <ForgotPassword onBack={handleBackToSignInClick} />
  ) : (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 gap-2">
          <InputField
            type="text"
            name="userName"
            value={formData.userName}
            placeholder="Enter username or email"
            onChange={handleChange}
            error={formErrors.userName}
            label="Username Or Email"
            color
          />
          <InputField
            type="password"
            name="password"
            value={formData.password}
            placeholder="Enter Password"
            onChange={handleChange}
            error={formErrors.password}
            label="Password"
            color
            endIcon={
              <div
                onClick={() => setShowPassword(!showPassword)}
                className="cursor-pointer [&_*]:fill-white-1000"
              >
                {showPassword ? (
                  <EyeCloseIcon className="" />
                ) : (
                  <EyeOpenIcon className="" />
                )}
              </div>
            }
          />
        </div>

        <div className="mt-3 flex justify-start">
          <button
            type="button"
            onClick={handleForgotPasswordClick}
            className="text-sm font-medium leading-none text-cetaceanBlue-3000 transition-all duration-300 hover:text-primary-1000"
          >
            Forgot Password?
          </button>
        </div>
        {!userDetails?.authEnable && (
          <div className="mt-6 flex justify-center md:justify-start">
            <PrimaryButton
              variant="secondary"
              type="submit"
              disabled={isLoading}
              keepColorWhenDisabled
            >
              {isLoading ? 'Login In...' : 'Login'}
            </PrimaryButton>
          </div>
        )}

        <span className="flex items-center pb-6 pt-6">
          <span className="h-px flex-1 bg-cetaceanBlue-2000"></span>

          <span className="shrink-0 px-4 text-sm font-semibold uppercase text-cetaceanBlue-4000">
            OR
          </span>

          <span className="h-px flex-1 bg-cetaceanBlue-2000"></span>
        </span>

        <div className="flex items-center justify-between gap-2 max-xxs:justify-center [&>button:hover]:bg-richBlack-500 [&>button]:flex [&>button]:items-center [&>button]:justify-center [&>button]:rounded-md [&>button]:bg-richBlack-1000 [&>button]:p-2 [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out">
          <button
            type="button"
            className="w-full"
            onClick={handleGoogleLogin}
            disabled={socialLoading.google}
          >
            <GoogleIcon />
          </button>
          <button
            type="button"
            className="w-full"
            onClick={handleFacebookLogin}
            disabled={socialLoading.facebook}
          >
            <FacebookIcon />
          </button>
          <button
            type="button"
            className="w-full"
            onClick={handleDiscordLogin}
            disabled={socialLoading.discord}
          >
            <DiscordIcon />
          </button>
          <button
            type="button"
            className="w-full"
            onClick={handleTwitchLogin}
            disabled={socialLoading.twitch}
          >
            <TwitchIcon />
          </button>
        </div>
      </form>
      {userDetails?.authEnable && (
        <div>
          <div className="columns-1">
            <div className="mt-4 w-full ">
              <label className="mb-1 block flex gap-2 text-base font-normal text-steelTeal-1000">
                Two Factor Code{' '}
                <label
                  className="relative inline-block flex h-5 w-5 items-center justify-center rounded-full border bg-gray-700 text-white-1000"
                  onMouseEnter={() => setShowTooltip(true)}
                  onMouseLeave={() => setShowTooltip(false)}
                >
                  i
                  {showTooltip && (
                    <div className="text-white absolute bottom-full left-4  z-10 whitespace-nowrap rounded bg-gray-700 px-3 py-0.5 text-sm opacity-90">
                      Please check google authenticator for otp
                      {/* <div className="tooltip-arrow w-3 h-3 bg-gray-700 absolute transform -translate-x-1/2 translate-y-full"></div> */}
                    </div>
                  )}
                </label>
              </label>
              <div className="text-white relative w-full rounded-md ">
                <input
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  type="text"
                  placeholder="Enter a otp from google authenticatore"
                  className={`text-white w-full rounded-md border ${
                    error
                      ? 'border-red-500'
                      : 'border-solid border-richBlack-1000'
                  } bg-richBlack-1000 p-3 text-base font-normal focus:border-steelTeal-1000`}
                />
                {formErrors?.otp && (
                  <label>
                    {otp?.length === 0
                      ? 'OTP is Required'
                      : otp.length < 6
                        ? formErrors?.otp
                        : ''}
                  </label>
                )}
              </div>
            </div>
          </div>
          <div className="mt-6 flex justify-center md:justify-start">
            <PrimaryButton
              type="submit"
              disabled={isLoading}
              onClick={verfiyOtp}
            >
              {isLoading ? 'Verifing Otp...' : 'Verify Otp'}
            </PrimaryButton>
          </div>
        </div>
      )}
    </div>
  );
}

export default SignIn;
