'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter, usePathname } from 'next/navigation';

import useAuthStore from '@/store/useAuthStore';
import useHelperHook from '@/hooks/useHelperHook';
import useAuthTab from '@/store/useAuthTab';
import useAuthModalStore from '@/store/useAuthModalStore';

import AuthCharacterSignup from '../../assets/images/stock-images/auto-img.png';
import SignUp from './SignUp';
import SignIn from './SignIn';
import Tabs from '../Common/Tabs';
import IconButton from '../Common/Button/IconButton';

function Auth() {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();

  const { isAuthenticated, hasRehydrated, setUserDetails } = useAuthStore(
    (state) => state
  );
  const { clearUserAuth } = useHelperHook();
  const { selectedTab, setSelectedTab } = useAuthTab((state) => state);
  const { isOpen, closeModal } = useAuthModalStore();

  const [isClosing, setIsClosing] = useState(false);

  const tabs = [
    { label: 'Register', content: <SignUp /> },
    { label: 'Login', content: <SignIn /> },
  ];

  useEffect(() => {
    if (!isAuthenticated) setUserDetails([]);
    if (isAuthenticated && pathname === '/sports')
      queryClient.invalidateQueries(['SPORTS']);
  }, [isAuthenticated, pathname, queryClient, setUserDetails]);

  const handleCloseModal = () => {
    setIsClosing(true); // trigger exit animation
    setTimeout(() => {
      closeModal();      // actually remove modal from Zustand store
      setIsClosing(false); // reset local state for next open
    }, 400); // match transition duration
  };


  useEffect(() => {
    window.addEventListener('logout', () => {
      clearUserAuth();
      localStorage.clear();
      closeModal();
      router.push('/');
    });
    setSelectedTab(Number(localStorage.getItem('activeTab')) || 0);
  }, []);

  // Reset local closing state whenever modal re-opens
  useEffect(() => {
    if (isOpen) setIsClosing(false);
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && !isAuthenticated && hasRehydrated && (
        <motion.div
          key="authentication-modal"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-end justify-center bg-[#000000A3] md:items-center"
        >
          <motion.div
            drag="y"
            dragConstraints={{ top: 0 }}
            dragElastic={0.2}
            onDragEnd={(event, info) => {
              if (info.offset.y > 50) {
                setIsClosing(true);       // trigger exit animation
                setTimeout(() => {
                  closeModal();           // actually remove modal from store
                  setIsClosing(false);    // reset local closing state
                }, 400);                  // match transition duration
              }
            }}
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: isClosing ? '100%' : 0, opacity: isClosing ? 0 : 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
             className="relative max-h-full w-full max-w-[53.25rem] p-0 md:min-h-[31.688rem] md:p-4"
          >
            <div className="relative overflow-hidden rounded-tl-3xl rounded-tr-3xl bg-cetaceanBlue2-2000 md:rounded-[1.5rem]">
              <div className="absolute right-3 top-3 hidden md:flex">
                <IconButton
                  onClick={handleCloseModal}
                  className="h-6 w-6 min-w-6"
                >
                  <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                </IconButton>
              </div>
              <div className="flex justify-center">
                <div className="flex w-full max-md:flex-col max-md:items-center">
                  <div className="relative w-full max-w-[30rem] max-md:hidden">
                    <Image
                      src={AuthCharacterSignup}
                      width={1000}
                      height={1000}
                      className="h-full w-full max-w-[30rem] object-cover"
                      alt="Auth Character"
                    />
                    <div className="absolute left-0 top-0 flex h-full w-full items-center justify-center">
                      <p className="max-w-[300px] font-inter text-4xl font-bold uppercase text-black-1000">
                        Connect With Friends And Start Winning!
                      </p>
                    </div>
                  </div>

                  <div className="relative w-full max-w-[30.25rem] p-4 pb-[27px] pt-6 max-md:max-w-full [&>.tabsContent]:mt-4">
                    <div className="absolute left-1/2 top-[6px] flex h-1 w-20 -translate-x-1/2 transform rounded-full bg-primaryBorder md:hidden"></div>
                    <Tabs
                      classes="[&>ul>li>button]:font-bold"
                      tabs={tabs}
                      setSelectedTab={setSelectedTab}
                    />
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default Auth;
