'use client';

import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
} from 'react';
import { X } from 'lucide-react';
import { useRouter } from 'next/navigation';

import IconButton from '@/components/Common/Button/IconButton';
import SearchModalSidebar from './SearchModalSidebar';
import SearchIcon from '@/assets/icons/Search';
import SearchFill from '@/assets/icons/SearchFill';
import ThemeSelect from '@/components/Common/InputField/ThemeSelect';
import PlayCard from '@/components/PlayCard';
import MainLoader from '@/components/Common/Loader/MainLoader';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import CurrencySelect from '@/components/Common/InputField/CurrencySelect';

import {
  useCategoryQuery,
  useGetFavoritesGamesQuery,
  useGetRecentGamesQuery,
  useProviderQuery,
} from '@/reactQuery/gamesQuery';
import useCategory from '@/hooks/useCategory';
import useAuthStore from '@/store/useAuthStore';
import { slugify } from '@/utils/helper';

// Debounce hook
function useDebounce(value, delay = 500) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);

  return [debouncedValue, setDebouncedValue];
}

function SearchModal({
  onClose,
  inputWidth = 'max-w-[14.375rem] max-sm:max-w-full',
}) {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore((state) => state);

  const options = [
    { value: '1', label: 'Popular' },
    { value: '2', label: 'Ordered A-Z' },
    { value: '3', label: 'Ordered Z-A' },
  ];

  const { data: providers } = useProviderQuery();
  const { data: categoryLabels } = useCategoryQuery();

  const [selectedProvider, setSelectedProvider] = useState({
    value: null,
    label: 'All',
  });
  const [selectedSortBy, setSelectedSortBy] = useState({
    value: '1',
    label: 'Popular',
  });
  const [activeSideBar, setActiveSideBar] = useState(null);
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useDebounce(
    localSearchQuery,
    500,
  );
  const [limit, setLimit] = useState(24);

  const isNormalCategory =
    activeSideBar !== 'Favorites' && activeSideBar !== 'Continue Playing';

  const {
    casinoGames,
    gamesLoading,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    dataUpdatedAt,
    refetch: refetchCategory,
  } = useCategory({
    limit,
    categoryName: isNormalCategory ? activeSideBar : null,
    pageNo: 1,
    gameName: debouncedSearchQuery || undefined,
    providers: selectedProvider?.value ?? null,
    sortBy: selectedSortBy?.value ?? null,
    enabled: isNormalCategory,
  });

  const {
    data: favoritesGames,
    isLoading: favoritesLoading,
    isFetching: favoritesIsFetching,
    fetchNextPage: favoritesFetchNextPage,
    hasNextPage: favoritesHasNextPage,
    isFetchingNextPage: favoritesIsFetchingNextPage,
  } = useGetFavoritesGamesQuery({
    enabled: activeSideBar === 'Favorites' && isAuthenticated,
    gameName: debouncedSearchQuery,
    providers: selectedProvider?.value ?? null,
    sortBy: selectedSortBy?.value ?? null,
    limit,
  });

  const { data: recentGames, isLoading: recentLoading } =
    useGetRecentGamesQuery({
      enabled: activeSideBar === 'Continue Playing' && isAuthenticated,
      search: debouncedSearchQuery,
      providers: selectedProvider?.value ?? null,
      sortBy: selectedSortBy?.value ?? null,
    });

  useEffect(() => {
    if (activeSideBar === null && localSearchQuery === '') {
      setLimit(24);
      refetchCategory();
    }
  }, [activeSideBar, localSearchQuery, refetchCategory]);

  const gamesList = useMemo(() => {
    if (activeSideBar === 'Favorites') {
      return favoritesGames?.games || [];
    } else if (activeSideBar === 'Continue Playing') {
      return recentGames || [];
    }
    return (
      casinoGames?.pages?.flatMap((page) => page.data.casinoGames.rows || []) ||
      []
    );
  }, [activeSideBar, favoritesGames, casinoGames?.pages, recentGames]);

  const providerOptions = useMemo(() => {
    if (!providers?.data?.providers) return [];
    return [
      { value: null, label: 'All' },
      ...providers.data.providers.map((p) => ({
        value: p.id,
        label: typeof p.name === 'object' ? p.name.EN : p.name,
      })),
    ];
  }, [providers]);

  const handleSortByChange = (sortOption) => {
    setSelectedSortBy(sortOption);
    setLimit(24);
  };

  const handleProviderChange = (provider) => {
    setSelectedProvider(provider);
    setLimit(24);
  };

  const handleSideBarClick = (item) => {
    setActiveSideBar(item.name.EN);
    setLimit(24);
  };

  const handleSearchInput = (e) => setLocalSearchQuery(e.target.value);

  const handleLoadMore = () => {
    if (activeSideBar === 'Favorites') {
      if (!favoritesIsFetchingNextPage && favoritesHasNextPage) {
        favoritesFetchNextPage();
      }
    } else if (isNormalCategory) {
      if (!isFetchingNextPage && hasNextPage) {
        fetchNextPage();
      }
    }
  };

  const handleReset = () => {
    setSelectedProvider({ value: null, label: 'All' });
    setSelectedSortBy({ value: '1', label: 'Popular' });
    setActiveSideBar(null);
    setLocalSearchQuery('');
    setDebouncedSearchQuery('');
    setLimit(24);
  };

  const shouldRenderGames =
    gamesList?.length > 0 &&
    (isNormalCategory
      ? Object.keys(gamesList[0] || {}).some((k) => k !== 'casinoCategory')
      : true);

  const scrollContainerRef = useRef(null);

  const handleScroll = useCallback(() => {
    const el = scrollContainerRef.current;
    if (!el) return;
    if (el.scrollHeight - el.scrollTop <= el.clientHeight + 100) {
      handleLoadMore();
    }
  }, [handleLoadMore]);

  useEffect(() => {
    const el = scrollContainerRef.current;
    if (!el) return;
    el.addEventListener('scroll', handleScroll);
    return () => el.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black-850"
    >
      <div className="relative z-10 mx-auto w-full max-w-[86.625rem] rounded-[2rem] bg-slateGray-700 p-8 max-lg:mx-[0.313rem] max-lg:rounded-lg max-sm:px-2.5 max-sm:py-3">
        <div className="scrollbar-none max-xl:pb-8  max-sm:pb-0">
          <div className="relative flex gap-8">
            <div className="max-h-[calc(100vh-6.25rem)] min-w-[12.6875rem] overflow-y-auto max-lg:hidden">
              <SearchModalSidebar
                searchOptions={categoryLabels?.data?.categories || []}
                handleSideBarClick={handleSideBarClick}
                handleReset={handleReset}
              />
            </div>

            <div className="w-full">
              <div className="top-filter sticky top-[-1px] z-10 bg-slateGray-700 pb-2">
                <div className="mb-1 flex w-full items-center justify-between">
                  <h3 className="text-white text-xl font-medium max-sm:text-[.9375rem]">
                    SEARCH GAMES
                  </h3>
                  <IconButton onClick={onClose} className="h-6 w-6 min-w-6">
                    <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                  </IconButton>
                </div>

                <div className="my-0 flex w-full justify-center gap-5 md:my-2 max-xl:flex-col max-sm:gap-2">
                  <div className="relative w-full">
                    <SearchFill className="absolute left-2 top-3 h-5 w-5 max-sm:top-[.375rem] [&>path]:stroke-steelTeal-200" />
                    <input
                      type="text"
                      value={localSearchQuery}
                      onChange={handleSearchInput}
                      placeholder="Search Filter"
                      className="text-white placeholder:text-white h-8 w-full rounded-[10px] border border-solid border-transparent bg-inputBgColor p-[10px] ps-9 text-base font-normal leading-none placeholder:font-semibold placeholder:opacity-75 focus:border focus:border-solid focus:border-borderColor-100 md:h-11 max-sm:text-base"
                    />
                  </div>

                  <div className="search-select-wrap flex justify-between gap-2 lg:gap-5">
                    <CurrencySelect
                      placeholder="Select Provider"
                      options={providerOptions}
                      value={selectedProvider}
                      onChange={setSelectedProvider}
                      showProviderText={true}
                      providerLabel="Providers"
                      selectMargin="0"
                    />
                    <CurrencySelect
                      placeholder="Select Order"
                      options={options}
                      value={selectedSortBy}
                      onChange={handleSortByChange}
                      showProviderText={true}
                      providerLabel="Sort by:"
                      selectMargin="0"
                    />
                  </div>
                </div>
              </div>

              <div
                ref={scrollContainerRef}
                className="max-h-[calc(100vh-230px)] overflow-y-auto max-sm:max-h-[calc(100vh-400px)]"
              >
                {(activeSideBar === 'Favorites'
                  ? favoritesLoading
                  : activeSideBar === 'Continue Playing'
                    ? recentLoading
                    : gamesLoading) && gamesList.length === 0 ? (
                  <div className="z-50 flex items-center justify-center">
                    <MainLoader className="w-32" />
                  </div>
                ) : shouldRenderGames ? (
                  <div className="card-wrap grid grid-cols-5 gap-[.375rem] md:gap-5 max-xl:grid-cols-3 max-md:grid-cols-3  max-xs:grid-cols-2">
                    {gamesList.map((game) => {
                      const {
                        id,
                        casinoGameId,
                        iconUrl,
                        name,
                        isFavorite,
                        casinoProvider,
                        casinoGame,
                        icon_url,
                      } = game;
                      const providerNameFromDotProp =
                        game['casinoProvider.name'];
                      const gameId = casinoGameId ?? id;
                      const gameImage =
                        iconUrl ?? icon_url ?? casinoGame?.iconUrl ?? '';
                      const gameName =
                        name?.EN ??
                        casinoGame?.name?.EN ??
                        casinoGame?.name ??
                        '';
                      const favorite =
                        isFavorite ?? casinoGame?.isFavorite ?? true;
                      const providerName =
                        casinoProvider?.name ??
                        casinoGame?.casinoProvider?.name ??
                        providerNameFromDotProp ??
                        '';
                      const gameUrl = `/casino/games/${slugify(providerName)}/${slugify(gameName)}`;

                      return (
                        <PlayCard
                          key={gameId}
                          gameId={gameId}
                          gameImage={gameImage}
                          gameName={gameName}
                          isFavorite={favorite}
                          shouldUpdate={true}
                          sizeVariant="fixed"
                          onClick={() => {
                            router.push(gameUrl);
                            onClose();
                          }}
                          providerName={providerName}
                        />
                      );
                    })}
                  </div>
                ) : (
                  <div className="flex h-96 items-center justify-center">
                    No games are available
                  </div>
                )}

                {activeSideBar !== 'Continue Playing' &&
                  (activeSideBar === 'Favorites'
                    ? favoritesHasNextPage
                    : hasNextPage) && (
                    <div className="flex justify-center py-6">
                      {(
                        activeSideBar === 'Favorites'
                          ? favoritesIsFetchingNextPage
                          : isFetchingNextPage
                      )
                        ? 'Loading more...'
                        : ''}
                    </div>
                  )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SearchModal;
