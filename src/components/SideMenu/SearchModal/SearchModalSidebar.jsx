import React, { useState } from 'react';
import CasinoIcon from '@/assets/images/svg-images/all-icons.svg';
import SlotsIcon from '@/assets/icons/SlotsIcon';
import Image from 'next/image';
import useAuthStore from '@/store/useAuthStore';

export default function SearchModalSidebar({
  searchOptions,
  handleSideBarClick = () => {},
  handleReset = () => {},
}) {
  const [activeTab, setActiveTab] = useState(null);
  const { isAuthenticated } = useAuthStore((state) => state);

  console.log('searchOptions', searchOptions);

  const handleClick = (item) => {
    setActiveTab(item.id);
    handleSideBarClick(item);
  };

  return (
    <div className="flex w-full rounded-[1.875rem] bg-slateGray-600 p-5">
      <ul className="flex h-[calc(100vh-150px)] w-full flex-col gap-8 overflow-y-auto text-[.9375rem] font-semibold text-steelTeal-200">
        <li
          className={`before:lef-0 relative flex cursor-pointer items-center gap-2 rounded-lg px-2   transition before:absolute before:-bottom-4 before:left-0 before:h-[1px] before:w-full before:bg-white-350 ${
            activeTab === 'all'
              ? 'text-white-1000'
              : 'hover:text-white text-steelTeal-200'
          }`}
          onClick={() => {
            setActiveTab('all');
            handleReset();
          }}
        >
          {/* <CasinoIcon
            className={`h-6 w-6 ${
              activeTab === 'all' ? 'text-white' : 'text-steelTeal-200'
            }`}
          /> */}
          <Image
            src={CasinoIcon}
            alt="CasinoIcon"
            width={24}
            height={24}
            className={`transition ${
              activeTab === 'all' ? 'contrast-1' : 'contrast-0'
            }`}
          />
          All Games
        </li>
        {searchOptions
          ?.filter((item) => {
            const isGameFilter = item.categoryFor?.includes('gameFilter');
            if (!isAuthenticated && (item.id === 260 || item.id === 255)) {
              return false;
            }
            return isGameFilter;
          })
          .map((item) => (
            <li
              key={item.id}
              onClick={() => handleClick(item)}
              className={`flex cursor-pointer items-center gap-2 rounded-lg px-2 transition ${
                activeTab === item.id
                  ? 'text-white'
                  : 'hover:text-white text-steelTeal-200'
              }`}
            >
              {item.icon_url ? (
                <Image
                  src={item.icon_url}
                  alt={item.name?.EN || 'Unknown'}
                  width={24}
                  height={24}
                  className={`transition ${
                    activeTab === item.id ? 'contrast-1' : 'contrast-0'
                  }`}
                />
              ) : (
                <SlotsIcon
                  className={`h-6 w-6 ${
                    activeTab === item.id ? 'text-white' : 'text-steelTeal-200'
                  }`}
                />
              )}
              {item.name.EN}
            </li>
          ))}
      </ul>
    </div>
  );
}
