'use client';

import React, { useMemo, useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import useGeneralStore from '@/store/useGeneralStore';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import { useQueryClient } from '@tanstack/react-query';
import { slugify } from '@/utils/helper';
import { useCategoryQuery } from '@/reactQuery/gamesQuery';
import useHelperHook from '@/hooks/useHelperHook';

import HamburgerIcon from '@/assets/icons/Hamburger';
import SearchFill from '@/assets/icons/SearchFill';
import FavoriteIcon from '@/assets/icons/Favorite';
import RecentIcon from '@/assets/icons/RecentIcon';
import ChallengesIcon from '@/assets/icons/ChallengesIcon';
import CasinoIcon from '@/assets/icons/CasinoIcon';
import OriginalsIcon from '@/assets/icons/Originals';
import SportsIcon from '@/assets/icons/sports';
import HomeIcon from '@/assets/icons/HomeIcon';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import VIPIcon from '@/assets/icons/VIP';
import LogoutIcon from '@/assets/icons/Logout';

import MyBetsIcon from '@/assets/icons/MyBetsIcon';
import LiveIcon from '@/assets/icons/LiveIcon';
import ScheduleIcon from '@/assets/icons/ScheduleIcon';
import SoccerIcon from '@/assets/icons/SoccerIcon';
import HorseracingIcon from '@/assets/icons/HorseracingIcon';
import TennisIcon from '@/assets/icons/TennisIcon';
import FifaIcon from '@/assets/icons/FifaIcon';
import IceHockeyIcon from '@/assets/icons/IceHockeyIcon';
import VolleyballIcon from '@/assets/icons/VolleyballIcon';
import FavouriteStarIcon from '@/assets/icons/FavouriteStarIcon';
import LuckyGamesIcon from '@/assets/icons/LuckyGamesIcon';
import PromotionsIcon from '@/assets/icons/PromotionsIcon';
import AffiliateIcon from '@/assets/icons/AffiliateIcon';
import BlogIcon from '@/assets/icons/BlogIcon';
import ForumIcon from '@/assets/icons/ForumIcon';
import ResponsibleGamingIcon from '@/assets/icons/ResponsibleGamingIcon';
import LiveSupportIcon from '@/assets/icons/LiveSupportIcon';

import DropDown from '../Common/DropDown';
import FeatureTabs from '../FeatureTabs';
import PrimaryButton from '../Common/Button/PrimaryButton';
import SearchModal from './SearchModal/SearchModal';

import Ipay from '@/assets/images/svg-images/ipay.svg';
import MasterCardImg from '@/assets/images/svg-images/master.svg';
import VisaImg from '@/assets/images/svg-images/visa.svg';
import Gpay from '@/assets/images/svg-images/gpay.svg';

import TableTennis from '@/assets/icons/TableTennis';
import BaseBall from '@/assets/icons/BaseBall';
import HandBall from '@/assets/icons/HandBall';
import GolfBall from '@/assets/icons/GolfBall';
import Boxing from '@/assets/icons/Boxing';
import ContinuePlayingIcon from '@/assets/icons/ContinuePlaying';
import useGameStore from '@/store/useGameStore';
import WalletModal from '@/app/account/WalletModal/WalletModal';
import Rugby from '@/assets/icons/Rugby';
import AmericanFootball from '@/assets/icons/AmericanFootball';
import Cycling from '@/assets/icons/Cycling';
import Specials from '@/assets/icons/Specials';
import Snooker from '@/assets/icons/Snooker';
import Cricket from '@/assets/icons/Cricket';
import Darts from '@/assets/icons/Darts';
import Waterpolo from '@/assets/icons/Waterpolo';
import Squash from '@/assets/icons/Squash';
import Formula1 from '@/assets/icons/Formula1';
import Counterstrike from '@/assets/icons/Counterstrike';
import MMA from '@/assets/icons/MMA';
import { useWindowSize } from '@/hooks/useWindowSize';
import useIsMobile from '@/hooks/useIsMobile';
import { usePathname } from 'next/navigation'
import useAuthModalStore from '@/store/useAuthModalStore';
import CloseIcon from '@/assets/icons/CloseIcon';

const Auth = dynamic(() => import('../Auth'), { ssr: false });

const getSidebarState = () => {
  const saved = localStorage.getItem('sidebarMenuOpen');
  return saved !== null ? JSON.parse(saved) : false;
};

const SideMenu = ({ inputWidth = 'max-w-[14.375rem] max-sm:max-w-full' }) => {
  const { setActiveMenu, openMenu, setOpenMenu, openChat } = useGeneralStore();
  const recentGames = useGameStore((state) => state.recentGames);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const pathname = usePathname();
  const { openModal } = useModalStore();
  const { openModal: AuthModalOpen } = useAuthModalStore();
  const { setSelectedTab } = useAuthTab();
  const { isAuthenticated } = useAuthStore();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { logout, isLoading } = useHelperHook();
  const { width } = useWindowSize();
  const isMobile = useIsMobile();

  useEffect(() => {
    if (!isMobile) {
      const savedState = getSidebarState();
      setOpenMenu(savedState);
    } else {
      setOpenMenu(false);
    }
  }, [pathname]);

  const handleLogout = async () => {
    if (!isLoading) {
      logout();
      queryClient.removeQueries();
      window.dispatchEvent(new Event('logout'));
      router.push('/');
    }
  };

  const { data: categoryLabels } = useCategoryQuery();

  const TABS = useMemo(() => {
    const staticTabs = [
      { text: 'Lobby', href: '/lobby', component: HomeIcon },
      ...(isAuthenticated
        ? [{ text: 'For You', href: '/casino', component: ForYouIcon }]
        : []),
    ];

    const categoryTabs =
      categoryLabels?.data?.categories
        ?.filter(
          (cat) =>
            Array.isArray(cat.categoryFor) &&
            cat.categoryFor.includes('sidebar'),
        )
        .map((cat) => {
          if (cat.name?.EN === 'Publishers') {
            return {
              text: 'Publishers',
              href: '/publishers',
              component: () => (
                <img
                  src={cat.icon_url}
                  alt="Publishers"
                  className="size-6 object-contain grayscale"
                />
              ),
              onClick: () => localStorage.removeItem('selectedCategory'),
            };
          }
          return {
            text: cat.name?.EN || '',
            href: `/category/${slugify(cat.name?.EN)}`,
            component: () => (
              <img
                src={cat.icon_url}
                alt={cat.name?.EN || ''}
                className="size-6 object-contain grayscale"
              />
            ),
            onClick: () => localStorage.removeItem('selectedCategory'),
          };
        }) || [];

    return [...staticTabs, ...categoryTabs];
  }, [categoryLabels, isAuthenticated]);

  const originalsMenuList = [];
  const sportsMenuList = [
    { component: MyBetsIcon, text: 'My Bets', href: '#' },
    { component: LiveIcon, text: 'Live', href: '#' },
    { component: ScheduleIcon, text: 'Schedule', href: '#' },
    { component: FavouriteStarIcon, text: 'Favourites', href: '#' },
    { component: SoccerIcon, text: 'Soccer', href: '#' },
    { component: HorseracingIcon, text: 'Horse Racing', href: '#' },
    { component: TennisIcon, text: 'Tennis', href: '#' },
    { component: FifaIcon, text: 'Fifa', href: '#' },
    { component: SportsIcon, text: 'Basketball', href: '#' },
    { component: IceHockeyIcon, text: 'Ice Hockey', href: '#' },
    { component: VolleyballIcon, text: 'Volleyball', href: '#' },
    { component: TableTennis, text: 'Table Tennis', href: '#' },
    { component: BaseBall, text: 'Baseball', href: '#' },
    { component: HandBall, text: 'Handball', href: '#' },
    { component: GolfBall, text: 'Golf', href: '#' },
    { component: Boxing, text: 'Boxing', href: '#' },
    { component: Rugby, text: 'Rugby', href: '#' },
    { component: Rugby, text: 'Aussie Rules', href: '#' },
    { component: AmericanFootball, text: 'American Football', href: '#' },
    { component: Cycling, text: 'Cycling', href: '#' },
    { component: Specials, text: 'Specials', href: '#' },
    { component: Snooker, text: 'Snooker', href: '#' },
    { component: Cricket, text: 'Cricket', href: '#' },
    { component: Darts, text: 'Darts', href: '#' },
    { component: Waterpolo, text: 'Waterpolo', href: '#' },
    { component: Squash, text: 'Squash', href: '#' },
    { component: Formula1, text: 'Formula 1', href: '#' },
    { component: Counterstrike, text: 'Counterstrike', href: '#' },
    { component: MMA, text: 'MMA', href: '#' },
  ];

  const handleDropdownClick = () => {
    setOpenMenu(false);
    localStorage.setItem('sidebarMenuOpen', false);
  };

  const handleMenuClick = (e, href) => {
    e.preventDefault();

    if (!isAuthenticated) {
      localStorage.setItem('activeTab', '1');
      setSelectedTab(1);
      AuthModalOpen(<Auth />);
      return;
    }

    router.push(href);
    setActiveMenu('');
  };

  const handleDropdownToggle = (dropdownName) => {
    setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);
  };

  const handleToggleSidebar = () => {
    const newState = !openMenu;
    if (!isMobile) {
      localStorage.setItem('sidebarMenuOpen', newState);
      setOpenMenu(newState);
    }
  };

  return (
    <>
      {(!openMenu || width <= 768) && (
        <div
          className="fixed inset-0 top-[3rem] z-40 hidden bg-black-850 max-lg:block max-md:hidden"
          onClick={handleToggleSidebar}
        />
      )}
      <aside
        id="default-sidebar"
        className={`${
          openMenu ? 'md:w-16 max-md:w-full' : ' w-0 md:w-60 '
        } fixed top-[52px]  z-30 h-screen bg-sidebarBgColor md:top-0 xl:left-0 max-xl:top-[52px]`}
        aria-label="Sidebar"
      >
        {(width > 768 || !openChat) && (
          <div
            className={`
      pb mt-2 flex items-center gap-3 px-4 pb-3 md:mb-2
      md:mt-1 md:border-b md:border-white-300 md:pb-[0.45rem] lg:min-h-12
      ${
        openMenu
          ? 'md:mb-0 md:justify-center md:border-none md:!pb-[0.35rem]'
          : 'justify-between'
      }
    `}
          >
            <HamburgerIcon
              className={`block size-6 cursor-pointer max-md:hidden ${openMenu ? 'mt-2.5' : ''}`}
              onClick={handleToggleSidebar}
            />
            <div
              className={`w-full max-md:hidden ${openMenu ? 'hidden' : 'block'}`}
            >
              <FeatureTabs />
            </div>
          </div>
        )}
        {/* <div
          className={`${
            openMenu
              ? 'case-1 hidden'
              : 'case-2 hidden max-lg:flex max-md:hidden'
          } rounded-tl-0 rounded-bl-0 absolute -right-[2.3rem] top-[0.313rem] flex h-9 w-9 cursor-pointer items-center justify-center rounded-br-md
        rounded-tr-md bg-inputBgColor p-2`}
        >
          <CloseIcon
            className={` size-6 cursor-pointer [&_*]:fill-steelTeal-200  ${openMenu ? 'mt-2.5' : ''}`}
            onClick={handleToggleSidebar}
          />
        </div> */}

        <div className="scrollbar-none flex h-full max-h-[calc(100dvh-3.75rem)] flex-col  justify-between overflow-y-auto overflow-x-hidden pb-4 max-xl:pb-[8rem] max-lg:h-full max-lg:pb-32 max-sm:pb-24">
          <div>
            <div
              className={`block px-3 pb-2 ${openMenu ? 'justify-start' : 'justify-between'}`}
            >
              <div
                className={`text-white relative flex h-11 w-full cursor-pointer items-center rounded-[0.625rem] border border-solid border-transparent bg-inputBgColor text-base font-normal leading-none max-sm:text-base ${
                  openMenu ? 'p-2.5 py-1 max-md:ps-9' : 'p-[10px] ps-9'
                }`}
                onClick={() => setIsModalOpen(true)}
              >
                <SearchFill
                  className={`absolute left-2 top-3 h-5 w-5 [&>path]:stroke-steelTeal-200 ${openMenu ? 'left-[0.6rem]' : ''}`}
                />
                <span
                  className={`${openMenu ? 'hidden max-md:block' : 'block'}`}
                >
                  Search Games
                </span>
              </div>
              {isModalOpen && (
                <SearchModal onClose={() => setIsModalOpen(false)} />
              )}
            </div>

            <div className="flex flex-col gap-4 p-4">
              <Link
                href="/favorites"
                className={`flex items-center gap-2 rounded-md capitalize ${openMenu ? 'md:justify-center' : 'justify-start'}`}
                onClick={(e) => handleMenuClick(e, '/favorites')}
              >
                <FavoriteIcon className="size-6 min-w-6 fill-steelTeal-1000" />
                <span
                  className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                >
                  favorites
                </span>
              </Link>

              {isAuthenticated && recentGames.length !== 0 && (
                <Link
                  href="/continue-playing"
                  className={`flex items-center gap-2 rounded-md capitalize ${openMenu ? 'md:justify-center' : 'justify-start'}`}
                  onClick={(e) => handleMenuClick(e, '/continue-playing')}
                >
                  <ContinuePlayingIcon className="size-6 min-w-6 fill-steelTeal-1000" />
                  <span
                    className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                  >
                    Continue Playing
                  </span>
                </Link>
              )}

              <Link
                href="#"
                className={`flex items-center gap-2 rounded-md capitalize ${openMenu ? 'md:justify-center' : 'justify-start'}`}
              >
                <ChallengesIcon className="min-w-6fill-steelTeal-1000  size-6" />
                <span
                  className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                >
                  Challanges
                </span>
              </Link>
            </div>

            <div className="flex flex-col gap-4 border-t border-white-300 p-4">
              <DropDown
                openMenu={openMenu}
                isOpen={activeDropdown === 'casino'}
                onToggle={() => handleDropdownToggle('casino')}
                items={{
                  title: (
                    <div
                      className={`flex w-full items-center gap-2 ${openMenu ? 'md:justify-center' : 'justify-start'}`}
                      onClick={handleDropdownClick}
                    >
                      <CasinoIcon className="size-6  min-w-6 fill-steelTeal-200" />
                      <span
                        className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                      >
                        Casino
                      </span>
                    </div>
                  ),
                  content: <DropDownContent contentList={TABS} />,
                }}
              />

              <DropDown
                isOpen={activeDropdown === 'originals'}
                onToggle={() => handleDropdownToggle('originals')}
                items={{
                  title: (
                    <div
                      className={`flex w-full items-center gap-2 ${openMenu ? 'md:justify-center' : 'justify-start'}`}
                      onClick={handleDropdownClick}
                    >
                      <OriginalsIcon className="size-6 min-w-6 fill-steelTeal-200" />
                      <span
                        className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                      >
                        Originals
                      </span>
                    </div>
                  ),
                  content: <DropDownContent contentList={originalsMenuList} />,
                }}
              />

              <DropDown
                isOpen={activeDropdown === 'sports'}
                onToggle={() => handleDropdownToggle('sports')}
                items={{
                  title: (
                    <div
                      className={`flex w-full items-center gap-2 ${openMenu ? 'md:justify-center' : 'justify-start'}`}
                      onClick={handleDropdownClick}
                    >
                      <SportsIcon className="size-6 min-w-6 fill-steelTeal-200" />
                      <span
                        className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                      >
                        Sports
                      </span>
                    </div>
                  ),
                  content: <DropDownContent contentList={sportsMenuList} />,
                }}
              />
            </div>

            <div className="border-b border-t border-white-300 p-4">
              <h4
                className={`mb-3 text-xs font-semibold uppercase text-white-400 ${openMenu ? 'md:hidden' : 'block'}`}
              >
                ENJOY EXCLUSIVE PERKS
              </h4>
              <Link
                href="/vip"
                className={`flex items-center gap-2 rounded-md capitalize ${openMenu ? 'md:justify-center' : 'justify-start'}`}
              >
                <VIPIcon className="size-6 min-w-6 fill-steelTeal-1000" />
                <span
                  className={`text-[.9375rem] font-semibold text-steelTeal-200 ${openMenu ? 'md:hidden' : 'inline-block'}`}
                >
                  VIP Club
                </span>
              </Link>
            </div>

            <div className="flex flex-col gap-4 p-4">
              {[
                { icon: LuckyGamesIcon, text: 'Lucky Games', href: '#' },
                { icon: PromotionsIcon, text: 'Promotions', href: '#' },
                { icon: AffiliateIcon, text: 'Affiliate', href: '#' },
                { icon: BlogIcon, text: 'Blog', href: '#' },
                { icon: ForumIcon, text: 'Forum', href: '#' },
                {
                  icon: ResponsibleGamingIcon,
                  text: 'Knowledge Base',
                  href: '#',
                },
                { icon: LiveSupportIcon, text: 'Live Support', href: '#' },
              ].map(({ icon: Icon, text, href }) => (
                <Link
                  key={text}
                  href={href}
                  className={`flex items-center gap-2 rounded-md capitalize ${
                    openMenu ? 'md:justify-center' : 'justify-start'
                  }`}
                >
                  <Icon className="size-6 min-w-6 fill-steelTeal-1000" />
                  <span
                    className={`text-[.9375rem] font-semibold text-steelTeal-200 ${
                      openMenu ? 'md:hidden' : 'inline-block'
                    }`}
                  >
                    {text}
                  </span>
                </Link>
              ))}
            </div>

            {isAuthenticated && (
              <div className="border-t border-white-300 p-4">
                <Link
                  href="/"
                  className={`flex items-center gap-2 rounded-md capitalize ${
                    openMenu ? 'md:justify-center' : 'justify-start'
                  }`}
                  onClick={handleLogout}
                >
                  <LogoutIcon className="size-6 min-w-6 fill-steelTeal-1000" />
                  <span
                    className={`text-[.9375rem] font-semibold text-steelTeal-200 ${
                      openMenu ? 'md:hidden' : 'inline-block'
                    }`}
                  >
                    Logout
                  </span>
                </Link>
              </div>
            )}
          </div>
          <div>
            <div
              className={`mt-auto border-none px-4 ${openMenu ? 'md:hidden' : 'block'}`}
            >
              <PrimaryButton
                className="text-white w-full rounded-[0.625rem] border-secondaryBtnBg bg-secondaryBtnBg px-4 py-2 text-[.9375rem] font-semibold"
                onClick={(e) => {
                  e.preventDefault();
                  if (!isAuthenticated) {
                    localStorage.setItem('activeTab', 1);
                    setSelectedTab(1);
                    AuthModalOpen(<Auth />);
                    return;
                  } else {
                    openModal(<WalletModal />);
                  }
                }}
              >
                DEPOSIT
              </PrimaryButton>

              <div className="mt-[1.0625rem] flex items-center justify-between gap-1">
                <Image src={Ipay} alt="Ipay" className="cursor-pointer" />
                <Image
                  src={MasterCardImg}
                  alt="MasterCard"
                  className="cursor-pointer"
                />
                <Image src={VisaImg} alt="Visa" className="cursor-pointer" />
                <Image src={Gpay} alt="Gpay" className="cursor-pointer" />
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};

export default SideMenu;

function DropDownContent({ contentList }) {
  return contentList.map((item, index) => {
    const IconComponent = item?.component;
    if (item?.button) {
      return (
        <div
          role="button"
          key={index}
          onClick={item.onClick}
          tabIndex={0}
          onKeyDown={() => {}}
          className="flex w-full items-center justify-start gap-2 rounded-md p-2 hover:bg-inputBgColor"
        >
          <IconComponent className="size-6 min-w-6 fill-steelTeal-1000" />
          <span className="text-[.9375rem] font-semibold text-steelTeal-200">
            {item.text}
          </span>
        </div>
      );
    }
    return (
      <Link
        key={index}
        href={item.href}
        onClick={item.onClick}
        className="flex items-center justify-start gap-2 rounded-md p-2 pl-[2px] hover:bg-inputBgColor"
      >
        <IconComponent className="size-6 min-w-6 fill-steelTeal-1000" />
        <span className="text-[.9375rem] font-semibold text-steelTeal-200">
          {item.text}
        </span>
      </Link>
    );
  });
}
