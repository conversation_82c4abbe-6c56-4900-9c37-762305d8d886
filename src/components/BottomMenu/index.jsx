'use client';

import React, { useEffect, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import useGeneralStore from '@/store/useGeneralStore';
import BrowseIcon from '@/assets/icons/BrowseIcon';
import CasinoIcon from '@/assets/icons/CasinoIcon';
import BetIcon from '@/assets/icons/BetIcon';
import FansbetIcon from '@/assets/icons/Fansbet';
import SportsIcon from '@/assets/icons/sports';
import ChatIcon from '@/assets/icons/Chat';

function BottomMenu() {
  const {
    setOpenChat,
    setActiveMenu,
    activeMenu,
    setOpenMenu,
    openMenu,
    openLobbyMenu,
    setOpenLobby,
    openChat,
  } = useGeneralStore();

  const router = useRouter();
  const pathname = usePathname();

  // Menu items
  const menuItems = useMemo(
    () => [
      {
        title: 'browse',
        label: 'Browse',
        icon: <BrowseIcon className="h-6 w-6 fill-richBlack-800 transition-all duration-200" activeMenu={activeMenu === 'browse'} />,
        onClick: () => {
          const newMenuState = !openMenu;
          setOpenChat(false);
          setOpenLobby(false);
          setOpenMenu(newMenuState);
          setActiveMenu(newMenuState ? 'browse' : pathname);
          sessionStorage.removeItem('lobby');
          sessionStorage.removeItem('chat');
        },
      },
      {
        title: '/casino',
        label: 'Casino',
        icon: <CasinoIcon className="h-6 w-6 fill-richBlack-800 transition-all duration-200" activeMenu={activeMenu === '/casino'} />,
        onClick: () => {
          setActiveMenu('/casino');
          setOpenChat(false);
          setOpenMenu(false);
          setOpenLobby(false);
          sessionStorage.removeItem('lobby');
          sessionStorage.removeItem('chat');
          router.push('/casino');
        },
      },
      {
        title: '/bets',
        label: 'Bets',
        icon: <BetIcon className="h-6 w-6 fill-richBlack-800 transition-all duration-200" activeMenu={activeMenu === '/bets'} />,
        onClick: () => {
          setActiveMenu('/bets');
          setOpenChat(false);
          setOpenMenu(false);
          setOpenLobby(false);
          sessionStorage.removeItem('lobby');
          sessionStorage.removeItem('chat');
          router.push('/bets');
        },
      },
      {
        title: '/sports',
        label: 'Sports',
        icon: <SportsIcon className="h-6 w-6 fill-richBlack-800 transition-all duration-200" activeMenu={activeMenu === '/sports'} />,
        onClick: () => {
          setActiveMenu('/sports');
          setOpenChat(false);
          setOpenMenu(false);
          setOpenLobby(false);
          sessionStorage.removeItem('lobby');
          sessionStorage.removeItem('chat');
          router.push('/sports');
        },
      },
      {
        title: 'lobby',
        label: 'Lobby',
        icon: <FansbetIcon className="h-6 w-6 fill-richBlack-800 transition-all duration-200" activeMenu={activeMenu === 'lobby'} />,
        onClick: () => {
          const newLobbyState = !openLobbyMenu;
          setOpenLobby(newLobbyState);
          setOpenChat(false);
          setOpenMenu(false);
          setActiveMenu(newLobbyState ? 'lobby' : pathname);
          if (newLobbyState) {
            sessionStorage.setItem('lobby', 'true');
          } else {
            sessionStorage.removeItem('lobby');
          }
          sessionStorage.removeItem('chat'); 
        },
      },
      {
        title: 'msg',
        label: 'Chats',
        icon: <ChatIcon className="h-6 w-6 fill-richBlack-800 transition-all duration-200" activeMenu={activeMenu === 'msg'} />,
        onClick: () => {
          const newChatState = !openChat;
          setOpenChat(newChatState);
          setOpenMenu(false);
          setOpenLobby(false);
          setActiveMenu(newChatState ? 'msg' : pathname);
          if (newChatState) {
            sessionStorage.setItem('chat', 'true');
          } else {
            sessionStorage.removeItem('chat');
          }
          sessionStorage.removeItem('lobby');
        },
      },
    ],
    [setActiveMenu, setOpenChat, setOpenMenu, setOpenLobby, openMenu, openLobbyMenu, openChat, router, activeMenu, pathname],
  );

  useEffect(() => {
    const savedLobby = sessionStorage.getItem('lobby') === 'true';
    const savedChat = sessionStorage.getItem('chat') === 'true';

    if (savedLobby) {
      setOpenLobby(true);
      setActiveMenu('lobby');
    } else if (savedChat) {
      setOpenChat(true);
      setActiveMenu('msg');
    } else if (['/casino', '/bets', '/sports'].includes(pathname)) {
      setActiveMenu(pathname);
    } else {
      setActiveMenu('browse');
    }
  }, []); 
  
  useEffect(() => {
    if (['browse', 'msg', 'lobby'].includes(activeMenu)) {
      document.body.classList.add('max-md:overflow-hidden');
    } else {
      document.body.classList.remove('max-md:overflow-hidden');
    }
  }, [activeMenu]);

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-40  hidden h-menuFooterHeight items-center justify-center border-t border-white-200 bg-black-1000 shadow-bottom-menu max-md:flex">
      <div className="mx-auto flex w-full max-w-[31.25rem] items-center justify-between">
        {menuItems.map((item) => (
          <button
            key={item.title}
            type="button"
            onClick={item.onClick}
            className="group relative flex min-w-[55px] flex-col items-center justify-center gap-[2px]"
          >
            <div
              className={`absolute left-0 top-[-8px] h-[2px] w-full bg-gradientIcon
                transition-opacity duration-200 [clip-path:polygon(0_0,_100%_0,_92%_100%,_8%_100%)]
                ${activeMenu === item.title ? 'opacity-100' : 'opacity-0'}`}
            ></div>
            <div className={`flex items-center justify-center rounded-md transition-all duration-200 ${activeMenu === item.title ? 'gradientIcon' : ''}`}>
              {item.icon}
            </div>
            <span className={`text-[12px] font-semibold transition-colors duration-200 ${activeMenu === item.title ? 'text-white-1000' : 'text-richBlack-800'}`}>
              {item.label}
            </span>
          </button>
        ))}
      </div>
    </nav>
  );
}

export default React.memo(BottomMenu);
