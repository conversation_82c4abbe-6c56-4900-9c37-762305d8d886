'use client';
import React, { useState } from 'react';
import { FileBarChart, X } from 'lucide-react';
import TreasureIcon from '@/assets/icons/Treasure';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Common/Button/IconButton';
import RainTransactions from './RainTransactions';
import TipsTransactions from './TipsTransaction';
import NoDataFound from '../Common/NoDataFound';

function TransactionsModal({ currentActiveTab }) {
  const { closeModal } = useModalStore((state) => state);
  const [activeTab, setActiveTab] = useState(
    currentActiveTab ? currentActiveTab : 'rain',
  );

  const handleCloseModal = () => {
    closeModal();
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[90vh] w-full max-w-xl">
        <div className="rounded-lg bg-maastrichtBlue-1000 p-2 shadow-lg">
          <div className="flex items-center justify-between p-2">
            <div className="flex items-center gap-3">
              <FileBarChart className="fill-steelTeal-100 transition-all duration-300 group-hover:fill-steelTeal-1000" />
              <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                Transactions
              </h3>
            </div>
            <div className="flex items-center gap-4">
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          <div className="p-4">
            <div className="mb-4 flex items-center justify-center">
              <div className="gap-1 rounded-full bg-cetaceanBlue-1000 p-1 md:gap-4">
                {['Buy', 'Redeem', 'Rain', 'Tips'].map((tab) => (
                  <button
                    key={tab}
                    type="button"
                    className={`flex items-center justify-center rounded-full px-3 py-2 text-sm font-semibold ${activeTab === tab.toLowerCase() ? 'bg-oxfordBlue-1000 text-white-1000' : 'text-steelTeal-1000'}`}
                    onClick={() => setActiveTab(tab.toLowerCase())}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            <div className="max-h-[70vh] overflow-y-auto">
              {activeTab === 'rain' && <RainTransactions />}
              {activeTab === 'tips' && (
                <TipsTransactions activeTab={activeTab} />
              )}
              {(activeTab === 'redeem' || activeTab === 'buy') && (
                <div className="p-10">
                  <NoDataFound className="w-28" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TransactionsModal;
