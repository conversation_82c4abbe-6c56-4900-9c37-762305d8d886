import React from 'react';

export default function CardSkeleton({ className = '' }) {
  return (
    <div className={`overflow-hidden rounded-xl ${className}`}>
      {/* Image Placeholder */}
      <div
        className="bg-white/10 relative h-40 w-full overflow-hidden rounded-xl border border-secondaryBtnBg
      "
      >
        <div className="animate-shimmer absolute  inset-0 bg-secondaryBtnBg bg-gradient-to-r from-transparent to-transparent" />
      </div>
    </div>
  );
}
