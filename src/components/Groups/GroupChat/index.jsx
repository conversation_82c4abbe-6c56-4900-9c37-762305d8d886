import ChatAvatar from '@/components/ChatAvatar';
import { ChatInput } from '@/components/ChatWindow/ChatInput';
import useChatWindow from '@/hooks/useChatWindow';
import useGroupChatWindow from '@/hooks/useGroupChatWindow';
import { useGetGroupChatsQuery } from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import { formatTo12Hour, isValidURL } from '@/utils/helper';
import { Grid } from '@giphy/react-components';
import Picker from '@emoji-mart/react';
import data from '@emoji-mart/data';
import Image from 'next/image';
import { useEffect, useCallback, useRef } from 'react';
import GroupCall from '@/components/ChatWindow/GroupChat/GroupCall';
import CallJoinButton from '../CallJoinButton';

export default function GroupChat({ groupData, userDetails, refetch }) {
  const {
    message,
    setMessage,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    showGifPicker,
    section,
    setSection,
    showSuggestions,
    selectedSuggestion,
    publicChats,
    isPublicChatsLoading,
    chatContainerRef,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessageForGroupChatPage,
    privateChat,
    recipientUser,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    livePlayersCount,
    newMessagesCount,
    scrollToBottom,
    handleGifSelect,
    fetchGifs,
    setGrabbedChat,
    updateGrabbedRainChat,
    setShowGifPicker,
  } = useChatWindow();

  const { setGroupChatForPage, groupChatForPage, setGroupName } =
    useGroupChatStore();
  // const { userDetails, isAuthenticated } = useAuthStore();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const pickerRef = useRef(null);

  const emojiButtonRef = useRef(null);
  const gifButtonRef = useRef(null);

  const {
    data: groupChatData,
    isLoading: isGroupChatsLoading,
    fetchNextPage: fetchNextGroupPage,
    hasNextPage: hasNextGroupChat,
    isFetchingNextPage: isFetchingOlderChats,
    refetch: refetchGroupChats,
  } = useGetGroupChatsQuery({
    groupId: groupData?.group?.id,
    // enabled: groupId: groupData?.id ,
  });
  useEffect(() => {
    setGroupChatForPage(groupChatData?.pages || []);
    setGroupName(groupData?.groupName || '');
  }, [groupChatData]);

  const handleSendMsg = (msg) => {
    handleSendMessageForGroupChatPage({ groupId: groupData?.group?.id });
  };
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [groupChatForPage]);

  const handleScroll = useCallback(async () => {
    if (!chatContainerRef.current || isFetchingOlderChats) return;

    if (chatContainerRef.current.scrollTop <= 0 && hasNextGroupChat) {
      const prevHeight = chatContainerRef.current.scrollHeight;

      await fetchNextGroupPage();

      requestAnimationFrame(() => {
        if (chatContainerRef.current) {
          const newHeight = chatContainerRef.current.scrollHeight;
          chatContainerRef.current.scrollTop = newHeight - prevHeight;
        }
      });
    }
  }, [hasNextGroupChat, isFetchingOlderChats, fetchNextGroupPage]);

  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container) return;
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  useEffect(() => {
    function handleClickOutside(event) {
      if (pickerRef.current && !pickerRef.current.contains(event.target)) {
        if (
          showEmojiPicker &&
          emojiButtonRef &&
          !emojiButtonRef?.current.contains(event.target)
        ) {
          setShowEmojiPicker(false);
        }
        if (
          showGifPicker &&
          gifButtonRef &&
          !gifButtonRef?.current.contains(event.target)
        ) {
          setShowGifPicker(false);
        }
      }
    }

    if (showEmojiPicker || showGifPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker, setShowEmojiPicker, showGifPicker, setShowGifPicker]);
  console.log(
    '🚀 ~ GroupChat ~ groupData:',
    groupData,
    groupChatData,
    groupChatForPage,
  );

  return (
    <div className="my-4 rounded-[.25rem] bg-erieBlack-200 p-4">
      <div className="max-h-[calc(100vh-150px)] overflow-y-auto pb-5">
        <div className="mb-[0.625rem] flex justify-between border-b border-white-370 pb-[0.625rem]">
          <h4 className="text-xl font-medium">{groupData?.groupName} Chat</h4>
          <CallJoinButton
            groupData={groupData}
            userDetails={userDetails}
            refetch={refetch}
          />
        </div>
        <div
          ref={chatContainerRef}
          className="h-[600px] overflow-y-auto" // give fixed height + scroll
        >
          {isFetchingOlderChats && (
            <div className="text-center text-xs text-slate-400">
              Loading older messages...
            </div>
          )}

          {groupChatForPage?.map((chat, index) => {
            const isReceiverMsg = chat.userId != userDetails?.id;
            return isReceiverMsg ? (
              <div className="mb-3 py-1.5" key={chat?.id}>
                <div className="flex items-center gap-1">
                  <ChatAvatar
                    key={chat?.id}
                    src={chat?.user?.imageUrl}
                    firstName={chat.user?.firstName}
                    lastName={chat.user?.lastName}
                    userName={chat.user?.username}
                    imageClassName="h-12 w-12 max-w-full rounded-full object-cover object-center"
                    imageWidth={48}
                    imageHeight={48}
                    avatarSize={48}
                  />

                  <div className="flex flex-col">
                    <div className="flex gap-2.5 text-steelTeal-200">
                      <div className="flex  gap-1">
                        <p className="mb-[0.2rem] text-[.8125rem] font-bold text-white-400">
                          {chat?.user?.username}:
                        </p>
                      </div>
                      <span className="text-[.8125rem] text-white-400">
                        {formatTo12Hour(chat?.createdAt)}
                      </span>
                    </div>
                    <p className="text-[.8125rem]">
                      {isValidURL(chat?.message) ? (
                        <Image
                          src={chat?.message}
                          width={10000}
                          height={10000}
                          className="w-32 rounded-lg rounded-tl-none  px-3 py-2 text-sm font-normal text-white-1000"
                          alt="GIF"
                        />
                      ) : (
                        <span className="relative top-[-2px] rounded-lg rounded-tl-none  pl-1 text-sm font-normal text-white-1000">
                          {chat?.message}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="mb-3 flex justify-end" key={chat?.id}>
                <div className="text-white relative max-w-[35.8125rem] rounded-[.25rem] bg-secondaryBtnBg px-[0.625rem] py-3">
                  <p className="break-words pr-12 text-sm leading-snug">
                    {isValidURL(chat?.message) ? (
                      <Image
                        src={chat?.message}
                        width={10000}
                        height={10000}
                        className="w-32 rounded-lg rounded-tl-none  px-3 py-2 text-sm font-normal text-white-1000"
                        alt="GIF"
                      />
                    ) : (
                      <span className="relative top-[-2px] rounded-lg rounded-tl-none  pl-1 text-sm font-normal text-white-1000">
                        {chat?.message}
                      </span>
                    )}
                  </p>

                  <span className="absolute bottom-1 right-2 text-[0.625rem] text-steelTeal-500">
                    {formatTo12Hour(chat?.createdAt)}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="relative flex min-h-0 shrink grow basis-[0%] flex-col">
        {showEmojiPicker && (
          <div className="absolute bottom-[5.5rem]  left-1/2 z-50 -translate-x-1/2  p-2">
            <div style={{ height: 300, overflowY: 'scroll' }}>
              <div ref={pickerRef}>
                <Picker
                  autoFocus={false}
                  data={data}
                  onEmojiSelect={(emoji) => {
                    // setShowEmojiPicker(false);
                    if (gifMessage) {
                      setGifMessage(null);
                    }
                    setMessage(message + emoji.native);
                  }}
                  theme="dark"
                />
              </div>
            </div>
          </div>
        )}

        {showGifPicker && (
          <div className="absolute bottom-[5.5rem]  left-1/2 z-50   -translate-x-1/2  p-2">
            <div style={{ height: 300, overflowY: 'scroll' }}>
              <div ref={pickerRef}>
                <Grid
                  fetchGifs={fetchGifs}
                  width={300}
                  className="mx-auto"
                  columns={2}
                  gutter={6}
                  onGifClick={handleGifSelect}
                />
              </div>
            </div>
          </div>
        )}
        <ChatInput
          message={message}
          setMessage={setMessage}
          gifMessage={gifMessage}
          setGifMessage={setGifMessage}
          showEmojiPicker={showEmojiPicker}
          setShowEmojiPicker={setShowEmojiPicker}
          showGifPicker={showGifPicker}
          showSuggestions={showSuggestions}
          selectedSuggestion={selectedSuggestion}
          handleKeyDown={handleKeyDown}
          selectSuggestion={selectSuggestion}
          handleSendMessage={handleSendMsg}
          handleInputChange={handleInputChange}
          handleGifPickerToggle={handleGifPickerToggle}
          handleEmojiPickerToggle={handleEmojiPickerToggle}
          inputRef={inputRef}
          suggestionsRef={suggestionsRef}
          tagSuggestion={tagSuggestion}
          livePlayersCount={livePlayersCount}
          isAuthenticated={isAuthenticated}
          groupId={groupData?.group?.id}
          section="GroupChat"
          emojiButtonRef={emojiButtonRef}
          gifButtonRef={gifButtonRef}
        />
      </div>
    </div>
  );
}
