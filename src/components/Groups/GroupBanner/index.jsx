'use client';

import React, { useRef, useState } from 'react';
import Image from 'next/image';
import { useQueryClient } from '@tanstack/react-query';
import { Upload, Loader2 } from 'lucide-react';

import { useUpdateGroupBannerMutation } from '@/reactQuery/authQuery';
import BannerLoader from '@/components/BannerLoader';
import useAuthStore from '@/store/useAuthStore';
import groupImage from '../../../../public/assets/demo-image/groupImage.webp';

const ACCEPTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

export default function GroupBanner({ groupData, refetch = () => {} }) {
  const { userDetails, isAuthenticated } = useAuthStore((state) => state);
  const fileInputRef = useRef(null);
  const queryClient = useQueryClient();

  const [isUpdatingBanner, setIsUpdatingBanner] = useState(false);
  const [bannerError, setBannerError] = useState('');

  const isAdmin = userDetails?.id == groupData?.groupAdmin;

  const updateBannerMutation = useUpdateGroupBannerMutation({
    onSuccess: (data) => {

      if (data?.groupBanner) {
        queryClient.setQueryData(
          ['GET_GROUP_DETAIL_QUERY', groupData?.groupName],
          (oldData) =>
            oldData ? { ...oldData, groupBanner: data.groupBanner } : oldData
        );
      } else {
        queryClient.invalidateQueries({
          queryKey: ['GET_GROUP_DETAIL_QUERY', groupData?.groupName],
        });
      }
      setIsUpdatingBanner(false);

      refetch();
    },
    onError: (error) => {
      console.error('Error updating banner:', error);
      setIsUpdatingBanner(false);
    },
  });

  const handleBannerClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    setBannerError('');

    if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      setBannerError('Only JPG, JPEG, PNG, or WEBP images are allowed.');
      return;
    }

    const MAX_SIZE_MB = 1;
    const MIN_SIZE_KB = 100;

    if (file.size < MIN_SIZE_KB * 1024) {
      setBannerError(`File must be at least ${MIN_SIZE_KB}KB (too low quality).`);
      return;
    }

    if (file.size > MAX_SIZE_MB * 1024 * 1024) {
      setBannerError(`File size should be less than ${MAX_SIZE_MB}MB.`);
      return;
    }

    const img = new window.Image();
    img.src = URL.createObjectURL(file);

    img.onload = () => {
      const { width, height } = img;
      const ratio = width / height;

      if (ratio < 3.5 || ratio > 4.5) {
        setBannerError('Banner must be close to 4:1 ratio (e.g., 1584x396).');
        return;
      }

      if (width < 800 || height < 200) {
        setBannerError('Banner is too small. Minimum recommended size is 800x200 px.');
        return;
      }

      setBannerError('');
      setIsUpdatingBanner(true);

      const formData = new FormData();
      formData.append('groupId', groupData?.id);
      formData.append('groupBanner', file);

      updateBannerMutation.mutate(formData);
    };

    img.onerror = () => {
      setBannerError('Invalid image file.');
    };
  };

  const getBannerImage = () => groupData?.groupBanner || groupImage;

  return (
    <>
      <div className="relative mb-4 flex items-center justify-center rounded-[1.25rem] lg:min-h-[200px]">
        <div className="h-full w-full rounded-xl bg-richBlack-610 text-2xl font-bold text-steelTeal-200 max-sm:text-lg">
          <Image
            src={getBannerImage()}
            alt={`${groupData?.groupName} banner`}
            height={400}
            width={1200}
            quality={90}
            priority
            className="aspect-[6/1] h-full w-full max-w-[1080px] object-cover object-center"
          />

          {isAdmin && isAuthenticated && (
            <>
              <button
                onClick={handleBannerClick}
                disabled={isUpdatingBanner}
                className="absolute bottom-8 ml-2 flex items-center gap-2 rounded-lg bg-primary-1000 bg-opacity-70 px-3 py-2 text-sm font-medium text-white-1000 hover:bg-opacity-80 disabled:cursor-not-allowed disabled:opacity-50"
                type="button"
              >
                {isUpdatingBanner ? (
                  <Loader2 size={20} className="animate-spin" />
                ) : (
                  <Upload size={20} />
                )}
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/png, image/jpeg, image/jpg, image/webp"
                onChange={handleFileChange}
                className="hidden"
              />
            </>
          )}
        </div>
      </div>

      {bannerError && (
        <p className="text-xs text-red-500">
          {bannerError}
        </p>
      )}
    </>
  );
}
