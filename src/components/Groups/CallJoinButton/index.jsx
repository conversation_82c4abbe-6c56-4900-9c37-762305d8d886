'use client';
import Auth from '@/components/Auth';
import GroupCall from '@/components/ChatWindow/GroupChat/GroupCall';
import {
  useGetPublicDetailQuery,
  useJoinGroup,
} from '@/reactQuery/chatWindowQuery';
import useAuthModalStore from '@/store/useAuthModalStore';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

const CallJoinButton = ({
  groupData = {},
  userDetails,
  refetch = () => {},
}) => {
  const { group } = groupData;
  const queryClient = useQueryClient();
  const { setSelectedTab } = useAuthTab((state) => state);

  const { openModal} = useAuthModalStore();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const [isGroupCallClicked, setIsGroupCallClicked] = useState(false);
  const voiceCall = useVoiceCallStore((state) => state.voiceCall);
  const router = useRouter();
  const joinGroup = useJoinGroup({
    onSuccess: async (response) => {
      toast.success('Group Joined');
      queryClient.invalidateQueries({
        queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });
      refetch();
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });
  useEffect(() => {
    if (voiceCall?.groupId == group?.id && isGroupCallClicked) {
      router.push('/');
    }
  }, [voiceCall, isGroupCallClicked]);

  if (group)
    return group?.members?.find((user) => user?.userId == userDetails?.id) ? (
      <GroupCall
        groupId={group?.id}
        groupName={group?.groupName}
        setIsGroupCallClicked={setIsGroupCallClicked}
      />
    ) : (
      <button
        disabled={joinGroup?.isPending}
        className="flex items-center justify-center gap-2 rounded-[10px] bg-[#e81c24] px-2 py-2 text-[14px] font-semibold capitalize leading-none text-black-1000 transition duration-200 hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-700"
        onClick={(e) => {
          e.stopPropagation();
          if (!isAuthenticated) {
            setSelectedTab(0);
            localStorage.setItem('activeTab', 0);
            openModal(<Auth />);
            return;
          }
          joinGroup.mutate({
            groupId: group.id,
            action: 'join',
          });
        }}
      >
        Join
      </button>
    );
};

export default CallJoinButton;
