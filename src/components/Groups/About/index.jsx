import ChatAvatar from '@/components/ChatAvatar';
import dynamic from 'next/dynamic';
const UserInfo = dynamic(() => import('@/components/UserInfoModal'), {
  ssr: false,
});
import useModalStore from '@/store/useModalStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import React, { useCallback } from 'react';
import GroupCall from '@/components/ChatWindow/GroupChat/GroupCall';
import useAuthStore from '@/store/useAuthStore';
import CallJoinButton from '../CallJoinButton';

export default function About({ groupData, userDetails,refetch }) {
  const { groupName, groupDescription, createdAt, profile } =
    groupData?.group || {};
  const { openUserInfoModal } = useUserInfoStore();
  const { openModal } = useModalStore();
  const date = new Date(createdAt);
  const day = date.getUTCDate();
  const month = date.toLocaleString('en-US', {
    month: 'long',
    timeZone: 'UTC',
  });
  const year = date.getUTCFullYear();

  const fullDate = `${month} ${day} ${year}`;

  const adminUsernames = groupData?.group?.members?.filter((m) => m?.isAdmin);

  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(<UserInfo />);
    },
    [openUserInfoModal, openModal],
  );

  return (
    <div className="my-4 rounded-[.25rem] bg-erieBlack-200 p-4">
      <div className="mb-[0.625rem] flex items-center gap-3 border-b border-white-370 pb-[0.625rem]">
        <ChatAvatar
          profileImage={profile}
          firstName={groupName}
          lastName=""
          userName={groupName}
          imageClassName="h-10 w-10 rounded-full flex-shrink-0"
          imageWidth={40}
          imageHeight={40}
          avatarSize={40}
        />
        <h4 className="flex-1 text-xl font-medium">{groupName}</h4>
        <CallJoinButton groupData={groupData} userDetails={userDetails} refetch={refetch} />
      </div>
      <div className="py-1.5">
        <h4 className="inline-block pr-2 text-base font-bold text-white-400">
          {'Admins: '}
        </h4>
        <p className="inline-block text-sm">
          {adminUsernames?.map((admin, index) => (
            <span
              key={admin?.userId || index}
              onClick={() => {
                handleOpenUserInfoModal(admin?.userId);
              }}
              className="cursor-pointer"
            >
              {admin?.user?.username + ' '}
            </span>
          ))}
        </p>
      </div>
      <div className="py-1.5">
        <h4 className="inline-block pr-2 text-base font-bold text-white-400">
          {'Number of Members: '}
        </h4>
        <p className="inline-block text-sm">
          {groupData && (groupData?.group?.members?.length || groupData?.members?.length || 0)}
        </p>
      </div>
      <div className="py-1.5">
        <h4 className="inline-block pr-2 text-base font-bold text-white-400">
          {'Created On: '}
        </h4>
        <p className="inline-block text-sm">{groupData && fullDate}</p>
      </div>
      <div className="py-1.5">
        <h4 className="inline-block pr-2 text-base font-bold text-white-400">
          {'Group Description: '}
        </h4>
        <p className="inline-block text-sm">{groupDescription}</p>
      </div>
    </div>
  );
}
