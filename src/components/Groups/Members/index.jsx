'use client';

import React from 'react';
import FriendsAccordion from '@/components/Dashboard/FriendsPage/FriendsAccordian';
import MessageIcon from '@/assets/icons/MessageIcon';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import FriendCall from '@/components/ChatWindow/Friends/FriendCall';
import useAuthStore from '@/store/useAuthStore';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import GroupCall from '@/components/ChatWindow/GroupChat/GroupCall';
import CallJoinButton from '../CallJoinButton';
import useGeneralStore from '@/store/useGeneralStore';
import useAuthTab from '@/store/useAuthTab';
import Auth from '@/components/Auth';
import useModalStore from '@/store/useModalStore';
import useAuthModalStore from '@/store/useAuthModalStore';

export default function Members({ groupData, userDetails,refetch }) {
  const members = groupData?.group?.members || [];

  const sortedMembers = [...members].sort((a, b) => {
    if (a.isAdmin === b.isAdmin) return 0;
    return a.isAdmin ? -1 : 1;
  });

  const { setIsPrivateChatOpen, setUserId, searchUserName, setSearchUserName } =
    usePrivateChatStore((state) => state);
  const { setOpenChat, setActiveMenu } = useGeneralStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal} = useAuthModalStore();
  const { isAuthenticated } = useAuthStore((state) => state);

  return (
    <div className="my-4 rounded-[.25rem] bg-erieBlack-200 p-4">
      <div className="pb-5">
        <div className="mb-[0.625rem] flex justify-between border-b border-white-370 pb-[0.625rem]">
          <h4 className="text-xl font-medium">
            {groupData?.group?.groupName} Members ({members.length})
          </h4>
          <CallJoinButton groupData={groupData} userDetails={userDetails} refetch={refetch} />
        </div>
        <div className="grid grid-cols-3 gap-4 max-lg:grid-cols-2 max-md:grid-cols-2 max-xs:grid-cols-1">
          {sortedMembers.map((member) => (
            <div key={member.id} className="mb-[0.625rem]">
              <FriendsAccordion
                bgColor="bg-erieBlack-100"
                username={member?.user?.username || 'Unknown User'}
                userId = {member?.user?.id}
                profileImage={member?.user?.imageUrl}
                currentStatus={member?.user?.currentStatus}
                gradientClass="bg-transparent"
                currentGamePlay={member?.user?.currentGamePlay}
                isAdmin={member.isAdmin}
                actions={[
                
                  member?.user?.areFriends && (
                    <FriendCall
                      key="call"
                      userId={member?.user?.id}
                      user={{
                        ...member?.user,
                        profileImage: member?.user?.imageUrl,
                      }}
                    />
                  ),
                  userDetails?.id != member?.user?.id && (
                    <button
                      key="msg"
                      onClick={() => {
                        if (!isAuthenticated) {
                          localStorage.setItem('activeTab', 1);
                          setSelectedTab(1);
                          openModal(<Auth/>);
                          return false;
                        }
                        setUserId(member?.user?.id);
                        setIsPrivateChatOpen(true);
                        setOpenChat(true)
                        setActiveMenu('msg');
                        if (searchUserName !== '') {
                          setSearchUserName('');
                        }
                      }}
                      className="cursor-pointer"
                    >
                      <MessageIcon size={16} />
                    </button>
                  ),
                ]}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
