'use client';

import Link from 'next/link';
import Image from 'next/image';
import useGeneralStore from '@/store/useGeneralStore';

import plus18 from '@/assets/images/svg-images/18+.svg';
import BrandLogo from '@/assets/images/logo/brand-logo.svg';
import License from '@/assets/webp/license.webp';

function Footer() {
  const { openChat, openMenu } = useGeneralStore((state) => state);

  const baseClasses =
    'border-t border-white-200    pb-[3.75rem] transition-all duration-300 ease-in-out  xl:pb-0';
  const layoutClasses = `
    ${openChat ? 'xl:mr-[20.5rem] xl:w-[calc(100%-14.75rem-20.5rem)]' : 'xl:mr-0 xl:w-[calc(100%-14.75rem)]'}
    ${openMenu ? 'xl:ml-[3.375rem] xl:w-[calc(100%-23.75rem)]' : 'xl:ml-[14.75rem] xl:w-[calc(100%-14.75rem)]'}
    ${openMenu && openChat ? 'xl:w-[calc(100%-23.75rem)] lg:pl-43' : openMenu ? '!mx-0 xl:w-full ' : ''}
  `.trim();
  const paddingClasses = `
    
     ${openChat ? 'pl-[5rem] xl:pl-4  max-md:px-2' : ''}
    ${openMenu ? '' : 'pl-[5rem] xl:pl-4 max-md:px-2'}
    ${openMenu && openChat ? ' pl-[5rem] xl:pl-4  max-md:px-2' : openMenu ? 'pl-[5rem] xl:pl-4  max-md:px-2' : '  max-md:px-2'}
  `;

  const platformLinks = [
    'Support',
    'FAQ',
    'Partnership program',
    'Help Center',
  ];
  const aboutLinks = [
    'AML Policy',
    'Sports Policy',
    'Responsible Gaming',
    'Privacy Policy',
    'Terms & Conditions',
  ];
  const communityLinks = ['X (Twitter)', 'Discord', 'Instagram', 'Facebook'];

  return (
    <footer className={`${baseClasses} ${layoutClasses} ${paddingClasses}`}>
      <div className="mx-auto w-full max-w-containerWidth">
        {/* Top grid */}
        <div className="pb-8 pt-[21px] lg:pt-10 max-sm:pb-1">
          <div className="grid grid-cols-2 gap-5 sm:grid-cols-4 md:grid-cols-6 max-sm:gap-8">
            {/* Brand Logo */}
            <div className="order-1 flex items-start md:col-span-1 max-sm:col-span-2">
              <Image
                src={BrandLogo}
                width={10000}
                height={10000}
                className="w-full max-w-[9.75rem] md:block"
                alt="Brand Logo"
                priority
              />
            </div>

            {/* Platform */}
            <div className="text-white order-2 col-span-1">
              <h6 className="mb-3 text-sm font-semibold">PLATFORM</h6>
              <div className="flex flex-col gap-2 font-medium">
                {platformLinks.map((link) => (
                  <Link
                    key={link}
                    href="#"
                    className="w-fit text-sm text-steelTeal-200 hover:text-white-1000"
                  >
                    {link}
                  </Link>
                ))}
              </div>
            </div>

            {/* About Us */}
            <div className="order-3 col-span-1">
              <h6 className="mb-3 text-sm font-semibold">ABOUT US</h6>
              <div className="flex flex-col gap-2 font-medium">
                {aboutLinks.map((link) => (
                  <Link
                    key={link}
                    href="#"
                    className="w-fit text-sm text-steelTeal-200 hover:text-white-1000"
                  >
                    {link}
                  </Link>
                ))}
              </div>
            </div>

            {/* Community */}
            <div className="order-4 col-span-2 xs:col-span-1">
              <h6 className="mb-4 hidden text-sm font-semibold xs:block">
                COMMUNITY
              </h6>
              <div className="flex flex-col justify-between gap-3 font-medium max-sm:flex-row">
                {communityLinks.map((link) => (
                  <Link
                    key={link}
                    href="#"
                    className="text-xs text-steelTeal-200 hover:text-white-1000"
                  >
                    {link}
                  </Link>
                ))}
              </div>
            </div>

            {/* License */}
            <div className="order-5 flex items-start justify-start xs:order-6 max-md:col-span-2 max-sm:gap-[1.5rem]">
              <Image
                src={License}
                width={10000}
                height={1000}
                className="w-full max-w-[3.25rem] md:block"
                alt="License"
                priority
              />
              <Image
                src={plus18}
                width={10000}
                height={10000}
                className="hidden h-10 w-[3.3125rem] max-w-full md:h-12 max-sm:block"
                alt="18+"
              />
            </div>

            {/* 18+ */}
            <div className="order-6 -ml-2 flex justify-start xs:order-5 md:justify-center max-md:col-span-2">
              <Image
                src={plus18}
                width={10000}
                height={10000}
                className="h-10 w-[5.125rem] max-w-full md:h-12 max-sm:hidden"
                alt="18+"
              />
            </div>
          </div>
        </div>

        {/* Bottom text */}
        <div className="flex items-center justify-start gap-8 py-4">
          <p className="text-sm font-medium text-white-400">
            © 2025 Fans Interactive Global Limited. All rights reserved.
            FansBets is owned and operated by Fans Interactive Global Limited,
            registration number: 15847, registered address: Hamchako, Mutsamudu,
            Autonomous Island of Anjouan, Union of Comoros. Payments may be
            processed by payment agent company Fans Interactive Processing
            Limited with address: Avlonos, 1 Maria House 1075, Nicosia, Cyprus
            and registration number: HE 469025. Our technology — including but
            not limited to software, systems, methods, processes, and related
            innovations — is protected by pending patent applications in the
            United States and internationally, including U.S. patent application
            no. 18/758,178 (filed May 19, 2025) and international PCT patent
            application no. PCT/US2025/030076 (filed May 19, 2025), both
            entitled: Online social wager-based gaming system featuring dynamic
            cross-provider game filtering, persistent cross-provider
            voice-interactive group play, automated multi-seat group game
            reservation, and distributed ledger bet verification. Priority date:
            May 20, 2024 (based on provisional application filing). For any
            support or issues, contact us through our customer support chat
            on-<NAME_EMAIL>.
          </p>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
