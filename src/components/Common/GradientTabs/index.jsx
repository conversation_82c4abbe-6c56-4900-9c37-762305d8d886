'use client';

import Auth from '@/components/Auth';
import useAuthModalStore from '@/store/useAuthModalStore';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import { AnimatePresence, motion } from 'framer-motion';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

function GradientTabs({
  tabs,
  classes = 'justify-center',
  setSelectedTab,
  authTabs = [],
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(
    pathname?.includes('friends') ? 0 : 1,
  );
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  
  const { openModal} = useAuthModalStore();

  const handleTabChange = ({ index, path }) => {
    if (!isAuthenticated) {
      authTabs.includes(index);
      openModal(<Auth />);
      return;
    }

    if (path) {
      router.push(path);
    }
    setActiveTab(index);
    // router.push('');
    if (setSelectedTab) setSelectedTab(index);
  };
  useEffect(() => {
    if (pathname?.includes('dashboard')) {
      setActiveTab(pathname?.includes('friends') ? 0 : 1);
    }
  }, [pathname]);

  return (
    <>
      {/* Tabs Header */}
      <div className={`mt-0 flex justify-center md:justify-start`}>
        <ul className={`relative flex w-full items-center  ${classes}`}>
          {tabs?.map((tab, idx) => (
            <li key={tab.label} className="z-[1]">
              <button
                type="button"
                aria-current="page"
                onClick={() => handleTabChange({ index: idx, path: tab?.path })}
                className={`
                  relative inline-block w-full min-w-[11.25rem] border
                  px-5 py-2.5 text-center text-[.9375rem] font-bold

                  ${idx === 0 ? 'rounded-bl-[0.625rem] rounded-tl-[0.625rem]' : ''}   
                  ${idx === 1 ? 'rounded-br-[0.625rem] rounded-tr-[0.625rem]' : ''} 
                  ${idx === tabs.length - 1 ? 'rounded-r-xl' : ''}    

                  ${
                    activeTab === idx
                      ? 'border-gameCardBorder bg-gradientIcon text-erieBlack-300'
                      : 'border-primaryBorder text-white-1000'
                  }
                `}
              >
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </div>

      {/* Tabs Content with Fade Effect */}
      <div className="tabsContent mx-auto !mt-4 w-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab} // ensures re-animation on tab switch
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            {tabs[activeTab]?.content}
          </motion.div>
        </AnimatePresence>
      </div>
    </>
  );
}

export default GradientTabs;
