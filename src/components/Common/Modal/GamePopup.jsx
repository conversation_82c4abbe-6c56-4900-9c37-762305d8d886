import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import IconButton from '@/components/Common/Button/IconButton';
import { X } from 'lucide-react';
import Auth from '@/components/Auth';
import Image from 'next/image';
import PrimaryButtonOutline from '../Button/PrimaryButtonOutline';
import PrimaryButton from '../Button/PrimaryButton';
import { getTextValue } from '@/utils/helper';
import defaultImage from '../../../assets/images/demo-image/card-image.png';
import useAuthModalStore from '@/store/useAuthModalStore';

const MOBILE_BUTTON_CLASSES = 'max-md:min-h-8 max-sm:text-xs !w-fit';

const GamePopup = ({ gameDetails, onClose, demoAvailable }) => {
  const { openModal} = useAuthModalStore();
  const { setSelectedTab } = useAuthTab();

  const handleAuth = (tab) => {
    setSelectedTab(tab);
    localStorage.setItem('activeTab', tab);
    openModal(<Auth />);
  };

  return (
    <div className="absolute z-10 h-full w-full bg-richBlack-300">
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-xl bg-maastrichtBlue-1000 p-6 shadow-lg md:min-w-[32.5rem] md:rounded-lg max-sm:w-full max-sm:p-2.5">
        <div className="absolute right-3 top-3">
          <IconButton onClick={onClose} className="h-6 w-6 min-w-6">
            <X className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
          </IconButton>
        </div>

        <div className="flex items-center gap-5">
          {/* <CardSkeleton className="min-h-[11.5rem]  min-w-[8.75rem] max-w-[8.75rem]" /> */}
          <div className="min-w-[9rem]  max-w-[9rem]">
            <div className="relative min-w-[7.5rem]  max-w-[8.75rem] overflow-hidden rounded-2xl">
              <Image
                src={gameDetails?.gameThumbnail || defaultImage}
                alt={getTextValue(gameDetails?.gameName) || 'Game'}
                width={1000}
                height={1000}
                className="aspect-[130/148] h-full w-full md:aspect-[100/132]"
              />
            </div>
          </div>
          <div className="flex flex-col items-start gap-4">
            <h4 className="text-xl font-medium uppercase text-white-1000 max-lg:text-sm max-sm:pr-6">
              {getTextValue(gameDetails?.gameName) || 'Game'}
            </h4>

            <button
              className=" z-2
      relative rounded-lg px-4 
      py-2 text-xs font-semibold text-white-1000 
      before:absolute before:inset-0.5 
      before:rounded-lg 
      before:border-2 
      before:border-transparent before:bg-reverseGradientBg before:content-[''] before:[-webkit-mask:linear-gradient(#fff_0_0)_padding-box,_linear-gradient(#fff_0_0)] before:[background-clip:border-box]
      before:[mask-composite:exclude] 
    "
            >
              <span className="text-gradient relative z-10 block">
                {getTextValue(gameDetails?.providerName)}
              </span>
            </button>
            <div className="flex items-center justify-end gap-2">
              <PrimaryButton
                className={MOBILE_BUTTON_CLASSES}
                onClick={() => handleAuth(0)}
                variant="secondary"
              >
                Register
              </PrimaryButton>
              {demoAvailable && (
                <PrimaryButtonOutline
                  className={MOBILE_BUTTON_CLASSES}
                  onClick={onClose}
                >
                  Play Demo
                </PrimaryButtonOutline>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default GamePopup;
