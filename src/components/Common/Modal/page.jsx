import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useModalStore from '@/store/useModalStore';

function Modal() {
  const { components, closeModal } = useModalStore();
  console.log("Component", components)
  const [closingMap, setClosingMap] = useState({}); // Track which modal is closing

  const handleClose = (key) => {
    setClosingMap((prev) => ({ ...prev, [key]: true }));
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 0.5, transition: { duration: 0.2, ease: 'easeOut' } },
    exit: { opacity: 0, transition: { duration: 0.3, ease: 'easeIn' } },
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.85 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.15, ease: 'easeOut' } },
    exit: { opacity: 0, scale: 0.85, transition: { duration: 0.15, ease: 'easeIn' } },
  };

  return (
    <AnimatePresence>
      {components.map((Component, i) => (
        <motion.div
          key={i}
          className="fixed inset-0 z-50 flex h-full w-full items-center justify-center aditya"
        >
          <motion.div
            variants={backdropVariants}
            initial="hidden"
            animate={closingMap[i] ? 'exit' : 'visible'}
            style={
              Component.props?.isEmpty
                ? {} // no style if isEmpty is true
                : { backgroundColor: 'rgb(0 0 0 / 68%)' } // otherwise apply
            }
            exit="exit"
            className="absolute inset-0 bg-black !opacity-100"
            onClick={() => handleClose(i)}
          />

          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate={closingMap[i] ? 'exit' : 'visible'}
            exit="exit"
            onAnimationComplete={() => {
              if (closingMap[i]) {
                closeModal();
                setClosingMap((prev) => {
                  const copy = { ...prev };
                  delete copy[i];
                  return copy;
                });
              }
            }}
            className="bg-white relative z-10 w-full max-w-[600px] rounded-lg p-6 shadow-lg"
            style={{ minHeight: '200px' }}
          >
            {Component}
          </motion.div>
        </motion.div>
      ))}
    </AnimatePresence>
  );
}

export default Modal;
