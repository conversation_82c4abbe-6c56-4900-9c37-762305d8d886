'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Button/IconButton';
import { X, ArrowLeft } from 'lucide-react';
import PrimaryButton from '../Button/PrimaryButton';

function GroupProfileModal({ banners, onSelect }) {
  const { closeModal } = useModalStore();
  const [selectedBanner, setSelectedBanner] = useState(null);

  const handleSelectClick = () => {
    if (selectedBanner) {
      onSelect(selectedBanner);
      closeModal();
    }
  };

  return (
    <div className="bg-black/60 fixed inset-0 z-50 flex items-center justify-center">
      <div className="relative w-full max-w-2xl rounded-lg bg-maastrichtBlue-1000 p-6 shadow-lg">
        {/* Close button (absolute, top-right) */}
        <div className="absolute right-3 top-3">
          <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
            <X className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
          </IconButton>
        </div>

        {/* Heading with Back button */}
        <div className="text-white mb-4 flex items-center gap-2">
          <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
            <ArrowLeft className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
          </IconButton>
          <h3 className="text-[.9375rem] font-semibold">Select Group Photo</h3>
        </div>

        {/* Banner grid with border */}
        <div className="mx-0 rounded-lg border border-gray-700 p-6 lg:mx-[4.75rem]">
          <div className="grid grid-cols-5 place-items-center gap-3">
            {banners.map((banner, idx) => (
              <div
                key={idx}
                className={`flex h-[2.875rem] w-[2.875rem] min-w-[2.875rem] cursor-pointer items-center justify-center overflow-hidden rounded-full border lg:h-[4.5rem] lg:w-[4.5rem] lg:min-w-[4.5rem] 
                  ${selectedBanner === banner ? 'border-2 border-yellow-400 shadow-[0_0_10px_rgba(255,215,0,0.6)]' : 'border-gray-700 hover:ring-2 hover:ring-steelTeal-500'}
                `}
                onClick={() => setSelectedBanner(banner)}
              >
                <Image
                  src={banner}
                  alt={`Banner ${idx + 1}`}
                  width={80}
                  height={80}
                  className="h-full w-full rounded-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Select button below border */}
        <div className="mt-6 flex justify-center">
          <PrimaryButton
            onClick={handleSelectClick}
            disabled={!selectedBanner}
            variant="secondary"
            className={`!w-auto font-semibold ${
              selectedBanner
                ? 'bg-TintGoldGradient '
                : 'cursor-not-allowed bg-gray-600 text-gray-300'
            }`}
          >
            Select Image
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
}

export default GroupProfileModal;
