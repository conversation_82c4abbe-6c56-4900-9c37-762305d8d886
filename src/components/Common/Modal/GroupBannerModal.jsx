'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Button/IconButton';
import { X, ArrowLeft } from 'lucide-react';

function GroupBannerModal({
  casinoBanners = [],
  sportsBanners = [],
  genericBanners = [],
  onSelect,
}) {
  const { closeModal } = useModalStore();
  const [selectedBanner, setSelectedBanner] = useState(null);
  const [activeTab, setActiveTab] = useState('casino'); // default tab

  const handleSelectClick = () => {
    if (selectedBanner) {
      onSelect(selectedBanner);
      closeModal();
    }
  };

  // Map tabs to banner lists
  const tabConfig = {
    casino: casinoBanners,
    sports: sportsBanners,
    generic: genericBanners,
  };

  return (
    <div className="bg-black/60 fixed inset-0 z-50 flex items-center justify-center">
      <div className="relative w-full max-w-2xl rounded-lg bg-maastrichtBlue-1000 p-6 shadow-lg">
        {/* Close button */}
        <div className="absolute right-3 top-3">
          <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
            <X className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
          </IconButton>
        </div>

        {/* Heading */}
        <div className="text-white mb-4 flex items-center gap-2">
          <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
            <ArrowLeft className="hover:text-white h-5 w-5 text-gray-400 transition-all duration-300" />
          </IconButton>
          <h3 className="text-[.9375rem] font-semibold">Select Group Photo</h3>
        </div>

        {/* Tabs + Grid */}
        <div className="rounded-lg border border-gray-700 p-4 md:mx-6">
          {/* Tabs */}
          <div className="mb-4 flex justify-start gap-2.5">
            {['casino', 'sports', 'generic'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`rounded-lg  text-sm font-medium capitalize transition ${
                  activeTab === tab
                    ? 'bg-transparent bg-TintGoldGradient bg-clip-text text-transparent'
                    : 'bg-transparent text-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Banner Grid */}

          <div className="theme-scroll grid max-h-80 place-items-center  gap-2.5 overflow-y-auto  pr-2 md:grid-cols-2">
            {tabConfig[activeTab].map((banner, idx) => (
              <div
                key={idx}
                className={`relative h-[4.8125rem] w-full  cursor-pointer overflow-hidden border  
              ${
                selectedBanner === banner
                  ? 'border border-yellow-400 shadow-[0_0_10px_rgba(255,215,0,0.6)]'
                  : ' border-transparent  hover:border-steelTeal-500'
              }
            `}
                style={{ aspectRatio: '16/9' }} // <-- keeps consistent rectangle shape
                onClick={() => setSelectedBanner(banner)}
              >
                <Image
                  src={banner}
                  alt={`${activeTab} Banner ${idx + 1}`}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Select button */}
        <div className="mt-6 flex justify-center">
          <button
            onClick={handleSelectClick}
            disabled={!selectedBanner}
            className={`rounded-[10px] px-4 py-2 text-[15px] font-semibold ${
              selectedBanner
                ? 'bg-TintGoldGradient text-black-1000'
                : 'cursor-not-allowed bg-gray-600 text-gray-300'
            }`}
          >
            Select Image
          </button>
        </div>
      </div>
    </div>
  );
}

export default GroupBannerModal;
