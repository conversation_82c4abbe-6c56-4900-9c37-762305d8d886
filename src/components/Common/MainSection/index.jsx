'use client';

import { usePathname } from 'next/navigation';
import useGeneralStore from '@/store/useGeneralStore';
import useTheatreStore from '@/store/useThreatreStore';

export default function MainSection({ children }) {
  const { openChat, openMenu } = useGeneralStore((state) => state);
  const pathname = usePathname();
  const { theatre, toggleTheatre } = useTheatreStore();
  const isCasinoGamePage = pathname.startsWith('/casino/games/');

  const baseClasses =
    'blurColor relative z-[2] min-h-dvh  bg-vip-Container-bg bg-cover bg-no-repeat';
  const headerPadding =
    'pt-[calc(var(--header-height)+0.5rem)] lg:pt-[calc(var(--header-height)+1rem)]';

  const layoutClasses = `
    ${openChat ? 'xl:mr-[20.5rem] xl:w-[calc(100%-14.75rem-20.5rem)]' : 'xl:mr-0 xl:w-[calc(100%-14.75rem)]'}  
    ${openMenu ? 'xl:ml-[3.6rem] xl:w-[calc(100%-23.75rem)]' : 'xl:ml-[14.75rem] xl:w-[calc(100%-14.75rem)]'}
    ${openMenu && openChat ? 'xl:w-[calc(100%-23.75rem)]' : openMenu ? '!mx-0 xl:w-full max-2xl:pl-[5rem]' : ''}
  `.trim();

  const paddingClasses =
    pathname === '/sports'
      ? 'px-[0.5px] pr-[1.5px]'
      : theatre && isCasinoGamePage
        ? 'px-2 max-md:pl-0 max-xl:pl-[4rem] max-2xl:!pl-[4rem]    md:pr-0'
        : 'px-4 max-xl:pl-[5rem]    max-xl:pr-[1rem] max-md:px-1';
  const maxWidthClass =
    theatre && isCasinoGamePage ? 'max-w-full' : 'max-w-containerWidth';

  return (
    <div
      className={`${baseClasses} ${headerPadding} ${layoutClasses} ${paddingClasses}`}
    >
      <div className={`mx-auto w-full ${maxWidthClass}`}>{children}</div>
    </div>
  );
}
