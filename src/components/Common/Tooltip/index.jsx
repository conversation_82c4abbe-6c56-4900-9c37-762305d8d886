import React from 'react';

function Tooltip({ children, text, position = 'top' }) {
  let positionClasses;
  let arrowClasses;

  switch (position) {
    case 'top':
      positionClasses =
        'bottom-full mb-2 left-[12px] transform -translate-x-1/2 -translate-y-[10px]';
      arrowClasses =
        'absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-1/2 h-2 w-2 rotate-45 bg-gray-200';
      break;
    case 'right':
      positionClasses = 'left-full ml-8 top-1/2 transform -translate-y-1/2'; // vertically centered, right of button

      arrowClasses =
        'absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2 h-2 w-2 rotate-45 bg-gray-200'; // arrow perfectly centered
      break;
    case 'bottom':
      positionClasses = 'top-full mt-2 left-1/2 transform -translate-x-1/2';
      arrowClasses =
        'absolute left-1/2 top-0 transform -translate-x-1/2 -translate-y-1/2 h-2 w-2 rotate-45 bg-gray-200';
      break;
    case 'left':
      positionClasses = 'right-full mr-2 top-1/2 transform -translate-y-1/2';
      arrowClasses =
        'absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2 h-2 w-2 rotate-45 bg-gray-200';
      break;
    default:
      positionClasses = 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
      arrowClasses =
        'absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-1/2 h-2 w-2 rotate-45 bg-gray-200';
      break;
  }

  return (
    <div className="group relative flex items-center">
      {children}
      <div
        className={`absolute z-10 hidden w-max rounded border border-gray-200 bg-gray-200 p-2 text-sm text-gray-900 group-hover:block ${positionClasses}`}
      >
        {text}
        <div className={arrowClasses} />
      </div>
    </div>
  );
}

export default Tooltip;
