'use client';

import React, { useRef } from 'react';
import Image from 'next/image';
import { motion, useAnimation } from 'framer-motion';
import { useQueryClient } from '@tanstack/react-query';
import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import { useUpdateFavoriteMutation } from '@/reactQuery/gamesQuery';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import defaultImage from '@/assets/images/demo-image/card-image.png';
import useAuthStore from '@/store/useAuthStore';
import toast from 'react-hot-toast';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import useAuthModalStore from '@/store/useAuthModalStore';

function GameCard({
  src,
  alt,
  width,
  height,
  onClick,
  gameId,
  isFavorite,
  aspectRatio = 'aspect-[4/3]',
}) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useAuthModalStore();
  const queryClient = useQueryClient();
  const { mutate: updateFavorite } = useUpdateFavoriteMutation({
    onSuccess: (response) => {
      if (response?.data?.success) {
        toast.success(response?.data?.message);
      }

      queryClient.invalidateQueries({ queryKey: ['GET_FAVORITES_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_CUSTOM_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_SUB_CATEGORY_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_SUB_CATEGORY_LOBBY_GAMES'] });
      queryClient.invalidateQueries({ queryKey: ['GET_CATEGORY_GAMES'] });
    },
    onError: (error) => {
      // console.error('Error changing password', error);
    },
  });

  const toggleFav = () => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    updateFavorite({ request: !isFavorite, casinoGameId: gameId });
  };

  const controls = useAnimation();
  const timeoutRef = useRef(null);

  const handleHoverStart = () => {
    timeoutRef.current = setTimeout(() => {
      controls?.start({
        rotate: [-5.24, -4.24, -5.24],
        y: [-14, -12, -14],
        transition: {
          duration: 0.6,
          ease: 'easeInOut',
          repeat: Infinity,
          repeatType: 'loop',
        },
      });
    }, 300);
  };

  const handleHoverEnd = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    controls?.start({
      rotate: 0,
      y: 0,
      transition: { duration: 0.3, ease: 'easeInOut' },
    });
  };

  const handleImageError = (e) => {
    e.target.src = defaultImage.src;
  };

  return (
    <motion.div
      className="relative overflow-hidden rounded-xl"
      whileHover={{
        transition: { duration: 0.3 },
        rotate: -5.24,
        y: -14,
        boxShadow: '0px 10px 40px rgba(255, 0, 0, 1)',
      }}
      onHoverStart={handleHoverStart}
      onHoverEnd={handleHoverEnd}
      whileTap={{ scale: 0.9 }}
      animate={controls}
    >
      <Image
        src={src}
        width={width}
        height={height}
        className={`${aspectRatio} h-full w-full max-w-full hover:cursor-pointer`}
        alt={alt}
        onClick={onClick}
        onError={handleImageError}
        unoptimized
      />
      <button
        type="button"
        onClick={toggleFav}
        className="absolute right-2 top-2 flex h-7 w-7 items-center justify-center rounded-lg bg-cetaceanBlue-1000 transition-all duration-300 active:scale-90"
      >
        {isFavorite ? (
          <HeartFillIcon className="h-4 w-4 fill-primary-1000" />
        ) : (
          <HeartStrokeIcon className="h-4 w-4 fill-steelTeal-1000" />
        )}
      </button>
    </motion.div>
  );
}

export default GameCard;
