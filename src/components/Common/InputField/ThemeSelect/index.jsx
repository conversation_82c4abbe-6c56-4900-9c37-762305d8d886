'use client';

import React from 'react';
import Select from 'react-select';

const ThemeSelect = ({
  label,
  options,
  value,
  onChange,
  placeholder = 'Select option',
  isClearable = true,
  isSearchable = true,
  error,
  color,
  register,
  selectMargin = 'mb-3',
}) => {
  const customStyles = {
    control: (base, state) => ({
      ...base,
      backgroundColor: '#33332e',
      borderColor: error ? '#ef4444' : state.isFocused ? '#cecab4' : '#cecab4',
      borderWidth: '0',
      borderRadius: '.625rem',
      minHeight: '40px',
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: '#33332e',
      borderRadius: '0.5rem',
      zIndex: 50,
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#33332e'
        : state.isFocused
          ? '#000000'
          : 'transparent',
      color: state.isSelected ? '#fff' : '#e2e8f0',
      cursor: 'pointer',
      '&:active': {
        backgroundColor: '#000000',
      },
    }),
    singleValue: (base) => ({
      ...base,
      color: '#fff',
    }),
    placeholder: (base) => ({
      ...base,
      color: '#8c8c8c',
    }),
    dropdownIndicator: (base) => ({
      ...base,
      color: '#94a3b8',
      '&:hover': {
        color: '#0d9488',
      },
    }),
    clearIndicator: (base) => ({
      ...base,
      color: '#94a3b8',
      '&:hover': {
        color: '#ef4444',
      },
    }),
  };

  return (
    <div className={`theme-select-wrap  w-full ${selectMargin}`}>
      {label && (
        <label
          className={`${color ? ' text-white-1000' : 'text-steelTeal-1000'} relative mb-1 flex gap-2 text-sm font-semibold capitalize`}
        >
          {label}
        </label>
      )}

      <Select
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isClearable={isClearable}
        isSearchable={isSearchable}
        styles={customStyles}
        // {...register('groupType')}
        // menuIsOpen={true}
        // menuOption={true}
        classNamePrefix="theme-select"
        components={{ IndicatorSeparator: () => null }}
      />

      {error && (
        <span className="mt-1 block text-xs text-red-500">
          {error.message || error}
        </span>
      )}
    </div>
  );
};

export default ThemeSelect;
