import React, { useState } from 'react';
import EyeCloseIcon from '@/assets/icons/Eye-Close';
import EyeOpenIcon from '@/assets/icons/Eye-Open';

function InputField({
  type,
  name,
  value,
  placeholder,
  onChange,
  error,
  label,
  labelRight, // <-- new prop
  color,
  className = '',
  register = () => {},
  registerName = '',
  startIcon: StartIcon,
  endIcon: EndIcon,
  inputBgColor,
}) {
  const [showPassword, setShowPassword] = useState(false);
  const isPasswordField = type === 'password';

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="mb-1 w-full">
      {label && (
        <div className="relative mb-1 flex items-center justify-between text-sm font-semibold">
          <label htmlFor={name} className={`${color || 'text-steelTeal-1000'}`}>
            {label}
          </label>
          {labelRight && (
            <span className="text-xs font-semibold text-white-400">
              {labelRight}
            </span>
          )}
        </div>
      )}

      <div className="relative flex items-center">
        {StartIcon && (
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            <StartIcon />
          </span>
        )}

        <input
          type={isPasswordField && showPassword ? 'text' : type}
          name={name}
          id={name}
          placeholder={placeholder}
          className={`text-white w-full border placeholder:!text-white-400 ${
            error ? 'border-red-500' : 'border-solid border-richBlack-1000'
          } min-h-10 rounded-md ${inputBgColor || 'bg-richBlack-1000'} ${
            StartIcon ? 'ps-10' : 'ps-3'
          } ${isPasswordField || EndIcon ? 'pr-10' : 'pe-3'} text-[15px] font-semibold focus:border-steelTeal-1000 focus:outline-none ${className}`}
          value={value}
          onChange={onChange}
          autoComplete="off"
          {...(register ? register(registerName) : {})}
        />

        {(isPasswordField || EndIcon) && (
          <span className="absolute right-3 top-1/2 z-10 flex -translate-y-1/2 transform cursor-pointer">
            {isPasswordField ? (
              <button type="button" onClick={togglePasswordVisibility}>
                {showPassword ? <EyeOpenIcon /> : <EyeCloseIcon />}
              </button>
            ) : (
              <EndIcon />
            )}
          </span>
        )}
      </div>

      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
}

export default InputField;
