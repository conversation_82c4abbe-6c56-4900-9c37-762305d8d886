'use client';

import React from 'react';
import Select, { components } from 'react-select';
import Image from 'next/image';

const CurrencySelect = ({
  label,
  options,
  value,
  onChange,
  placeholder = 'Select option',
  isClearable = true,
  isSearchable = true,
  error,
  color,
  selectMargin = 'mb-3',
  showProviderText = false,
  providerLabel = '',
}) => {
  const customStyles = {
    control: (base, state) => ({
      ...base,
      borderColor: error ? '#ef4444' : state.isFocused ? '#cecab4' : '#cecab4',
      borderWidth: '0',
      minWidth: 'max-content',
      width: 'auto',
    }),

    singleValue: (base) => ({
      ...base,
      color: '#fff',
      maxWidth: 'none',
    }),
    // menu: (base) => ({
    //   ...base,
    //   backgroundColor: '#33332e',
    //   borderRadius: '0.5rem',
    //   zIndex: 50,
    //   width: 'max-content',
    //   minWidth: '100%',
    // }),
  };

  // Render icon (SVG component or image path)
  const RenderIcon = ({ icon: Icon, label, size = 20 }) => {
    if (!Icon) return null;

    if (typeof Icon === 'string') {
      return (
        <Image
          src={Icon}
          alt={label}
          width={size}
          height={size}
          className="rounded"
        />
      );
    }

    return <Icon width={size} height={size} />;
  };

  const CustomOption = (props) => {
    const { data } = props;
    return (
      <components.Option {...props}>
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <RenderIcon icon={data.icon} label={data.label} size={20} />
            <span>{data.label}</span>
          </div>
          {data.right && (
            <span className="text-xs text-gray-400">{data.right}</span>
          )}
        </div>
      </components.Option>
    );
  };

  const CustomSingleValue = (props) => {
    const { data, selectProps } = props;
    return (
      <components.SingleValue {...props}>
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            {selectProps.showProviderText && (
              <span className="text-white-400 max-sm:text-xs">
                {selectProps.providerLabel || 'Providers:'}
              </span>
            )}
            <RenderIcon icon={data.icon} label={data.label} size={18} />
            <span className="text-xs font-semibold text-white-1000 lg:text-[.9375rem]">
              {data.label}
            </span>
          </div>
          {data.right && (
            <span className="text-xs font-semibold  text-steelTeal-200 lg:text-[.9375rem]">
              {data.right}
            </span>
          )}
        </div>
      </components.SingleValue>
    );
  };

  return (
    <div className={`theme-select-wrap w-full ${selectMargin}`}>
      {label && (
        <label
          className={`${
            color ? 'text-white-1000' : ''
          } relative mb-1 flex gap-2 text-sm font-semibold capitalize`}
        >
          {label}
        </label>
      )}

      <Select
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isClearable={isClearable}
        isSearchable={isSearchable}
        styles={customStyles}
        // menuIsOpen={true}
        classNamePrefix="theme-select currency-select"
        showProviderText={showProviderText}
        providerLabel={providerLabel}
        components={{
          IndicatorSeparator: () => null,
          Option: CustomOption,
          SingleValue: CustomSingleValue,
        }}
      />

      {error && (
        <span className="mt-1 block text-xs text-red-500">
          {error.message || error}
        </span>
      )}
    </div>
  );
};

export default CurrencySelect;
