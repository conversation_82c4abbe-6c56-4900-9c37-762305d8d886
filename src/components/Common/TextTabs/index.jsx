'use client';

import React, { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import { usePathname } from 'next/navigation';
import useAuthModalStore from '@/store/useAuthModalStore';

export default function TextTabs({ tabs = [], onChange, authTabs = [] }) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState(0);
  const { openModal } = useAuthModalStore();
  const handleTabClick = (index) => {
    if (!isAuthenticated) {
      authTabs.includes(index);
      openModal(<Auth />);
      return;
    }
    setActiveTab(index);
    if (onChange) onChange(index);
  };
  useEffect(() => {
    if (!isAuthenticated && pathname.split('/').includes('dashboard')) {
      setActiveTab(1);
    } else {
      setActiveTab(0);
    }
  }, [isAuthenticated, pathname]);

  return (
    <div>
      {/* Tabs Below */}
      <div className="flex gap-4 max-sm:gap-[0.625rem]">
        {tabs.map((tab, index) => (
          <p
            key={index}
            onClick={() => handleTabClick(index)}
            className={`cursor-pointer py-2 text-sm font-bold transition-colors ${
              activeTab === index
                ? 'bg-textGradient  bg-clip-text text-transparent'
                : 'text-white'
            }`}
          >
            {tab.label}
          </p>
        ))}
      </div>
      {/* Tab Content on Top */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab} // ensures re-animation on tab switch
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          <div className="text-white">{tabs[activeTab]?.content}</div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
