'use client';

import React from 'react';
import CaretDownIcon from '@/assets/icons/Caret-Down';
import useGeneralStore from '@/store/useGeneralStore';

function DropDown({ items, isOpen = false, onToggle }) {
  const { openMenu } = useGeneralStore();

  return (
    <div>
      <button
        type="button"
        onClick={onToggle}
        className={`flex w-full items-center gap-2.5 rounded-[0.625rem] ${openMenu ? 'md:justify-center' : 'justify-start'
          }`}
      >
        {items.title}
        <CaretDownIcon
          className={`${isOpen ? 'rotate-180' : 'rotate-0'} ${!openMenu ? 'block' : 'md:hidden'
            } ml-auto size-6 fill-white-1000 transition-all duration-300`}
        />
      </button>

      <div
        className={`grid overflow-hidden text-sm text-slate-600 transition-all duration-300 ease-in-out ${isOpen
            ? 'grid-rows-[1fr] space-y-2 py-2 opacity-100'
            : 'grid-rows-[0fr] opacity-0'
          }`}
      >
        <div className="max-h-96 overflow-y-auto">
          <ul className="">{items.content}</ul>
        </div>
      </div>
    </div>
  );
}

export default DropDown;
