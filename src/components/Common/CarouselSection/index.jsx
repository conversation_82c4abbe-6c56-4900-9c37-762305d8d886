'use client';

import React, { useMemo, useEffect } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import Image from 'next/image';
import { usePrevNextButtons } from '../Button/EmblaArrowButtons';
import LeftArrowIcon from '@/assets/images/svg-images/left-arrow.svg';
import RightArrowIcon from '@/assets/images/svg-images/right-arrow.svg';
import GridIcon from '@/assets/images/svg-images/grid-icon.svg';
import CategoryPlaceholder from '@/assets/images/stock-images/category-placeholder.webp';

const CarouselSection = ({
  children,
  title = '',
  titleIcon = null,
  showViewAll = false,
  showNavigation = true,
  showGridIcon = false,
  viewAll = 'View All',
  onViewAllClick,
  cardGap = 'gap-0 md:gap-0',
  containerMargin = 'my-6',
  titleClassName = 'text-[14px] font-[600] text-white-1000',
  emblaOptions = { loop: true, align: 'start' },
  customNavigation = null,
  navigationStyle = 'default',
  showCustomControls = false,
  customControls = null,
  leftArrowIcon = LeftArrowIcon,
  rightArrowIcon = RightArrowIcon,
  gridIcon = GridIcon,
  middleSection = null,
  onClickGridIcon = () => { },
  onReachEnd = null,
}) => {
  const [emblaRef, emblaApi] = useEmblaCarousel(emblaOptions);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick: rawPrev,
    onNextButtonClick: rawNext,
  } = usePrevNextButtons(emblaApi);

  const onPrevButtonClick = useMemo(() => () => rawPrev(), [rawPrev]);
  const onNextButtonClick = useMemo(() => () => rawNext(), [rawNext]);

  useEffect(() => {
    if (!emblaApi || !onReachEnd) return;

    const checkEnd = () => {
      if (!emblaApi.canScrollNext()) onReachEnd();
    };

    emblaApi.on('scroll', checkEnd);
    emblaApi.on('resize', checkEnd);

    return () => {
      emblaApi.off('scroll', checkEnd);
      emblaApi.off('resize', checkEnd);
    };
  }, [emblaApi, onReachEnd]);

  const isViewAllImage = (viewAll) => {
    if (typeof viewAll !== 'string') return false;
    return (
      /\.(jpg|jpeg|png|gif|svg|webp)$/i.test(viewAll) ||
      viewAll.includes('/') ||
      viewAll.includes('.')
    );
  };

  const handleViewAllClick = () => {
    if (onViewAllClick) onViewAllClick();
  };

  const renderViewAllContent = () => {
    if (isViewAllImage(viewAll)) {
      return (
        <Image
          src={viewAll}
          className="h-[14px] w-[14px] object-cover"
          width={100}
          height={100}
          alt="view all"
        />
      );
    }
    return viewAll;
  };

  const NavigationButton = React.memo(
    ({ direction, disabled, onClick, icon, className = '' }) => (
      <div
        className={`flex h-[28px] w-[28px] items-center justify-center rounded-lg bg-primaryBorder ${disabled
          ? 'cursor-not-allowed opacity-50'
          : 'hover:bg-primaryBorder/80 cursor-pointer transition-all duration-200'
          } ${className}`}
        onClick={disabled ? undefined : onClick}
      >
        <Image
          src={icon}
          className="h-[14px] w-[14px] rounded-full object-cover"
          width={100}
          height={100}
          alt={`${direction} arrow`}
        />
      </div>
    ),
    (prev, next) =>
      prev.disabled === next.disabled &&
      prev.icon === next.icon &&
      prev.direction === next.direction &&
      prev.onClick === next.onClick,
  );

  const memoizedPrevButton = useMemo(
    () => (
      <NavigationButton
        direction="prev"
        disabled={prevBtnDisabled}
        onClick={onPrevButtonClick}
        icon={leftArrowIcon}
      />
    ),
    [prevBtnDisabled, onPrevButtonClick, leftArrowIcon],
  );

  const memoizedNextButton = useMemo(
    () => (
      <NavigationButton
        direction="next"
        disabled={nextBtnDisabled}
        onClick={onNextButtonClick}
        icon={rightArrowIcon}
      />
    ),
    [nextBtnDisabled, onNextButtonClick, rightArrowIcon],
  );

  const renderNavigation = () => {
    if (customNavigation) return customNavigation;

    return (
      <div className="flex gap-2">
        {showViewAll && navigationStyle === 'default' && (
          <div
            className="flex h-[28px] cursor-pointer items-center justify-center rounded-lg bg-primaryBorder px-2 py-2"
            onClick={handleViewAllClick}
          >
            {isViewAllImage(viewAll) ? (
              renderViewAllContent()
            ) : (
              <p className="text-xs font-semibold">{renderViewAllContent()}</p>
            )}
          </div>
        )}

        {showGridIcon && navigationStyle === 'groups' && (
          <div
            className="flex h-[28px] w-[28px] cursor-pointer items-center justify-center rounded-lg bg-primaryBorder"
            onClick={() => onClickGridIcon()}
          >
            <Image
              src={gridIcon}
              className="h-[14px] w-[14px] object-cover"
              width={100}
              height={100}
              alt="grid"
            />
          </div>
        )}

        {showNavigation && (
          <>
            {memoizedPrevButton}
            {memoizedNextButton}
          </>
        )}
      </div>
    );
  };

  const iconSrc = useMemo(() => {
    if (!titleIcon) return CategoryPlaceholder;
    if (typeof titleIcon === 'string') return titleIcon;
    if (titleIcon?.src) return titleIcon.src;
    return CategoryPlaceholder;
  }, [titleIcon]);

  return (
    <>
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {title && (
            <div className="flex items-center gap-2">
              {titleIcon && (
                <Image
                  src={iconSrc}
                  alt="Title Icon"
                  className="h-5 w-5 object-cover"
                  width={20}
                  height={20}
                  priority 
                  onError={(e) => {
                    e.currentTarget.src = CategoryPlaceholder.src; // fallback on error
                  }}
                />
              )}
              <p className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent">
                {title}
              </p>
            </div>
          )}
          {showCustomControls && customControls}
        </div>

        {/* Navigation */}
        {renderNavigation()}
      </div>

      {/* Middle Section */}
      {middleSection && middleSection}

      {/* Carousel */}
      <div
        className={`-ml-3 overflow-hidden max-sm:ml-[-.375rem] ${containerMargin}`}
        ref={emblaRef}
      >
        <div className={`flex  ${cardGap}`}>{children}</div>
      </div>

      {/* View All for group style */}
      {navigationStyle === 'groups' &&
        showViewAll &&
        !isViewAllImage(viewAll) && (
          <div className="flex items-center justify-start gap-2">
            <p
              className="hover:text-decoration-underline mt-2 flex cursor-pointer items-center gap-1 ps-1 text-[12px] font-normal text-slateGray-900"
              onClick={handleViewAllClick}
            >
              {viewAll}
            </p>
          </div>
        )}
    </>
  );
};

export default CarouselSection;
