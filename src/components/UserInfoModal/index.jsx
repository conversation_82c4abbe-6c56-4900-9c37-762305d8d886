/* eslint-disable no-nested-ternary */
'use client';

import React, { useState, useEffect } from 'react';
// import Image from 'next/image';
import toast from 'react-hot-toast';
import { X } from 'lucide-react';
import useUserActions from '@/hooks/useUserActions';
import HeartFillIcon from '@/assets/icons/Heart-Fill';
import HeartStrokeIcon from '@/assets/icons/Heart-Stroke';
import useUserInfoStore from '@/store/useUserInfoStore';
import useUserDetails from '@/hooks/useUserDetails';
// import coinAC from '@/assets/images/stock-images/coin-ac.png';
import ProfileIcon from '@/assets/icons/Profile';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useAuthStore from '@/store/useAuthStore';
import {
  useCancelFriendsRequest,
  useCreateFriendsRequest,
  useUnFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import useModalStore from '@/store/useModalStore';
import { usePathname, useRouter } from 'next/navigation';
import defaultImage from '@/assets/icons/profile-icon.svg';
import IconButton from '../Common/Button/IconButton';
import CustomImage from '../Common/CustomImage';
import Tooltip from '../Common/Tooltip';
import StoreModal from '../Store';
import ChatAvatar from '../ChatAvatar';
import PlayCard from '../PlayCard';
import useGeneralStore from '@/store/useGeneralStore';
import { slugify, userStatusColor } from '@/utils/helper';
import { useQueryClient } from '@tanstack/react-query';
import MessageSquareShare from '@/assets/icons/MessageSquareShare';
import Eye from '@/assets/icons/Eye';
import UserRoundPlus from '@/assets/icons/UserRoundPlus';
import UserRoundMinus from '@/assets/icons/UserRoundMinus';
import EyeOff from '@/assets/icons/EyeOff';
import FriendRequestPopup from '../FriendRequestPopup';
import useIsMobile from '@/hooks/useIsMobile';

function UserInfo({
  setUsername = () => { },
  setPrivateChatUserDetails = () => { },
}) {
  const pathname = usePathname();
  const router = useRouter();
  const checkIfGamePath = () => {
    if (pathname.startsWith('/game') || pathname.startsWith('/crash-game')) {
      toast.error('Can not access store while playing game');
      return true;
    }
    return false;
  };

  const { openModal, closeModal } = useModalStore((state) => state);
  const [showFriendPopup, setShowFriendPopup] = useState(false);
  const isMobile = useIsMobile();

  // const [currency, setCurrency] = useState('AC');
  const {
    // isUserInfoModalOpen,
    selectedUserId,
    closeUserInfoModal,
    userDetails,
    loading,
    setUserDetails,
  } = useUserInfoStore();
  const { setIsPrivateChatOpen, setUserId } = usePrivateChatStore(
    (state) => state,
  );

  const { refetch, data: privateChatDetails } = useUserDetails();
  const userId = useAuthStore((state) => state?.userDetails?.id);
  const { ignoreUser, likeUser, unignoreUser, unlikeUser } = useUserActions({
    refetch,
  });
  const {
    setActiveMenu,
    setOpenChat,
    setOpenLobby,
    activePreviousState,
  } = useGeneralStore();
  const { isAuthenticated } = useAuthStore((state) => state);
  const queryClient = useQueryClient();

  const [isLiked, setIsLiked] = useState(false);
  const [isIgnored, setIsIgnored] = useState(false);

  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      userDetails.friendRequestStatus = 'pending';
      setUserDetails(userDetails);
      refetch();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const mutationUnFriendRequest = useUnFriendsRequest({
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['GET_FRIEND_LIST_QUERY'] });
      queryClient.invalidateQueries({ queryKey: ['GET_INCOMING_MESSAGE_LIST'] });
      queryClient.invalidateQueries({ queryKey: ['GET_RECENT_CHAT_QUERY'] });
      toast.success(response?.data?.message);
      refetch();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const mutationCancelFriendRequest = useCancelFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetch();
      queryClient.invalidateQueries({ queryKey: ['GET_USER_LIST_QUERY'] });
      queryClient.invalidateQueries({ queryKey: ['GET_INCOMING_MESSAGE_LIST'] });
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetch();
    },
  });

  const unFriend = (unfriendUserId) => {
    mutationUnFriendRequest.mutate({ unfriendUserId });
  };

  const cancelFriend = (cancelFriendUserId) => {
    mutationCancelFriendRequest.mutate({ actioneeId: cancelFriendUserId });
  };

  useEffect(() => {
    if (userDetails) {
      setIsLiked(userDetails.liked);
      setIsIgnored(userDetails.ignored);
    }
  }, [userDetails]);

  useEffect(() => {
    if (privateChatDetails) {
      setPrivateChatUserDetails(privateChatDetails);
    }
  }, [privateChatDetails]);

  const handleLike = () => {
    if (isLiked) {
      unlikeUser(selectedUserId);
    } else {
      likeUser(selectedUserId);
    }
    setIsLiked(!isLiked);
  };

  const handleIgnore = () => {
    if (isIgnored) {
      unignoreUser(selectedUserId);
    } else {
      ignoreUser(selectedUserId);
    }
    setIsIgnored(!isIgnored);
  };

  const handleOpenChat = () => {
    console.log('🚀 ~ openChat ~ userDetails?.userId:', userDetails);
    setUserId(userDetails?.id);
    setIsPrivateChatOpen(true);
    setOpenChat(true);
    closeUserInfoModal();
    setUsername('');
    closeModal();
  };

  const handleOpenTip = (userDetails) => {
    openModal(<StoreModal userDetails={userDetails} currentActiveTab="tips" />);
  };
  const handleGameClick = (provider, name) => {
    setOpenLobby(false);
    closeModal();
    router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`);

    // router.push(`/game/${gameId}`);
  };

  const groupUrlHref = () => {
    closeModal();
    router.push(`/user/${userDetails.username}`);
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-[894px] px-2 py-[10px] md:p-4 ">
        <div className="min-h-[32.6875rem] rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <ProfileIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                User Info
              </h3>
            </div>

            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          {loading ? (
            <div className="text-white p-4">Loading...</div>
          ) : (
            <div className=" px-2 py-[10px] pl-1 md:p-4">
              <div className="mb-4 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div className="flex items-center gap-[10px]">
                  <div className="relative h-[86px] w-[86px] min-w-20 rounded-full">
                    {/* <CustomImage
                      src={userDetails?.profileImage || defaultImage}
                      alt="Profile"
                      width={80}
                      height={80}
                      className="mr-4 h-full w-full rounded-full"
                      skeletonWidth={80}
                      skeletonHeight={80}
                    /> */}
                    <ChatAvatar
                      profileImage={userDetails?.imageUrl}
                      userName={userDetails?.username}
                      imageClassName=" h-[86px] w-[86px] rounded-full object-cover"
                      avatarSize={86}
                    />
                    <span
                      className={`absolute bottom-[.375rem] right-[.625rem] h-3 w-3 rounded-full  ${userStatusColor(
                        userDetails?.currentStatus,
                      )} border-white border`}
                    />
                    {/* <span className="absolute -bottom-0.5 -left-1 flex h-7 w-7 items-center justify-center rounded-full border-2 border-oxfordBlue-1000 bg-primary-1000 pt-1 text-center text-xs leading-none">
                      14
                    </span> */}
                  </div>

                  <div className="flex flex-col gap-2">
                    <h5 className="text-white text-lg leading-none">
                      {userDetails?.username}
                    </h5>
                    {/* <h6 className="rounded-full bg-cetaceanBlue-1000 px-2.5 py-1.5 text-sm leading-none text-steelTeal-1000">
                      Level: 2
                    </h6> */}
                  </div>
                </div>

                <div className="flex flex-row items-end justify-between gap-2 md:flex-col md:justify-normal">
                  <button
                    className="w-fit rounded-[50px] border border-golden-600 bg-black-1000 px-[10px] py-[5px] text-sm"
                    onClick={groupUrlHref}
                  >
                    View page
                  </button>
                  {isAuthenticated && (
                    <div>
                      <div className="relative flex items-center gap-3">
                        {userDetails?.id !== userId && (
                          <Tooltip text="Chat" position="top">
                            <div onClick={handleOpenChat} className="cursor-pointer">
                              <MessageSquareShare className="text-steelTeal-1000 hover:text-white-1000" />
                            </div>
                          </Tooltip>
                        )}

                        {userDetails?.id !== userId ? (
                          userDetails?.friendRequestStatus === true ? (
                            <Tooltip text="Un-Friend" position="top">
                              <div className="">
                                <div
                                  onClick={() => setShowFriendPopup(true)}
                                  className="cursor-pointer"
                                >
                                  <UserRoundMinus className="text-steelTeal-1000 hover:text-white-1000" />
                                </div>
                                {showFriendPopup && (
                                  <FriendRequestPopup
                                    text={'Unfriend ?'}
                                    onConfirm={() => {
                                      unFriend(userDetails.id);
                                      setShowFriendPopup(false);
                                    }}
                                    onCancel={() => setShowFriendPopup(false)}
                                  />
                                )}
                              </div>
                            </Tooltip>
                          ) : userDetails?.friendRequestStatus === 'pending' ? (
                            <Tooltip text="Cancel Request" position="top">
                              <div className="">
                                <div
                                  onClick={() => setShowFriendPopup(true)}
                                  className="cursor-pointer"
                                >
                                  <UserRoundMinus className="text-steelTeal-1000 hover:text-white-1000" />
                                </div>
                                {showFriendPopup && (
                                  <FriendRequestPopup
                                    text={'Cancel friend request ?'}
                                    onConfirm={() => {
                                      cancelFriend(userDetails.id);
                                      setShowFriendPopup(false);
                                    }}
                                    onCancel={() => setShowFriendPopup(false)}
                                  />
                                )}
                              </div>
                            </Tooltip>
                          ) : (
                            <Tooltip text="Add-Friend" position="top">
                              <div
                                onClick={() =>
                                  mutationRequest.mutate({
                                    requesteeId: userDetails && userDetails.id,
                                  })
                                }
                                className="cursor-pointer"
                              >
                                <UserRoundPlus className="text-steelTeal-1000 hover:text-white-1000" />
                              </div>
                            </Tooltip>
                          )
                        ) : null}
                        {userDetails?.id !== userId && (
                          <button
                            type="button"
                            onClick={handleIgnore}
                            className=""
                          >
                            {isIgnored ? (
                              <Tooltip text="Unignore-User" position="top">
                                <EyeOff className="text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                              </Tooltip>
                            ) : (
                              <Tooltip text="Ignore-User" position="top">
                                <Eye className="text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                              </Tooltip>
                            )}
                          </button>
                        )}

                        {userDetails?.id !== userId && (
                          <div className="flex items-center justify-center gap-3 rounded-full bg-cetaceanBlue-1000 p-2">
                            <button
                              onClick={handleLike}
                              type="button"
                              className=""
                            >
                              {isLiked ? (
                                <Tooltip text="Unlike" position="top">
                                  <HeartFillIcon className="h-5 w-5 fill-primary-1000 transition-all duration-300" />
                                </Tooltip>
                              ) : (
                                <Tooltip text="Like" position="top">
                                  <HeartStrokeIcon className="h-5 w-5 fill-primary-1000 transition-all duration-300" />
                                </Tooltip>
                              )}
                            </button>
                            <span className="mt-0.5 inline-block text-sm leading-none text-steelTeal-1000">
                              {userDetails?.likesCount}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-4 gap-[10px] max-lg:grid-cols-3 max-md:grid-cols-2">
                {userDetails?.currentGamePlay && (
                  <Gamescard
                    title={'Now Playing'}
                    gameName={
                      userDetails?.currentGamePlay?.name?.EN ||
                      userDetails?.currentGamePlay?.name
                    }
                    gameId={userDetails?.currentGamePlay?.gameId}
                    gameImage={userDetails?.currentGamePlay?.imageUrl}
                    isFavorite={userDetails?.currentGamePlay?.isFavorite}
                    onClick={() => {
                      handleGameClick(
                        userDetails?.currentGamePlay?.providerName,
                        userDetails?.currentGamePlay?.name?.EN ||
                        userDetails?.currentGamePlay?.name,
                      );
                    }}
                    providerName={userDetails?.currentGamePlay?.providerName}
                  />
                )}
                {!userDetails?.currentGamePlay &&
                  userDetails?.recentGamePlay && (
                    <Gamescard
                      title={'Recently Played'}
                      gameName={
                        userDetails?.recentGamePlay?.casinoGame?.name?.EN
                      }
                      gameId={userDetails?.recentGamePlay?.gameId}
                      gameImage={
                        userDetails?.recentGamePlay?.casinoGame?.iconUrl
                      }
                      isFavorite={
                        userDetails?.recentGamePlaycasinoGame?.isFavorite
                      }
                      onClick={() =>
                        handleGameClick(
                          userDetails?.recentGamePlay?.casinoGame
                            ?.casinoProvider?.name,
                          userDetails?.recentGamePlay?.casinoGame?.name,
                        )
                      }
                      providerName={
                        userDetails?.recentGamePlay?.casinoGame?.casinoProvider
                          ?.name
                      }
                    />
                  )}

                {userDetails?.biggestWin?.length > 0 && (
                  <Gamescard
                    title={'Biggest Win'}
                    gameName={
                      userDetails?.biggestWin?.[0]?.name?.EN ||
                      userDetails?.biggestWin?.[0]?.name
                    }
                    gameId={userDetails?.biggestWin?.[0]?.gameId}
                    gameImage={userDetails?.biggestWin?.[0]?.imageUrl}
                    isFavorite={userDetails?.biggestWin?.[0]?.isFavorite}
                    onClick={() =>
                      handleGameClick(
                        userDetails?.biggestWin?.[0]?.providerName,
                        userDetails?.biggestWin?.[0]?.name?.EN ||
                        userDetails?.biggestWin?.[0]?.name,
                      )
                    }
                    providerName={userDetails?.biggestWin?.[0]?.providerName}
                  />
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default UserInfo;

const Gamescard = ({
  title,
  gameId,
  gameImage,
  gameName,
  isFavorite,
  onClick,
  providerName,
}) => {
  return (
    <>
      <div className="flex flex-col gap-[10px]">
        <p className="text-[15px] font-semibold">{title}</p>
        <PlayCard
          key={title}
          gameId={gameId}
          gameImage={gameImage}
          gameName={gameName}
          isFavorite={isFavorite}
          onClick={() => onClick(providerName, gameName)}
          providerName={providerName}
          sizeVariant={window?.innerWidth < 768 ? 'fixed' : 'default'}
        />
      </div>
    </>
  );
};
