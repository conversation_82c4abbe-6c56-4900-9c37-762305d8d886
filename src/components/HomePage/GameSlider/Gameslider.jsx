'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import EnterIcon from '@/assets/images/svg-images/enter-icon.svg';
import ChatIcon from '@/assets/images/svg-images/chat-icon.svg';
import RotateIcon from '@/assets/images/svg-images/rotate-icon.svg';

import ToggleSwitchNew from '@/components/ToggleSwitch/newToggle';
import useAuthStore from '@/store/useAuthStore';
import Auth from '@/components/Auth';
import useModalStore from '@/store/useModalStore';
import PlayCard from '@/components/PlayCard';
import CarouselSection from '@/components/Common/CarouselSection';

import useLivePlayers from '@/hooks/useLivePlayers';
import usePlayerStore from '@/store/usePlayerStore';
import {
  useCreateFriendsRequest,
  useGetFriendsListQuery,
} from '@/reactQuery/chatWindowQuery';
import PlayerAvatar from '../PlayerAvatar';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import UserInfo from '@/components/UserInfoModal';
import useUserInfoStore from '@/store/useUserInfoStore';
import ChatAvatar from '@/components/ChatAvatar';
import { slugify, userStatusColor } from '@/utils/helper';
import { UserRoundPlus, UserRound, Clock } from 'lucide-react';
import useActivePlayers from '@/hooks/useActivePlayer';
import GameSliderModel from '../GameSliderModal';
import useActivePlayerStore from '@/store/useActivePlayeStore';
import { useRouter } from 'next/navigation';
import FriendCall from '@/components/ChatWindow/Friends/FriendCall';
import useGeneralStore from '@/store/useGeneralStore';
import { useQueryClient } from '@tanstack/react-query';
import useAuthModalStore from '@/store/useAuthModalStore';

export default function GameSlider() {
  const { userDetails } = useAuthStore((state) => state);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const { openModal } = useModalStore((state) => state);
  const { openModal: AuthModalOpen } = useAuthModalStore();
  const { openUserInfoModal } = useUserInfoStore();
  const [enabled, setEnabled] = useState(true);
  const { refetchPublicUsers, refetchMyFriends } = useActivePlayers();
  const router = useRouter();
  const { setIsPrivateChatOpen, setUserId, searchUserName, setSearchUserName } =
    usePrivateChatStore((state) => state);
  const queryClient = useQueryClient();

  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(<UserInfo />);
    },
    [openUserInfoModal, openModal],
  );

  useLivePlayers();
  const { livePlayers, isPlayerLoading, refetchLivePlayers } = usePlayerStore(
    (state) => state,
  );

  const { myfriends, publicUsers } = useActivePlayerStore((state) => state);

  const {
    data: friendsList,
    isLoading: friendsLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({
    params: { search: '', playersStatus: 'AVAILABLE' },
    enabled: !enabled ? true : false,
  });

  const [timer, setTimer] = useState(60);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev === 1) {
          if (enabled) {
            refetchLivePlayers?.();
            refetchPublicUsers();
          } else {
            refetchFriendsList();
            refetchMyFriends();
          }
          return 60;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [enabled, refetchFriendsList, refetchLivePlayers]);

  useEffect(() => {
    if (!isAuthenticated) {
      queryClient.invalidateQueries({ queryKey: ['allLivePlayers'] });
    }
  }, [isAuthenticated]);

  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      refetchPublicUsers();
      toast.success(response?.data?.message);
    },
    onError: (error) => {
      refetchPublicUsers();
      toast.error(error.response.data.errors.map((e) => e.description));
    },
  });

  const sendFriendRequest = (id) => {
    mutationRequest.mutate({
      requesteeId: +id,
    });
  };

  const players = !enabled
    ? friendsList?.rows
        ?.filter((f) => f?.relationUser?.currentStatus === 'AVAILABLE')
        ?.map((f) => ({
          id: f?.relationUser?.id,
          name: f?.relationUser?.username,
          avatar: f?.relationUser?.profileImage,
          status: f?.relationUser?.currentStatus,
        })) || []
    : livePlayers
        ?.filter((p) => p?.id != userDetails?.id)
        .map((p) => ({
          id: p?.id,
          name: p?.username,
          avatar: p?.imageUrl,
          status: 'AVAILABLE',
        })) || [];

  const isLoading = !enabled ? friendsLoading : isPlayerLoading;

  const authCheck = () => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 1);
      AuthModalOpen(<Auth />);
      return false;
    }
    return true;
  };

  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
    if (searchUserName != '') {
      setSearchUserName('');
    }
  };
  const { openMenu, openChat: chatOpen } = useGeneralStore();

  // const gameData = enabled
  //   ? publicUsers?.data?.response || []
  //   : myfriends?.data?.response || [];

  const gameData = enabled ? publicUsers || [] : myfriends || [];

  // Custom controls
  const customControls = (
    <>
      <div className="flex max-h-[28px] w-fit flex-row-reverse items-center gap-2 rounded-full bg-primaryBorder px-[18px] py-1 md:flex-row md:gap-3 max-sm:px-2 ">
        <p
          className={`block whitespace-nowrap bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent md:hidden`}
        >
          {enabled ? 'PUBLIC PLAYERS' : 'MY FRIENDS'}
        </p>

        <p
          className={`hidden text-sm font-semibold uppercase md:block ${
            !enabled
              ? 'bg-cardBorderGradient bg-clip-text text-transparent'
              : 'text-[hsla(0,0%,42%,1)]'
          }`}
        >
          MY FRIENDS
        </p>

        <ToggleSwitchNew
          enabled={enabled}
          setEnabled={setEnabled}
          authCheck={authCheck}
        />

        <p
          className={`hidden text-sm font-semibold uppercase md:block ${
            enabled
              ? 'bg-cardBorderGradient bg-clip-text text-transparent'
              : 'text-[hsla(0,0%,42%,1)]'
          }`}
        >
          PUBLIC PLAYERS
        </p>
      </div>

      <div className="flex h-7 w-7 cursor-pointer items-center justify-center gap-3 rounded-lg bg-primaryBorder">
        <Image
          src={RotateIcon}
          className="h-6 w-6 object-cover"
          width={100}
          height={100}
          alt="Rotate"
        />
      </div>
    </>
  );

  return (
    <CarouselSection
      navigationStyle="groups"
      showGridIcon={true}
      showViewAll={false}
      cardGap="gap-0"
      containerMargin="my-6"
      emblaOptions={{ loop: false, align: 'start' }}
      showCustomControls={true}
      customControls={customControls}
      middleSection={
        <PlayerAvatar
          players={players}
          isLoading={isLoading}
          emptyMessage={'No players are online right now'}
          handleOpenUserInfoModal={handleOpenUserInfoModal}
        />
      }
      onClickGridIcon={() => {
        if (!isAuthenticated) {
          AuthModalOpen(<Auth />);
          return;
        }
        router.push('/dashboard/friends');
      }}
    >
      {gameData.map((game) => (
        <div
          className={`flex-[0_0_calc(100%/6)] pl-3  max-3xl:flex-[0_0_calc(100%/5)]
          max-xxl:flex-[0_0_calc(100%/4)]
         max-xl:flex-[0_0_calc(100%/5)]         
        max-lg:flex-[0_0_calc(100%/4.3)] 
        max-lmd:flex-[0_0_calc(100%/4)]
        max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]
        ${openMenu && chatOpen ? 'max-xxl:flex-[0_0_calc(100%/5)] max-2xl:flex-[0_0_calc(100%/4)]' : openMenu ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]' : chatOpen ? 'max-2xl:flex-[0_0_calc(100%/3.5)]' : ' max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/5)]'}
        `}
          key={game.id}
        >
          <PlayCard
            key={game?.gameId || ''}
            gameId={game?.gameId || ''}
            gameImage={game?.thumbnail || ''}
            gameName={game?.gameName?.EN ?? game?.gameName}
            sizeVariant="fixed"
            onClick={() =>
              router.push(
                `/casino/games/${slugify(
                  game?.providerName,
                )}/${slugify(game?.gameName?.EN ?? game?.gameName)}`,
              )
            }
            providerName={game?.providerName}
            isFavorite={game?.isFavorite}
          />

          {game.players?.slice(0, 3).map((player, index) => (
            <div
              key={player.userId}
              className="mt-2 flex w-full cursor-pointer justify-between rounded-full border border-slateGray-1000 px-2 py-1"
            >
              <div className="flex items-center gap-1">
                <div
                  className="relative h-[22px] w-[22px] md:h-[28px] md:w-[28px]"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenUserInfoModal(player?.userId);
                  }}
                >
                  <div className="h-full w-full overflow-hidden rounded-full">
                    <ChatAvatar
                      profileImage={player.profileImage || player.imageUrl}
                      firstName={player.firstName}
                      lastName={player.lastName}
                      userName={player.username}
                      avatarSize={window?.innerWidth < 768 ? 22 : 28}
                      imageClassName="h-full w-full object-cover rounded-full"
                    />
                  </div>
                  <span
                    className={`absolute bottom-[2px] right-0 h-[7px] w-[7px] rounded-full ${userStatusColor(
                      player?.currentStatus?.[player.userId] ||
                        player?.currentStatus ||
                        'AVAILABLE',
                    )} border-white border`}
                  />
                </div>
                <p className="text-[10px] font-bold text-white-1000 md:text-xs">
                  {player.username.toLowerCase()}
                </p>
              </div>

              <div className="flex items-center gap-3 md:gap-2">
                {player?.areFriends ? (
                  <>
                    {/* <Image
                      src={EnterIcon}
                      className="h-4 w-4 cursor-pointer"
                      width={100}
                      height={100}
                      alt="Enter"
                    /> */}
                    <FriendCall
                      key="call"
                      userId={player?.userId}
                      user={player}
                      iconClassName="h-4 w-4 cursor-pointer"
                    />
                    <div
                      onClick={() => {
                        openChat(player?.userId);
                      }}
                    >
                      <Image
                        src={ChatIcon}
                        className="h-4 w-4 cursor-pointer"
                        width={100}
                        height={100}
                        alt="Chat"
                      />
                    </div>
                  </>
                ) : (
                  <>
                    {isAuthenticated &&
                      player?.userId !== userDetails?.id &&
                      !player?.areFriends && (
                        <>
                          {player?.friendRequestStatus === false && (
                            <UserRoundPlus
                              id="UserRoundPlusFriend"
                              onClick={() => sendFriendRequest(player?.userId)}
                              className="h-[20px] w-[20px] cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                            />
                          )}

                          {player?.friendRequestStatus === 'pending' && (
                            <div className="relative inline-block">
                              <UserRound
                                size={20}
                                className="text-steelTeal-1000"
                              />
                              <Clock
                                size={12}
                                className="bg-black text-white absolute -bottom-1 -right-0.5 rounded-full"
                              />
                            </div>
                          )}
                        </>
                      )}
                  </>
                )}
              </div>
            </div>
          ))}
          {game.players?.length > 3 && (
            <p
              className="hover:text-decoration-underline mt-2 cursor-pointer ps-1 text-[12px] font-normal text-slateGray-900"
              onClick={() => {
                openModal(
                  <GameSliderModel
                    activePlayer={game}
                    userDetails={userDetails}
                    sendFriendRequest={sendFriendRequest}
                    openChat={openChat}
                  />,
                );
              }}
            >
              View All
            </p>
          )}
        </div>
      ))}
    </CarouselSection>
  );
}
