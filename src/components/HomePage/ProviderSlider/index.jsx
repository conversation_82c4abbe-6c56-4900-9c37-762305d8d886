'use client';
import React from 'react';
import HistoryIcon from '@/assets/images/svg-images/history.svg?url';
import Pragmatic from '@/assets/images/stock-images/pragmatic.webp';
import Evolution from '@/assets/images/stock-images/evolution.webp';
import Hacksaw from '@/assets/images/stock-images/hacksaw.webp';
import Nolimit from '@/assets/images/stock-images/nolimit.webp';
import PushGaming from '@/assets/images/stock-images/pushgaming.webp';
import RelaxGaming from '@/assets/images/stock-images/relaxgaming.webp';
import defaultImage from '@/assets/images/stock-images/provider-placeholder.webp'

import Image from 'next/image';
import useAuthStore from '@/store/useAuthStore';
import CarouselSection from '@/components/Common/CarouselSection';
import { useProviderQuery } from '@/reactQuery/gamesQuery';
import useGeneralStore from '@/store/useGeneralStore';
import { useRouter } from 'next/navigation';

function SafeImage({ src, fallback, alt, width, height, className }) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onError={(e) => { e.currentTarget.src = fallback.src }}
      loading="eager"
    />
  );
}

export default function ProviderSlider() {
  const providers = [
    { icon: Pragmatic },
    { icon: Evolution },
    { icon: Hacksaw },
    { icon: Nolimit },
    { icon: PushGaming },
    { icon: RelaxGaming },
  ];

  const { openMenu, openChat } = useGeneralStore();
  const router = useRouter();
  const { isAuthenticated } = useAuthStore((state) => state);

  // const {data: providers} = useProviderQuery()

  const handleViewAllClick = () => {
    router.push('/publishers');
  };

  const customControls = (
    <>
      <Image
        src={HistoryIcon}
        alt="History Icon"
        className="h-5 w-5 object-cover"
        width={20}
        height={20}
      />
      <p className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent">
        Publishers
      </p>
    </>
  );

  return (
    <div className="my-3 mb-6">
      <CarouselSection
        navigationStyle="default"
        showGridIcon={false}
        showViewAll={true}
        viewAll="View All"
        onViewAllClick={handleViewAllClick}
        cardGap=""
        containerMargin=""
        emblaOptions={{ loop: false, dragFree: true }}
        showCustomControls={true}
        customControls={customControls}
      >
        {providers.map((sport, index) => (
          <div
            key={index}
            className={`mt-4 flex-[0_0_calc(100%/6)] pl-3 max-3xl:flex-[0_0_calc(100%/5)]
              max-xxl:flex-[0_0_calc(100%/4)]
              max-2xl:flex-[0_0_calc(100%/3.5)]
              max-xl:flex-[0_0_calc(100%/4)]
              max-lg:flex-[0_0_calc(100%/3.5)]
              max-md:flex-[0_0_calc(100%/3.5)] max-sm:flex-[0_0_calc(100%/2.2)] max-sm:pl-[0.375rem]
              ${openMenu && openChat
                ? ' max-xxl:flex-[0_0_calc(100%/6)]'
                : openMenu
                  ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]'
                  : openChat
                    ? ''
                    : 'max-xxl:flex-[0_0_calc(100%/6)]'
              }`}
          >
            <button
              className="flex max-h-[6rem] min-h-[6rem] w-full flex-col items-center justify-center gap-1 rounded-[0.625rem] bg-slateGray-700 px-2 py-1 transition md:rounded-2xl md:px-4 md:py-4 max-sm:max-h-16 max-sm:min-h-16"
            >
              <SafeImage
                src={sport.icon}
                fallback={defaultImage}
                alt={`Provider ${index + 1}`}
                width={1000}
                height={1000}
                className="w-[7rem] max-sm:w-20"
              />
            </button>
          </div>
        ))}
      </CarouselSection>
    </div>
  );
}
