'use client';

import useLobby from '@/hooks/useLobby';
import MainLoader from '@/components/Common/Loader/MainLoader';
import useGameStore from '@/store/useGameStore';
import defaultImage from '@/assets/images/demo-image/card-image.png';
import useAuthStore from '@/store/useAuthStore';
import CarouselSection from '@/components/Common/CarouselSection';
import PlayCard from '@/components/PlayCard';
import { useRouter } from 'next/navigation';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import SportsIcon from '@/assets/images/svg-images/sports-icon.svg';
import { slugify } from '@/utils/helper';
import useGeneralStore from '@/store/useGeneralStore';
import useLobbyGames from '@/hooks/useLobbyGames';
import GameTabs from '../GameTabs';
import ContinuePlaying from '@/components/ContinuePlaying';

const transformCategoriesData = (categories) => {
  if (!Array.isArray(categories)) return [];

  return categories
    .filter(category => 
      category.name?.EN === 'Sportsbook' || category.name?.EN === 'Continue Playing' ||
      (category?.casinoGames && category.casinoGames.length > 0)
    )
    .map(category => ({
      categoryName: category.name?.EN || category.name,
      categoryIcon: category.iconUrl,
      orderId: category.orderId,
      games: category.casinoGames?.map(game => ({
        id: game.id,
        name: game.name,
        iconUrl: game.iconUrl || defaultImage,
        demoAvailable: game.demoAvailable,
        uniqueId: game.uniqueId,
        restrictedCountries: game.restrictedCountries,
        casinoProviderId: game.casinoProviderId,
        isFavorite: game.isFavorite,
        casinoProviderName: game.casinoProvider?.name,
      })) || [],
    }))
    .sort((a, b) => a.orderId - b.orderId);
};

function NoGamesAvailable() {
  return (
    <div className="col-span-full flex h-96 items-center justify-center">
      No games are available
    </div>
  );
}

function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center">
      <MainLoader className="max-h-10 min-h-10" />
    </div>
  );
}

export default function LobbyGames() {
  const searchQuery = useGameStore((state) => state.searchQuery);
  const { userDetails, isAuthenticated } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const router = useRouter();
  const {
    setActiveMenu,
    setOpenLobby,
    activePreviousState,
    openMenu,
    openChat,
  } = useGeneralStore();
  const { lobbyGames, isLobbyGamesLoading, isSuccess } = useLobbyGames({
    search: searchQuery,
    userId: userDetails?.id,
  });

  console.log("**lobbyGames", lobbyGames);

  const handleGameClick = (provider, name) => {
    setOpenLobby(false);
    router.push(`/casino/games/${slugify(provider)}/${slugify(name)}`);
  };

  const handleViewAllClick = (categoryName) => {
    setActiveMenu(activePreviousState);
    setOpenLobby(false);
    localStorage.setItem('selectedCategory', categoryName);
    router.push(`/category/${slugify(categoryName)}`);
  };

  if (isLobbyGamesLoading) {
    return <LoadingSpinner />;
  }

  const hasGamesInAnyCategory = lobbyGames?.categories?.rows?.some(
    category => category.casinoGames && category.casinoGames.length > 0
  );

  if (!hasGamesInAnyCategory && isSuccess) {
    return <NoGamesAvailable />;
  }

  const categoriesData = transformCategoriesData(lobbyGames?.categories?.rows || []);

  return (
    <>
      {categoriesData.map((subCategory) => {
        const { games, categoryName, categoryIcon } = subCategory ?? {};

        if (categoryName === 'Sportsbook') {
          return <GameTabs key={categoryName} />;
        }

        if (categoryName === 'Continue Playing') {
          return <ContinuePlaying  isCarousel />;
        }

        if (!games?.length) return null;

        return (
          <section
            key={categoryName}
            className="rounded-lg bg-oxfordBlue-1000 shadow-container"
          >
            <CarouselSection
              title={categoryName}
              titleIcon={categoryIcon || SportsIcon}
              navigationStyle="default"
              showViewAll={true}
              showNavigation={true}
              viewAll="View All"
              onViewAllClick={() => handleViewAllClick(categoryName)}
              cardGap="gap-0"
              containerMargin="mt-3 mb-10"
              titleClassName="text-[14px] font-[600] text-white-1000"
              emblaOptions={{
                loop: false,
                align: 'start',
                slidesToScroll: 'auto',
              }}
            >
              {games?.map((game, index) => (
                <div
                  className={`max-lmd:flex-[0_0_calc(100%/4)] flex-[0_0_calc(100%/6)]  pl-3
          max-3xl:flex-[0_0_calc(100%/5)]
         max-xxl:flex-[0_0_calc(100%/4)]         
        max-xl:flex-[0_0_calc(100%/5)] 
        max-lg:flex-[0_0_calc(100%/4.3)]
        max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]
        ${openMenu && openChat ? 'max-xxl:flex-[0_0_calc(100%/5)] max-2xl:flex-[0_0_calc(100%/4)]' : openMenu ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]' : openChat ? 'max-2xl:flex-[0_0_calc(100%/3.5)]' : ' max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/5)]'}
        `}
                  key={index + game?.name}
                >
                  <PlayCard
                    key={game?.name}
                    gameId={game.id}
                    gameImage={game?.iconUrl}
                    gameName={game?.name?.EN || game?.name}
                    isFavorite={game?.isFavorite}
                    sizeVariant="fixed"
                    onClick={() =>
                      handleGameClick(game?.casinoProviderName, game?.name?.EN)
                    }
                    providerName={game?.casinoProviderName}
                  />
                </div>
              ))}
            </CarouselSection>
          </section>
        );
      })}
    </>
  );
}