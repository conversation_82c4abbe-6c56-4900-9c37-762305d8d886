'use client';
import React, { useEffect, useState } from 'react';
import LobbyGames from '@/components/HomePage/LobbyGames';
import PromotionSlider from '@/components/PromotionBanner';
import GameSlider from '@/components/HomePage/GameSlider/Gameslider';
import LiveTable from '@/components/HomePage/LiveTable';
import Groups from '@/components/HomePage/Groups';
import HeroSlider from '@/components/HeroSlider';
import useAuthStore from '@/store/useAuthStore';
import ProviderSlider from '../ProviderSlider';
import Gamefilter from '../GameFilter';
import Category from '@/components/Category';
import useRecommendations from '@/hooks/useRecommendations';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import Favorites from '@/components/Favorites';
import ConnectedPlay from '../ConnectedPlay';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import ContinuePlaying from '@/components/ContinuePlaying';

function HomeSection() {
  const { isAuthenticated } = useAuthStore();
  const [selectedTab, setSelectedTab] = useState('Lobby');
  const [icon, setIcon] = useState();
  const connectedPlay = useActiveGroupStore((state) => state.connectedPlay);
  const { data, gamesLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useRecommendations({
      limit: 100,
      enabled: selectedTab === 'For You' || selectedTab === 'Lobby',
    });
  useEffect(() => {
    if (Object.keys(connectedPlay).length !== 0) {
      setSelectedTab('For You');
    } else {
      setSelectedTab('Lobby');
    }
  }, [isAuthenticated, connectedPlay]);
  return (
    <>
      {isAuthenticated ? (
        Object.keys(connectedPlay).length === 0 ? (
          <HeroSlider />
        ) : (
          <ConnectedPlay />
        )
      ) : (
        <PromotionSlider />
      )}

      <Gamefilter
        onTabChange={setSelectedTab}
        setIcon={setIcon}
        defaultTab={selectedTab}
      />

      {selectedTab === 'Lobby' && (
        <>
          <GameSlider />
          <Groups />
        </>
      )}
      {isAuthenticated && selectedTab === 'Lobby' && <ProviderSlider />}

      {selectedTab === 'Lobby' ? (
        <>
          {isAuthenticated && (
            <>
              <Category
                selectedTab="For You"
                externalGames={data}
                externalLoading={gamesLoading}
                externalFetchNextPage={fetchNextPage}
                externalHasNextPage={hasNextPage}
                externalIsFetchingNextPage={isFetchingNextPage}
                externalIcon={<ForYouIcon size={16} activeMenu />}
                isCarousel
              />
            </>
          )}
          <LobbyGames />
        </>
      ) : selectedTab === 'For You' ? (
        <Category
          selectedTab="For You"
          externalGames={data}
          externalLoading={gamesLoading}
          externalFetchNextPage={fetchNextPage}
          externalHasNextPage={hasNextPage}
          externalIsFetchingNextPage={isFetchingNextPage}
          externalIcon={<ForYouIcon size={16} activeMenu />}
        />
      ) : selectedTab === 'Favorites' ? (
        <>
          <Favorites />
        </>
      ) : selectedTab === 'Continue Playing' ? (
        <>
          <ContinuePlaying />
        </>
      ) : (
        <Category selectedTab={selectedTab} externalIcon={icon} />
      )}
      {selectedTab === 'Lobby' && (
        <>
          <LiveTable />
        </>
      )}
    </>
  );
}

export default HomeSection;
