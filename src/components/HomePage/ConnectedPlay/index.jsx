'use client';

import ChatIcon from '@/assets/images/svg-images/chat-icon.svg';
import MuteIcon from '@/assets/images/svg-images/mute.svg';
import UnMuteIcon from '@/assets/images/svg-images/unmute.svg';

import RotateIcon from '@/assets/images/svg-images/rotate-icon.svg';
import SoundIcon from '@/assets/images/svg-images/sound.svg';
import SoundOffIcon from '@/assets/images/svg-images/soundOff.svg';

import ChatAvatar from '@/components/ChatAvatar';
import CarouselSection from '@/components/Common/CarouselSection';
import PlayCard from '@/components/PlayCard';
import useGroupCall from '@/hooks/useGroupCall';
import usePrivateCall from '@/hooks/usePrivateCall';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useAuthStore from '@/store/useAuthStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import Image from 'next/image';
import Enter from '../../../assets/images/svg-images/enter.svg';
import PlayerAvatar from '../PlayerAvatar';
import { useCallback, useEffect } from 'react';
import useModalStore from '@/store/useModalStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import UserInfo from '@/components/UserInfoModal';
import useConnectedPlay from '@/hooks/useConnecedPlay';
import { slugify, userStatusColor } from '@/utils/helper';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import { useOpenChatWindow } from '@/utils/chatWindow';
import useGeneralStore from '@/store/useGeneralStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useCallModalStore from '@/store/useCallModalStore';
import { useRouter } from 'next/navigation';

export default function ConnectedPlay() {
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const voiceCall = useVoiceCallStore.getState().voiceCall;
  const { handleConnectedPlay } = useConnectedPlay();
  const { openMenu, openChat } = useGeneralStore();
  const router = useRouter();

  const {
    handleDisconnectCall,
    isMuted: isMutedPrivateCall,
    toggleMicrophone: toggleMicrophonePrivateCall,
    toggleSpeaker: toggleSpeakerPrivateCall,
    isSpeakerOn: isSpeakerOnPrivateCall,
  } = usePrivateCall();

  const {
    handleDeclineCall: handleDeclineGroupCall,
    isMuted,
    toggleMicrophone,
    toggleSpeaker: toggleSpeakerGroupCall,
    isSpeakerOn: isSpeakerOnGroupCall,
  } = useGroupCall();
  const connectedPlay = useActiveGroupStore((state) => state.connectedPlay);
  // let connectedPlay = {
  //   groupId: '61',
  //   groupName: 'Clash Masters one',
  //   groupProfile:
  //     'https://fans-dev-storage.s3.us-east-1.amazonaws.com/development/assets/group_profile/Clash_Masters_one-1755847833831.jpeg',
  //   groupBanner:
  //     'https://fans-dev-storage.s3.us-east-1.amazonaws.com/development/assets/group_banner/Clash_Masters_one-1755847833771.png',
  //   callLogId: '750',
  //   games: [
  //     {
  //       masterCasinoGameId: '1604',
  //       providerName: 'Kalamba',
  //       gameName: {
  //         EN: 'Monkey God',
  //       },
  //       gameIdentifier: 'klb_monkeygod',
  //       gameUrl:
  //         'https://s3.eu-central-1.amazonaws.com/kalamba-hub88-assets/monkey-god-icon-150x150.png',
  //       isFavourite: false,
  //       users: [
  //         {
  //           userId: '22',
  //           username: 'testyuser',
  //           firstName: 'Test',
  //           lastName: 'user',
  //           imageUrl: null,
  //           currentStatus: {
  //             22: 'AVAILABLE',
  //           },
  //           areFriends: false,
  //           ignored: false,
  //           liked: false,
  //           likesCount: 0,
  //           friendRequestStatus: false,
  //           ignoredBy: false,
  //         },
  //       ],
  //     },
  //   ],
  //   otherUsersOnCall: [],
  //   suggestedGames: [
  //     {
  //       masterCasinoGameId: '1551',
  //       name: {
  //         EN: 'Live Baccarat Lobby',
  //       },
  //       page_code: 'evo_livebaccaratlobby',
  //       imageUrl: 'https://cdn.hub88.io/evolution/evo_livebaccaratlobby.jpg',
  //       masterCasinoProviderId: '18',
  //       providerName: 'Evolution Gaming',
  //       isFavorite: true,
  //     },
  //     {
  //       masterCasinoGameId: '1604',
  //       name: {
  //         EN: 'Monkey God',
  //       },
  //       page_code: 'klb_monkeygod',
  //       imageUrl:
  //         'https://s3.eu-central-1.amazonaws.com/kalamba-hub88-assets/monkey-god-icon-150x150.png',
  //       masterCasinoProviderId: '20',
  //       providerName: 'Kalamba',
  //       isFavorite: true,
  //     },
  //     {
  //       masterCasinoGameId: '1685',
  //       name: {
  //         EN: 'Monkey God LowMin',
  //       },
  //       page_code: 'klb_monkeygodlowmin',
  //       imageUrl:
  //         'https://s3.eu-central-1.amazonaws.com/kalamba-hub88-assets/monkey-god-icon-150x150.png',
  //       masterCasinoProviderId: '20',
  //       providerName: 'Kalamba',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1830',
  //       name: {
  //         EN: 'Balloon Race',
  //       },
  //       page_code: 'evo_balloonrace',
  //       imageUrl: 'https://cdn.hub88.io/evolution/evo_balloonrace.jpg',
  //       masterCasinoProviderId: '18',
  //       providerName: 'Evolution Gaming',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1725',
  //       name: {
  //         EN: 'Chaos Crew Scratch',
  //       },
  //       page_code: 'hsg_chaoscrewscratch',
  //       imageUrl:
  //         'https://cdn.hub88.io/hacksawgaming/chaos-crew-scratch-thumbnail.jpg',
  //       masterCasinoProviderId: '21',
  //       providerName: 'Hacksaw Gaming',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1611',
  //       name: {
  //         EN: 'Dino Odyssey',
  //       },
  //       page_code: 'klb_dinoodyssey',
  //       imageUrl:
  //         'https://s3.eu-central-1.amazonaws.com/kalamba-hub88-assets/dino-odyssey-icon-150x150.png',
  //       masterCasinoProviderId: '20',
  //       providerName: 'Kalamba',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1612',
  //       name: {
  //         EN: 'Double Joker',
  //       },
  //       page_code: 'klb_doublejoker',
  //       imageUrl:
  //         'https://s3.eu-central-1.amazonaws.com/kalamba-hub88-assets/double-joker-icon-150x150.png',
  //       masterCasinoProviderId: '20',
  //       providerName: 'Kalamba',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1636',
  //       name: {
  //         EN: 'Lightning Dice',
  //       },
  //       page_code: 'evo_lightningdice',
  //       imageUrl: 'https://cdn.hub88.io/evolution/evo_lightningdice.jpg',
  //       masterCasinoProviderId: '18',
  //       providerName: 'Evolution Gaming',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1588',
  //       name: {
  //         EN: 'Baccarat',
  //       },
  //       page_code: 'ont_baccarat',
  //       imageUrl:
  //         'https://files.onetouch.io/game/game-thumbs/latest/OneTouch_Baccarat_490x368.jpg',
  //       masterCasinoProviderId: '19',
  //       providerName: 'OneTouch Generic',
  //       isFavorite: false,
  //     },
  //     {
  //       masterCasinoGameId: '1856',
  //       name: {
  //         EN: 'Live88 Teen Patti 1Day',
  //       },
  //       page_code: 'bbl_live88teenpatti1day',
  //       imageUrl: 'https://cdn.hub88.io/bombaylive/bbl_teenpatti1daylive88.png',
  //       masterCasinoProviderId: '27',
  //       providerName: 'Live88',
  //       isFavorite: false,
  //     },
  //   ],
  // };
  const setIsMinimized = useCallModalStore((state) => state.setIsMinimized);

  const { openModal } = useModalStore((state) => state);
  const { openUserInfoModal } = useUserInfoStore();
  console.log('🚀 ~ ConnectedPlay ~ connectedPlay:', connectedPlay);
  const { setIsPrivateChatOpen, setUserId, searchUserName, setSearchUserName } =
    usePrivateChatStore((state) => state);
  const { setIsGroupChatOpen, setGroupId, setGroupName } = useGroupChatStore(
    (state) => state,
  );

  const openChatWindow = useOpenChatWindow();

  const handleMute = () => {
    if (voiceCall?.isOneToOneCall) {
      return {
        icon: isMutedPrivateCall ? MuteIcon : UnMuteIcon,
        toggle: toggleMicrophonePrivateCall,
        speakerIcon: isSpeakerOnPrivateCall ? SoundIcon : SoundOffIcon,
        toggleSpeaker: toggleSpeakerPrivateCall,
      };
    } else {
      return {
        icon: isMuted ? MuteIcon : UnMuteIcon,
        toggle: toggleMicrophone,
        speakerIcon: isSpeakerOnGroupCall ? SoundIcon : SoundOffIcon,
        toggleSpeaker: toggleSpeakerGroupCall,
      };
    }
  };
  const handleOpenUserInfoModal = useCallback(
    (userId) => {
      openUserInfoModal(userId);
      openModal(<UserInfo />);
    },
    [openUserInfoModal, openModal],
  );
  const openGroupChat = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
    setIsPrivateChatOpen(false);
    if (searchUserName !== '') setSearchUserName('');
  };

  const middleSectionData = () => {
    if (voiceCall?.isOneToOneCall) {
      return {
        image: connectedPlay?.otherUsersOnCall?.filter(
          (user) => user.userId != userDetails?.id,
        )?.[0]?.imageUrl,
        name: connectedPlay?.otherUsersOnCall?.filter(
          (user) => user.userId != userDetails?.id,
        )?.[0]?.username,
      };
    } else {
      return {
        image: connectedPlay?.groupProfile,
        name: connectedPlay?.groupName,
      };
    }
  };
  // Custom controls for Connected Play
  const customControls = (
    <>
      <div className="flex max-h-[28px] w-fit items-center gap-3 rounded-full bg-primaryBorder px-[18px] py-1">
        <p className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold text-transparent">
          CONNECTED PLAY
        </p>
      </div>
      <div className="flex h-7 w-7 cursor-pointer items-center justify-center gap-3 rounded-lg bg-primaryBorder">
        <Image
          src={RotateIcon}
          className="h-6 w-6 object-cover"
          width={100}
          height={100}
          onClick={() =>
            handleConnectedPlay({
              callLogId: connectedPlay?.callLogId,
              groupId: connectedPlay?.groupId,
              isOneToOneCall: connectedPlay?.groupId ? false : true,
            })
          }
        />
      </div>
    </>
  );

  const middleSection = (
    <>
      <div className="mt-4 flex w-full justify-between gap-2 rounded-full border border-slateGray-1000 px-3 py-1 md:w-fit">
        <div className="flex items-center gap-1">
          <div className="relative h-[30px] w-[30px] flex-shrink-0 rounded-full md:h-9 md:w-9">
            <ChatAvatar
              profileImage={middleSectionData()?.image}
              firstName={middleSectionData()?.name}
              lastName=""
              userName={middleSectionData()?.name}
              imageClassName="h-full w-full rounded-full"
              imageWidth={window?.innerWidth < 768 ? 30 : 36}
              imageHeight={window?.innerWidth < 768 ? 30 : 36}
              avatarSize={window?.innerWidth < 768 ? 30 : 36}
            />
          </div>
          <p className="w-full max-w-[200px] truncate text-[10px] font-normal text-white-1000 md:max-w-[250px] md:text-xs">
            {middleSectionData()?.name}
            {/* Group Long Name for long text shown herere */}
          </p>
        </div>
        <div className="flex items-center gap-[14px] md:gap-3">
          <Image
            src={handleMute()?.icon}
            width={100}
            height={100}
            className="h-5 w-5 cursor-pointer"
            alt={isMuted ? 'Mute' : 'UnMute'}
            onClick={() => handleMute()?.toggle()}
          />
          <Image
            src={handleMute()?.speakerIcon}
            width={100}
            height={100}
            className="h-5 w-5 cursor-pointer"
            alt="Sound"
            onClick={() => handleMute()?.toggleSpeaker()}
          />

          <Image
            src={ChatIcon}
            width={100}
            height={100}
            className="h-5 w-5 cursor-pointer"
            alt="Chat"
            onClick={(e) => {
              e.stopPropagation();

              if (voiceCall?.isOneToOneCall) {
                setUserId(
                  connectedPlay?.otherUsersOnCall?.filter(
                    (user) => user.userId != userDetails?.id,
                  )?.[0]?.userId,
                );
                setIsPrivateChatOpen(true);
                if (searchUserName !== '') {
                  setSearchUserName('');
                }
                openChatWindow();
              } else {
                openGroupChat(connectedPlay?.groupId, connectedPlay?.groupName);
                openChatWindow();
              }
            }}
          />
        </div>
      </div>
      <PlayerAvatar
        players={[
          {
            userId: userDetails?.id,
            username: userDetails?.username,
            imageUrl: userDetails?.imageUrl,
          },
          ...(connectedPlay?.otherUsersOnCall?.filter(user=>user.userId!=userDetails?.id) ?? []), // ensures new array
        ]}
        isLoading={false}
        emptyMessage={'No players are online right now'}
        handleOpenUserInfoModal={handleOpenUserInfoModal}
        className="w-full overflow-hidden px-0 pt-2 md:p-4"
      />
    </>
  );

  if (Object.keys(connectedPlay).length === 0) return null;
  return (
    <>
      {isAuthenticated && (
        <CarouselSection
          navigationStyle="groups"
          showGridIcon={true}
          showViewAll={false}
          cardGap="gap-0"
          containerMargin="mb-6"
          emblaOptions={{ loop: false, align: 'start' }}
          showCustomControls={true}
          customControls={customControls}
          middleSection={middleSection}
          gridIcon={Enter}
          onClickGridIcon={() => {
            setIsMinimized(false);
          }}
        >
          {[...connectedPlay?.games, ...connectedPlay?.suggestedGames].map(
            (game) => (
              <div
                className={`max-lmd:flex-[0_0_calc(100%/4)] flex-[0_0_calc(100%/6)]  pl-3
          max-3xl:flex-[0_0_calc(100%/5)]
         max-xxl:flex-[0_0_calc(100%/4)]         
        max-xl:flex-[0_0_calc(100%/5)] 
        max-lg:flex-[0_0_calc(100%/4.3)]
        max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]
        ${openMenu && openChat ? 'max-xxl:flex-[0_0_calc(100%/5)] max-2xl:flex-[0_0_calc(100%/4)]' : openMenu ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]' : openChat ? 'max-2xl:flex-[0_0_calc(100%/3.5)]' : ' max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/5)]'}
        `}
                key={`${game?.masterCasinoGameId}-${Math.random()}`}
              >
                <PlayCard
                  key={game?.masterCasinoGameId}
                  sizeVariant="fixed"
                  gameId={game?.masterCasinoGameId}
                  gameImage={game?.imageUrl || game?.gameUrl}
                  gameName={game?.name?.EN || game?.gameName?.EN}
                  providerName={game?.providerName}
                  onClick={() =>
                    router.push(
                      `/casino/games/${slugify(game?.providerName)}/${slugify(game?.name?.EN || game?.gameName?.EN)}`,
                    )
                  }
                />

                {[...(game?.players || []), ...(game?.users || [])]?.map(
                  (player) => (
                    <div
                      key={player?.userId}
                      className="mt-2 flex w-full cursor-pointer justify-between rounded-full border border-slateGray-1000 px-2 py-1"
                    >
                      <div className="flex items-center gap-1">
                        <div
                          className="relative h-[22px] w-[22px] md:h-[28px] md:w-[28px]"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenUserInfoModal(player?.userId);
                          }}
                        >
                          <div className="h-full w-full overflow-hidden rounded-full">
                            <ChatAvatar
                              profileImage={
                                player?.profileImage || player?.imageUrl
                              }
                              firstName={player?.firstName}
                              lastName={player?.lastName}
                              userName={player?.username}
                              avatarSize={window?.innerWidth < 767 ? 22 : 28}
                              imageClassName="h-full w-full object-cover rounded-full"
                            />
                          </div>

                          {(player?.currentStatus?.[player?.userId] ===
                            'AVAILABLE' ||
                            player?.currentStatus === 'AVAILABLE') && (
                            <span
                              className={`absolute bottom-[1px] right-[-1px]  h-[7px] w-[7px]  rounded-full md:bottom-[3px] md:right-0 ${userStatusColor(
                                player?.currentStatus?.[player?.userId] ||
                                  'AVAILABLE',
                              )} border-white border`}
                            />
                          )}
                        </div>
                        <p className="text-[10px] font-bold text-white-1000 md:text-xs">
                          {player.username.toLowerCase()}
                        </p>
                      </div>
                      {player?.userId != userDetails?.id && (
                        <div className="flex items-center gap-2">
                          <Image
                            src={ChatIcon}
                            className="h-4 w-4"
                            width={100}
                            height={100}
                            alt="Chat"
                            onClick={() => {
                              setUserId(player.userId);
                              setIsPrivateChatOpen(true);
                              if (searchUserName !== '') {
                                setSearchUserName('');
                              }
                              openChatWindow();
                            }}
                          />
                        </div>
                      )}
                    </div>
                  ),
                )}
                {[...(game?.players || []), ...(game?.users || [])]?.length >
                  3 && (
                  <p className="hover:text-decoration-underline mt-2 cursor-pointer ps-1 text-[12px] font-normal text-slateGray-900">
                    View All
                  </p>
                )}
              </div>
            ),
          )}
        </CarouselSection>
      )}
    </>
  );
}
