'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import ChatIcon from '@/assets/images/svg-images/chat-icon.svg';
import RotateIcon from '@/assets/images/svg-images/rotate-icon.svg';
import ToggleSwitchNew from '@/components/ToggleSwitch/newToggle';
import useAuthStore from '@/store/useAuthStore';
import Auth from '@/components/Auth';
import useModalStore from '@/store/useModalStore';
import PlayCard from '@/components/PlayCard';
import CarouselSection from '@/components/Common/CarouselSection';
import EnterIcon from '@/assets/images/svg-images/enter-icon.svg';
import GroupUser from '@/assets/icons/GroupUser';
import DoubleUserIcon from '@/assets/icons/DoubleUserIcon';
import useGeneralStore from '@/store/useGeneralStore';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import ChatAvatar from '@/components/ChatAvatar';
import { useOpenChatWindow } from '@/utils/chatWindow';
import useGroupChatStore from '@/store/useGroupChatStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import GroupCall from '@/components/ChatWindow/GroupChat/GroupCall';
import { useJoinGroup } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import useAuthTab from '@/store/useAuthTab';
import { slugify } from '@/utils/helper';
import { useRouter } from 'next/navigation';
import useActiveGroups from '@/hooks/useActiveGroup';
import ShowGroupModal from '@/components/Models/ShowGroupModal';
import useAuthModalStore from '@/store/useAuthModalStore';
// import ChatIcon from '@/assets/images/svg-images/chat-icon.svg';
const games = [
  {
    id: 1,
    key: 'Game Title Here',
    image: '/games/akkadia.jpg',
    title: 'Game Title Here',
  },
  {
    id: 2,
    key: 'Game Title Here',
    image: '/games/akkadia.jpg',
    title: 'Game Title Here',
  },
  {
    id: 3,
    key: 'Game Title Here',
    image: '/games/akkadia.jpg',
    title: 'Game Title Here',
  },
  {
    id: 4,
    key: 'Game Title Here',
    image: '/games/akkadia.jpg',
    title: 'Game Title Here',
  },
  {
    id: 5,
    key: 'Game Title Here',
    image: '/games/akkadia.jpg',
    title: 'Game Title Here',
  },
  {
    id: 6,
    key: 'Game Title Here',
    image: '/games/akkadia.jpg',
    title: 'Game Title Here',
  },
];

const avatars = [
  { id: 1, name: 'shivam98', avatar: '/avatars/1.jpg' },
  { id: 2, name: 'codewithsomya', avatar: '/avatars/2.jpg' },
  { id: 3, name: 'viveka', avatar: '/avatars/3.jpg' },
];

export default function Groups() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { openModal } = useModalStore((state) => state);
  const { openModal: AuthModalOpen } = useAuthModalStore();
  const [enabled, setEnabled] = useState(true);
  const { setSelectedTab } = useAuthTab((state) => state);
  const queryClient = useQueryClient();
  const router = useRouter();
  const {
    myGroups,
    setMyGroupsLoading,
    publicGroups,
    setPublicGroupsLoading,
    setActiveTab,
  } = useActiveGroupStore((state) => state);
  const { refetchMyGroups, refetchPublicGroups } = useActiveGroups();
  // console.log('🚀 ~ Groups ~ publicGroups:', myGroups, publicGroups);

  const authCheck = () => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 1);
      AuthModalOpen(<Auth />);
      return false;
    }
    return true;
  };
  const { openMenu, openChat } = useGeneralStore();
  const openChatWindow = useOpenChatWindow();
  const { setIsGroupChatOpen, setGroupId, setGroupName } = useGroupChatStore(
    (state) => state,
  );
  const { setIsPrivateChatOpen, searchUserName, setSearchUserName } =
    usePrivateChatStore((state) => state);
  const openGroupChat = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
    setIsPrivateChatOpen(false);
    if (searchUserName !== '') setSearchUserName('');
  };
  const joinGroup = useJoinGroup({
    onSuccess: async (response) => {
      toast.success('Group Joined');
      queryClient.invalidateQueries({
        queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });
  useEffect(() => {
    const interval = setInterval(() => {
      // setTimer((prev) => {
      //   if (prev === 1) {
      //     if (enabled) {
      //       refetchLivePlayers?.();
      //       refetchPublicUsers();
      //     } else {
      //       refetchFriendsList();
      //       refetchMyFriends();
      //     }
      //     return 60;
      //   }
      //   return prev - 1;
      // });
      refetchMyGroups();
      refetchPublicGroups();
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  const openProfileDetails = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
  };

  const handleOpenModal = (e, id, group) => {
    e.stopPropagation();
    openProfileDetails(id, group?.groupName);
    openModal(<ShowGroupModal groupId={id} groupData={group} />);
  };

  const customControls = (
    <>
      <div className="flex max-h-[28px] w-fit flex-row-reverse items-center gap-2 rounded-full bg-primaryBorder px-[18px] py-1 md:flex-row md:gap-3 max-sm:px-2">
        <p className="block whitespace-nowrap bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent md:hidden">
          {enabled ? 'PUBLIC GROUPS' : 'MY GROUPS'}
        </p>
        <p
          className={`hidden text-sm font-semibold uppercase md:block ${
            !enabled
              ? 'bg-cardBorderGradient bg-clip-text text-transparent'
              : 'text-[hsla(0,0%,42%,1)]'
          }`}
        >
          MY GROUPS
        </p>

        <ToggleSwitchNew
          enabled={enabled}
          setEnabled={setEnabled}
          authCheck={authCheck}
        />
        <p
          className={`hidden text-sm font-semibold uppercase md:block ${
            enabled
              ? 'bg-cardBorderGradient bg-clip-text text-transparent'
              : 'text-[hsla(0,0%,42%,1)]'
          }`}
        >
          PUBLIC GROUPS
        </p>
      </div>

      <div className="flex h-7 w-7 cursor-pointer items-center justify-center gap-3 rounded-lg bg-primaryBorder">
        <Image
          src={RotateIcon}
          className="h-6 w-6 object-cover"
          width={100}
          height={100}
          alt="Rotate"
        />
      </div>
    </>
  );

  return (
    <CarouselSection
      navigationStyle="groups"
      showGridIcon={true}
      showViewAll={false}
      cardGap="gap-0"
      containerMargin="my-6"
      emblaOptions={{ loop: false, align: 'start' }}
      showCustomControls={true}
      customControls={customControls}
      onClickGridIcon={() => router.push('/dashboard/groups')}
    >
      {enabled
        ? publicGroups.length === 0 && (
            <div className="flex w-full pl-6">
              Currently no groups are available
            </div>
          )
        : myGroups.length === 0 && (
            <div className="flex w-full pl-6">
              Currently no groups are available
            </div>
          )}
      {[...(!enabled ? myGroups || [] : publicGroups || [])].map((game) => (
        <div
          className={`flex-[0_0_calc(100%/6)] pl-3  max-3xl:flex-[0_0_calc(100%/5)]
          max-xxl:flex-[0_0_calc(100%/4)]
         max-xl:flex-[0_0_calc(100%/5)]         
        max-lg:flex-[0_0_calc(100%/4.3)] 
        max-lmd:flex-[0_0_calc(100%/4)]
        max-md:flex-[0_0_calc(100%/4)] max-sm:flex-[0_0_calc(100%/3.2)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/2.2)]
        ${openMenu && openChat ? 'max-xxl:flex-[0_0_calc(100%/5)] max-2xl:flex-[0_0_calc(100%/4)]' : openMenu ? 'max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/6)]' : openChat ? 'max-2xl:flex-[0_0_calc(100%/3.5)]' : ' max-xxl:flex-[0_0_calc(100%/6)] max-2xl:flex-[0_0_calc(100%/5)]'}
        `}
          key={game.id}
        >
          <PlayCard
            key={game?.gameId}
            gameId={game?.gameId}
            sizeVariant="fixed"
            gameImage={game?.gameThumbnail || ''}
            gameName={game?.gameName?.EN || 'Awaiting Game Selection'}
            providerName={game?.providerName}
            onClick={() => {
              game?.gamePlay &&
                router.push(
                  `/casino/games/${slugify(
                    game?.providerName,
                  )}/${slugify(game?.gameName?.EN)}`,
                );
            }}
            isFavorite={game?.isFavorite}
            showBelowSection={game?.gamePlay ?? true}
          />

          <div className="mt-2 flex w-full  flex-col items-center justify-between gap-2 rounded-2xl border border-slateGray-1000 px-1 py-2 md:px-2">
            <div className="flex w-full  gap-2">
              <div
                className="relative h-[36px] w-[36px] min-w-[36px] cursor-pointer rounded-full"
                onClick={(e) => handleOpenModal(e, game?.groupId, game)}
              >
                {/* <Image
                  src={Player1}
                  className="h-full w-full rounded-full object-cover"
                  width={100}
                  height={100}
                /> */}
                <ChatAvatar
                  profileImage={game?.groupProfile}
                  firstName={game?.groupName}
                  lastName=""
                  userName={game?.groupName}
                  imageClassName="h-full w-full rounded-full object-cover "
                  imageWidth={36}
                  imageHeight={36}
                  avatarSize={36}
                />
              </div>
              <p className="flex h-8 items-center">
                <p className="truncate-2-lines w-full max-w-[8.625rem] text-xs font-bold">
                  {game?.groupName}
                </p>
              </p>
            </div>
            <div className="flex w-full items-center justify-between gap-1">
              <div className="flex  items-start justify-start gap-1 whitespace-nowrap">
                <DoubleUserIcon className="" />
                <p className="text-xs font-black text-steelTeal-200">
                  • {game?.totalUsersInGroup}
                </p>

                <p className="text-xs font-black text-green-600">
                  • {game?.availableUsersInGroup || 0}
                </p>
                <p className="text-xs font-black text-steelTeal-600">
                  • {game?.totalUsersInCall || 0}
                </p>
              </div>
              <div className="flex gap-[5px]">
                {game?.users?.slice(0, 3).map((user, index) => (
                  <div
                    className={`relative h-[29px] min-w-[29px] rounded-full md:h-[36px] md:w-[36px] md:min-w-[36px] ${index != 0 && '-ms-5'}`}
                    key={index}
                  >
                    {/* <Image
                    src={}
                    className="h-full w-full rounded-full object-cover"
                    width={100}
                    height={100}
                  /> */}
                    <ChatAvatar
                      profileImage={user?.imageUrl}
                      firstName={user?.username}
                      lastName=""
                      userName={user?.username}
                      imageClassName="h-full w-full rounded-full object-cover"
                      imageWidth={window?.innerWidth < 768 ? 29 : 36}
                      imageHeight={window?.innerWidth < 768 ? 29 : 36}
                      avatarSize={window?.innerWidth < 768 ? 29 : 36}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="grid w-full grid-cols-2 justify-between gap-1">
              {game?.isUserExist ? (
                <GroupCall
                  groupId={game.groupId}
                  groupName={game.groupName}
                  className="h-4 w-4 min-w-4 max-sm:h-[.6875rem] max-sm:w-[.6875rem]"
                  buttonClassName="flex max-h-[35px] w-full items-center justify-center gap-[6px] rounded-full border border-avatarBorder bg-primaryBorder px-2 py-[8px] text-xs max-sm:w-full max-lg:text-[0.625rem]"
                  buttonName="Connect"
                />
              ) : (
                <button className="flex max-h-[35px] w-full items-center justify-center gap-[6px] rounded-full border border-avatarBorder bg-primaryBorder px-2 py-[8px] text-xs max-lg:text-[0.625rem]  max-sm:w-full">
                  <Image
                    src={EnterIcon}
                    className="h-4 w-4 min-w-4 max-sm:h-[.6875rem] max-sm:w-[.6875rem]"
                    width={100}
                    height={100}
                    alt="Enter"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!isAuthenticated) {
                        setSelectedTab(0);
                        localStorage.setItem('activeTab', 0);
                        AuthModalOpen(<Auth />);
                        return;
                      }
                      joinGroup.mutate({
                        groupId: game.groupId,
                        action: 'join',
                      });
                    }}
                  />{' '}
                  Join
                </button>
              )}

              <button
                className="flex max-h-[35px] w-full  items-center justify-center gap-[6px] rounded-full border border-avatarBorder bg-primaryBorder px-2 py-[8px] text-[12px] max-sm:w-full max-sm:text-[0.625rem]"
                onClick={(e) => {
                  e.stopPropagation();
                  openGroupChat(game.groupId, game?.groupName);
                  openChatWindow();
                }}
              >
                <Image
                  src={ChatIcon}
                  className="h-4 w-4 min-w-4 max-sm:h-[.6875rem] max-sm:w-[.6875rem]"
                  width={100}
                  height={100}
                  alt="Enter"
                />{' '}
                Chat
              </button>
            </div>
          </div>
        </div>
      ))}
    </CarouselSection>
  );
}
