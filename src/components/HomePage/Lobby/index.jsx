'use client';
import { useEffect, useState } from 'react';
import GameSlider from '../GameSlider/Gameslider';
import GameTabs from '../GameTabs';
import Groups from '../Groups';
import LiveTable from '../LiveTable';
import LobbyGames from '../LobbyGames';
import ProviderSlider from '../ProviderSlider';
import useAuthStore from '@/store/useAuthStore';
import useRecommendations from '@/hooks/useRecommendations';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import Category from '@/components/Category';
import Gamefilter from '../GameFilter';
import Favorites from '@/components/Favorites';
import ConnectedPlay from '../ConnectedPlay';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import ContinuePlaying from '@/components/ContinuePlaying';

const LobbySection = () => {
  const { isAuthenticated } = useAuthStore();
  const connectedPlay = useActiveGroupStore((state) => state.connectedPlay);

  const [selectedTab, setSelectedTab] = useState(
    Object.keys(connectedPlay).length !== 0 ? 'For You' : 'Lobby'
  );
  const [icon, setIcon] = useState(
    <ForYouIcon size={16} activeMenu={selectedTab === 'For You'} />
  );

  const { data, gamesLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useRecommendations({
      limit: 50,
      enabled: selectedTab === 'For You' || selectedTab === 'Lobby',
    });

  useEffect(() => {
    if ((isAuthenticated && selectedTab === 'For You') || Object.keys(connectedPlay).length !== 0) {
      setSelectedTab('For You');
      setIcon(<ForYouIcon size={16} activeMenu />);
      if (connectedPlay?.scroll) window.scrollTo(0, 0);
    }
  }, [isAuthenticated, connectedPlay]);

  const isLobby = selectedTab === 'Lobby';
  const isForYou = selectedTab === 'For You';
  const isFavorites = selectedTab === 'Favorites';
  const isContinuePlaying = selectedTab === 'Continue Playing';
  const hasConnectedPlay = Object.keys(connectedPlay).length !== 0;

  return (
    <div className="mx-auto max-w-containerWidth">
      {hasConnectedPlay && <ConnectedPlay />}

      <Gamefilter onTabChange={setSelectedTab} setIcon={setIcon} defaultTab={selectedTab} />

      {isLobby && (
        <>
          <GameSlider />
          <Groups />
          <ProviderSlider />
        </>
      )}

      {isLobby && (
        <>
          {isAuthenticated && (
            <>
              <Category
                selectedTab="For You"
                externalGames={data}
                externalLoading={gamesLoading}
                externalFetchNextPage={fetchNextPage}
                externalHasNextPage={hasNextPage}
                externalIsFetchingNextPage={isFetchingNextPage}
                externalIcon={icon}
                isCarousel
              />
              <ContinuePlaying isCarousel />
            </>
          )}
          <LobbyGames />
        </>
      )}

      {isForYou && (
        <Category
          selectedTab="For You"
          externalGames={data}
          externalLoading={gamesLoading}
          externalFetchNextPage={fetchNextPage}
          externalHasNextPage={hasNextPage}
          externalIsFetchingNextPage={isFetchingNextPage}
          externalIcon={icon}
        />
      )}

      {isFavorites && <Favorites />}
      {isContinuePlaying && <ContinuePlaying />}
      {!isLobby && !isForYou && !isFavorites && !isContinuePlaying && (
        <Category selectedTab={selectedTab} externalIcon={icon} />
      )}

      {isLobby && (
        <>
          <GameTabs />
          <LiveTable />
        </>
      )}
    </div>
  );
};

export default LobbySection;
