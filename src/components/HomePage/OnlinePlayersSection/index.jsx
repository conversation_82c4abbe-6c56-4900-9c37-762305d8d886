'use client';

import PlayerCard from '@/components/Common/PlayerCard';
import useLivePlayers from '@/hooks/useLivePlayers';
import usePlayerStore from '@/store/usePlayerStore';

export default function OnlinePlayersSection() {
  const { onlinePlayers } = useLivePlayers();
  const { livePlayers } = usePlayerStore((state) => state);

  return (
    <section className="mb-10 rounded-lg p-3">
      <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">Online Players</h6>
      </div>

      {livePlayers && livePlayers.length > 0 ? (
        <div className="scrollbar-none flex gap-3 overflow-x-auto sm:grid sm:grid-cols-8 sm:gap-4 md:grid-cols-10 lg:grid-cols-12">
          {livePlayers.map((player, index) => (
            <PlayerCard
              gameId={1}
              gameName="Custom Game"
              gameImage="/custom.png"
              wrapperClass="w-[300px]"
              imageWrapperClass="relative pb-[60%]"
            />
          ))}
        </div>
      ) : (
        <div className="py-4 text-center text-white-1000">
          No online players at the moment.
        </div>
      )}
    </section>
  );
}
