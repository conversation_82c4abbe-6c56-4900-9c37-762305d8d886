'use client';

import ProfileIcon from '@/assets/icons/Profile';
import ChatAvatar from '@/components/ChatAvatar';
import useModalStore from '@/store/useModalStore';
import { X, UserRoundPlus, UserRound, Clock } from 'lucide-react';
import Image from 'next/image';
import EnterIcon from '@/assets/images/svg-images/enter-icon.svg';
import ChatIcon from '@/assets/images/svg-images/chat-icon.svg';
import { useState } from 'react';

function GameSliderModel({
  activePlayer,
  userDetails,
  sendFriendRequest,
  openChat,
}) {
  const { closeModal } = useModalStore((state) => state);
  const [players, setPlayers] = useState(activePlayer.players);

  const handleSendFriendRequest = (id) => {
    sendFriendRequest(id);
    setPlayers((prev) =>
      prev.map((p) =>
        p.userId === id ? { ...p, friendRequestStatus: 'pending' } : p,
      ),
    );
  };

  return (
    <div
      tabIndex={-1}
      aria-hidden="true"
      className="bg-black/60 fixed inset-0 z-50 flex items-center justify-center overflow-y-auto px-4"
    >
      <div className="relative w-full max-w-lg p-4">
        <div className="rounded-xl border border-richBlack-900 bg-maastrichtBlue-1000 shadow-2xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-richBlack-900 p-4">
            <div className="flex items-center gap-3">
              <ProfileIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white text-lg font-semibold tracking-wide">
                Active Players
              </h3>
            </div>
            <button onClick={closeModal} className="h-6 w-6 min-w-6">
              <X className="hover:text-white h-5 w-5 text-steelTeal-1000 transition duration-200" />
            </button>
          </div>

          {/* Game Info */}
          <div className="flex items-center gap-4 p-4 max-sm:flex-col max-sm:text-center">
            <div className="h-16 w-16 overflow-hidden rounded-md">
              <Image
                src={activePlayer?.thumbnail || ''}
                alt="Game Thumbnail"
                width={64}
                height={64}
                className="h-full w-full object-cover"
              />
            </div>
            <div className="flex flex-col">
              <h4 className="text-white text-xl font-medium">
                {activePlayer?.gameName?.EN ?? activePlayer?.gameName}
              </h4>
            </div>
          </div>

          {/* Player List */}
          <div className="max-h-[300px] space-y-3 overflow-auto p-4 pt-0">
            {players?.length > 0 ? (
              players.map((player, idx) => (
                <div
                  key={idx}
                  className="bg-maastrichtBlue-950 hover:bg-maastrichtBlue-900 border-secondaryBorder flex items-center justify-between gap-3 rounded-lg border border-white-300 p-2 transition"
                >
                  {/* Avatar + Username */}
                  <div className="flex items-center gap-3">
                    <div className="relative h-10 w-10  rounded-full">
                      {player?.profileImage ? (
                        <Image
                          src={player?.profileImage}
                          alt={player?.username}
                          width={40}
                          height={40}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <ChatAvatar
                          profileImage={player.profileImage || player.imageUrl}
                          firstName={player?.firstName}
                          lastName={player?.lastName}
                          userName={player?.username}
                          avatarSize={40}
                          imageClassName="h-full w-full object-cover"
                        />
                      )}
                      <span className="border-maastrichtBlue-950 absolute bottom-0 right-0 h-2 w-2 rounded-full border-2 bg-green-400" />
                    </div>
                    <h5 className="text-white max-w-[160px] truncate text-sm font-medium">
                      {player?.username?.toLowerCase()}
                    </h5>
                  </div>

                  {/* Friend / Chat Actions */}
                  <div className="flex items-center gap-2">
                    {player?.areFriends ? (
                      <>
                        {/* Enter */}
                        <button onClick={closeModal}>
                          <Image
                            src={EnterIcon}
                            className="h-4 w-4 cursor-pointer"
                            width={16}
                            height={16}
                            alt="Enter"
                          />
                        </button>
                        {/* Chat */}
                        <button
                          onClick={() => {
                            openChat(player?.userId);
                            closeModal();
                          }}
                        >
                          <Image
                            src={ChatIcon}
                            className="h-4 w-4 cursor-pointer"
                            width={16}
                            height={16}
                            alt="Chat"
                          />
                        </button>
                      </>
                    ) : (
                      <>
                        {player?.userId !== userDetails?.id &&
                          !player?.areFriends && (
                            <>
                              {/* Send Friend Request */}
                              {player?.friendRequestStatus === false && (
                                <UserRoundPlus
                                  id="UserRoundPlusFriend"
                                  onClick={() =>
                                    handleSendFriendRequest(player?.userId)
                                  }
                                  className="h-[20px] w-[20px] cursor-pointer text-steelTeal-1000 hover:text-white-1000"
                                />
                              )}

                              {/* Pending Friend Request */}
                              {player?.friendRequestStatus === 'pending' && (
                                <div className="relative inline-block">
                                  <UserRound
                                    size={20}
                                    className="text-steelTeal-1000"
                                  />
                                  <Clock
                                    size={12}
                                    className="bg-black text-white absolute -bottom-1 -right-0.5 rounded-full"
                                  />
                                </div>
                              )}
                            </>
                          )}
                      </>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-sm text-white-700">
                No active players at the moment.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default GameSliderModel;
