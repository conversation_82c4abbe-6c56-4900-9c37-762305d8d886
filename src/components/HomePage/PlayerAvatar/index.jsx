'use client';

import React from 'react';
import ChatAvatar from '@/components/ChatAvatar';

import { userStatusColor } from '@/utils/helper';

function PlayerAvatar({
  players = [],
  isLoading,
  emptyMessage,
  handleOpenUserInfoModal,
  className = 'w-full overflow-hidden px-0 py-2 md:p-4',
  }) {
    return (
      <div className={className}>
      <div className="scrollbar-none flex gap-2 overflow-x-auto scroll-smooth md:gap-4">
        {isLoading ? (
          <p className="text-white">Loading...</p>
        ) : !players.length ? (
          <p className="text-white">{emptyMessage}</p>
        ) : (
          players.map((player) => (
            <div
              key={player.id}
              className="flex w-fit flex-none flex-col items-center text-base md:w-[90px]"
            >
              <div
                className="text-white relative h-[90px] w-[90px] cursor-pointer rounded-full border-none text-center max-md:h-[63px] max-md:w-[63px]"
                onClick={() => handleOpenUserInfoModal(player?.id || player?.userId)}
              >
                <ChatAvatar
                  profileImage={player?.avatar || player?.imageUrl}
                  firstName={player?.firstName}
                  lastName={player?.lastName}
                  userName={player?.name || player?.username}
                  imageClassName="rounded-full !h-full !w-full"
                  imageWidth=""
                  imageHeight=""
                  avatarSize={window?.innerWidth > 767 ? 87 : 60}
                  // fontSize="text-base"
                />
                {/* <Image
                  src={player?.avatar || Player1}
                  alt={player?.name}
                  className="rounded-full h-full w-full object-cover"
                  width={90}
                  height={90}
                /> */}
                <span
                  className={`absolute  bottom-[8px] right-[4px]  h-3 w-3 md:bottom-[0.625rem] md:right-[.5625rem]  ${userStatusColor(player?.status || 'AVAILABLE')} rounded-full`}
                ></span>
              </div>
              <p className="text-white mt-2 max-w-[67px] truncate whitespace-nowrap text-xs font-semibold md:max-w-[90px]">
                {player.name?.toLocaleLowerCase()}
              </p>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

export default PlayerAvatar;
