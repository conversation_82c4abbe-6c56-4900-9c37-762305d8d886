'use client';
import React from 'react';
import SportsIcon from '@/assets/images/svg-images/sports-icon.svg';
import LiveIcon from '@/assets/images/svg-images/live-icon.svg';
import SoccerIcon from '@/assets/images/svg-images/soccer-icon.svg';
import HorseRacing from '@/assets/images/svg-images/horse.svg';
import TennisIcon from '@/assets/images/svg-images/tennis-icon.svg';
import FifaIcon from '@/assets/images/svg-images/fifa-icon.svg';
import BasketballIcon from '@/assets/images/svg-images/basketball-icon.svg';
import IceHockey from '@/assets/images/svg-images/ice-hockey-icon.svg';
import VolleyBallIcon from '@/assets/images/svg-images/volleyball-icon.svg';
import RugbyIcon from '@/assets/images/svg-images/rugby-icon.svg';
import Image from 'next/image';
import useAuthStore from '@/store/useAuthStore';
import CarouselSection from '@/components/Common/CarouselSection';
import { useRouter } from 'next/navigation';
import useGeneralStore from '@/store/useGeneralStore';

export default function GameTabs({}) {
  const sports = [
    { label: 'LIVE', icon: LiveIcon },
    { label: 'SOCCER', icon: SoccerIcon },
    { label: 'HORCE RACING', icon: HorseRacing },
    { label: 'TENNIS', icon: TennisIcon },
    { label: 'FIFA', icon: FifaIcon },
    { label: 'BASKETBALL', icon: BasketballIcon },
    { label: 'ICE HOCKEY', icon: IceHockey },
    { label: 'VOLLEYBALL', icon: VolleyBallIcon },
    { label: 'RUGBY', icon: RugbyIcon },
  ];
  const { openMenu, openChat } = useGeneralStore();

  const router = useRouter();
  const { isAuthenticated } = useAuthStore((state) => state);

  const handleViewAllClick = () => {
    router.push('/sportsbook');
  };

  // Custom controls for the sportsbook title and icon
  const customControls = (
    <>
      <Image
        src={SportsIcon}
        className="h-5 w-5 object-cover"
        width={100}
        height={100}
      />
      <p className="bg-cardBorderGradient bg-clip-text text-[14px] font-semibold uppercase text-transparent">
        Sportsbook
      </p>
    </>
  );

  return (
    <>
      <div className="mb-10 mt-3 ">
        <CarouselSection
          navigationStyle="default"
          showGridIcon={false}
          showViewAll={true}
          viewAll="View All"
          onViewAllClick={handleViewAllClick}
          containerMargin=""
          emblaOptions={{
            loop: false,
            align: 'center',
            slidesToScroll: 'auto',
          }}
          showCustomControls={true}
          customControls={customControls}
        >
          {sports.map((sport, index) => (
            <div
              className={` flex-[0_0_calc(100%/9)]  py-1 pl-3 max-3xl:flex-[0_0_calc(100%/7)]
        
         max-xl:flex-[0_0_calc(100%/6)]         
        max-lg:flex-[0_0_calc(100%/6)] 
        max-lmd:flex-[0_0_calc(100%/5)]
        max-md:flex-[0_0_calc(100%/9)] max-sm:flex-[0_0_calc(100%/3)] max-sm:pl-[0.375rem] max-xs:flex-[0_0_calc(100%/3)]
        ${openMenu && openChat ? 'cas-1 max-xxl:flex-[0_0_calc(100%/8)] max-2xl:flex-[0_0_calc(100%/9)]' : openMenu ? 'cas-2 max-xxl:flex-[0_0_calc(100%/9)] max-2xl:flex-[0_0_calc(100%/9)]' : openChat ? 'cas-3 max-2xl:flex-[0_0_calc(100%/6)]' : 'cas-4 max-xxl:flex-[0_0_calc(100%/9)] max-2xl:flex-[0_0_calc(100%/8)]'}
                     `}
            >
              <button
                key={index}
                className="text-white mt-4 flex w-full flex-col  items-center justify-center gap-1 rounded-2xl  border border-gameTabBorder bg-BadgeGradientBg px-2 py-4   font-semibold transition hover:bg-[#3c361f]"
              >
                <span className="text-xl">
                  <Image
                    className="h-10 w-10"
                    src={sport.icon}
                    width={100}
                    height={100}
                  />
                </span>
                <span className="xl :text-base text-xs">{sport.label}</span>
              </button>
            </div>
          ))}
        </CarouselSection>
      </div>
    </>
  );
}
