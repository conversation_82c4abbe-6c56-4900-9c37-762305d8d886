'use client';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import HomeIcon from '@/assets/icons/HomeIcon';
import ForYouIcon from '@/assets/icons/ForYoyIcon';
import useGameStore from '@/store/useGameStore';
import { useCategoryQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import SearchIcon from '@/assets/icons/SearchIcon';
import FavoriteIcon from '@/assets/icons/Favorite';

export default function Gamefilter({
  onTabChange = () => {},
  setIcon = () => {},
  defaultTab = 'Lobby',
  showGameFilter = true,
  inputWidth = 'lg:max-w-[14.375rem]  max-sm:max-w-full',
}) {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef(null);
  const { isAuthenticated } = useAuthStore();
  const { data: categoryLabels } = useCategoryQuery();
  const tabsRef = useRef({});

  const {
    setSearchQuery,
    recentGames,
    setActiveTab: setActiveTabStore,
  } = useGameStore((state) => ({
    setSearchQuery: state.setSearchQuery,
    recentGames: state.recentGames,
    setActiveTab: state.setActiveTab,
  }));

  const TABS = useMemo(() => {
    if (!categoryLabels?.data?.categories) return null;
    const staticTabs = [
      { label: 'Lobby', icon: <HomeIcon size={16} /> },
      ...(isAuthenticated
        ? [{ label: 'For You', icon: <ForYouIcon size={16} /> }]
        : []),
    ];

    const categoryTabs =
      categoryLabels?.data?.categories
        ?.filter((cat) => {
          // Only categories meant for gameFilter
          const validForGameFilter =
            Array.isArray(cat.categoryFor) &&
            cat.categoryFor.includes('gameFilter');

          // Special handling for category 260
          if (cat.id === 260) {
            return isAuthenticated && recentGames.length > 0;
          }

          // Skip 255 for unauthenticated users
          if (cat.id === 255 && !isAuthenticated) return false;

          // Otherwise allow
          return validForGameFilter;
        })
        .map((cat) => ({
          label: cat.name?.EN || '',
          icon: cat.icon_url,
        })) || [];

    return [...staticTabs, ...categoryTabs];
  }, [categoryLabels, isAuthenticated, recentGames]);

  useEffect(() => {
    setActiveTab(defaultTab);
    setActiveTabStore(defaultTab);
  }, [defaultTab, setActiveTabStore]);

  useEffect(() => {
    if (!activeTab) return;
    if (activeTab === 'For You' || activeTab === 'Lobby') {
      setIcon(<ForYouIcon size={16} activeMenu />);
    } else {
      const tab = TABS?.find((t) => t.label === activeTab);
      if (tab) setIcon(tab.icon);
    }
  }, [activeTab, TABS, setIcon]);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    if (!activeTab || !tabsRef.current[activeTab] || !containerRef.current || !TABS) return;

    const tabElement = tabsRef.current[activeTab];
    const container = containerRef.current;

    const tabIndex = TABS.findIndex((tab) => tab.label === activeTab);
    const isFirstTab = tabIndex === 0;
    const isLastTab = tabIndex === TABS.length - 1;

    if (isFirstTab) {
      container.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
    } else if (!isLastTab) {
      const scrollLeft = tabElement.offsetLeft - container.offsetLeft;
      container.scrollTo({
        left: scrollLeft,
        behavior: 'smooth',
      });
    }
  }, [activeTab, TABS]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(localSearchQuery);
    }, 500);
    return () => clearTimeout(handler);
  }, [localSearchQuery, setSearchQuery]);

  const handleSearchInput = (e) => {
    setLocalSearchQuery(e.target.value);
  };
  let isDown = false;
  let startX;
  let scrollLeft;

  const handleMouseDown = (e) => {
    isDown = true;
    containerRef.current.classList.add('cursor-grabbing');
    startX = e.pageX - containerRef.current.offsetLeft;
    scrollLeft = containerRef.current.scrollLeft;
  };

  const handleMouseLeaveOrUp = () => {
    isDown = false;
    containerRef.current.classList.remove('cursor-grabbing');
  };

  const handleMouseMove = (e) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX - containerRef.current.offsetLeft;
    const walk = (x - startX) * 1.5; // multiplier controls speed
    containerRef.current.scrollLeft = scrollLeft - walk;
  };

  const placeholderText = !isMobile ? 'Search Filter' : 'Search';

  return (
    <div className="my-3 mb-6 flex flex-col  justify-between gap-6 md:flex-row max-lg:gap-2 max-sm:mb-3  max-sm:mt-0 max-sm:flex-col-reverse">
      {/* Tabs */}
      {showGameFilter && (
        <div
          ref={containerRef}
          className="flex h-11 w-full cursor-grab flex-nowrap overflow-x-auto rounded-xl bg-slateGray-700 p-1 shadow-tabsShadow [-ms-overflow-style:none] [scrollbar-width:none] md:space-x-3 [&::-webkit-scrollbar]:hidden"
          onMouseDown={handleMouseDown}
          onMouseLeave={handleMouseLeaveOrUp}
          onMouseUp={handleMouseLeaveOrUp}
          onMouseMove={handleMouseMove}
        >
          {TABS?.map((tab) => (
            <button
              key={tab.label}
              ref={(el) => (tabsRef.current[tab.label] = el)}
              onClick={() => {
                setActiveTab(tab.label);
                setActiveTabStore(tab.label);
                if (onTabChange) onTabChange(tab.label);
                setLocalSearchQuery('');
                setSearchQuery('');
                if (tab.label === 'For You') {
                  setIcon(<ForYouIcon size={16} activeMenu />);
                } else {
                  setIcon(tab.icon);
                }
              }}
              className={`flex min-w-[max-content] items-center gap-2 whitespace-nowrap rounded-[10px] px-2 py-1 text-[15px] font-semibold transition  ${
                activeTab === tab.label
                  ? 'bg-primaryBorder text-white-1000 [&_svg_path]:fill-white-1000'
                  : 'text-steelTeal-200'
              }`}
            >
              {typeof tab.icon === 'string' ? (
                <img
                  src={tab.icon}
                  alt={tab.label}
                  className="h-5 w-5 object-contain grayscale"
                />
              ) : (
                tab.icon
              )}
              {tab.label}
            </button>
          ))}
        </div>
      )}

      {/* Input */}
      <div className={`relative w-full max-sm:hidden   ${inputWidth}`}>
        <SearchIcon className="absolute left-2 top-3 h-5 w-5 [&>path]:stroke-steelTeal-200" />
        <input
          type="text"
          value={localSearchQuery}
          onChange={handleSearchInput}
          placeholder={placeholderText}
          className="text-white placeholder:text-white h-11  w-full rounded-[10px] border border-solid border-transparent bg-inputBgColor p-[10px] ps-9 text-base  font-normal leading-none placeholder:font-semibold  placeholder:opacity-75 focus:border focus:border-solid focus:border-borderColor-100 max-sm:text-base"
        />
      </div>
    </div>
  );
}
