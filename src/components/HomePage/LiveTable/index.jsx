'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Player1 from '@/assets/images/stock-images/player-1.png';
import FansbetCoin from '@/assets/images/svg-images/fansbet-coin.svg';
import { useTrasactionQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import Auth from '@/components/Auth';
import useModalStore from '@/store/useModalStore';
import useAuthModalStore from '@/store/useAuthModalStore';

// Define tabs with tableType
const TABS = [
  { label: 'Friends', tableType: 'friends' },
  { label: 'Public', tableType: 'public' },
  { label: 'Biggest Wins', tableType: 'biggest-win' },
];

const LiveTable = () => {
  const [activeTab, setActiveTab] = useState(TABS[1]);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal} = useAuthModalStore();

  const {
    data: transactionsData,
    isLoading,
    fetchNextPage,
    hasNextPage,
  } = useTrasactionQuery({ limit: 10, tableType: activeTab.tableType });

  return (
    <div className="my-[40px] text-white-1000">
      {/* Tabs */}
      <div className="mb-4 flex space-x-3 rounded-xl bg-slateGray-700 p-1 shadow-tabsShadow sm:w-fit">
        {TABS.map((tab) => (
          <button
            key={tab.label}
            onClick={() => {
              if (tab.tableType === 'friends' && !isAuthenticated) {
                setSelectedTab(0);
                localStorage.setItem('activeTab', 0);
                openModal(<Auth />);
                return;
              }
              setActiveTab(tab);
            }}
            className={`w-full whitespace-nowrap rounded-[10px] px-4 py-2 text-[15px] font-bold transition md:px-6 ${activeTab.label === tab.label
                ? 'bg-primaryBorder text-white-1000'
                : ' text-steelTeal-200'
              }`}
          >
            {tab.label}
          </button>
        ))}

      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="text-left text-[13px] font-semibold text-steelTeal-200">
              <th className="hidden whitespace-nowrap px-4 py-2 md:block">
                PLAYER
              </th>
              <th className="px-4 py-2">GAME</th>
              <th className="hidden whitespace-nowrap px-4 py-2 text-end md:block">
                BET AMOUNT
              </th>
              <th className="hidden px-4 py-2 text-end md:table-cell ">
                MULTIPLIER
              </th>
              <th className="px-4 py-2 text-end">PAYOUT</th>
            </tr>
          </thead>
          <tbody className="text-white text-sm [&>tr:nth-child(odd)>td]:bg-slateGray-700">
            {isLoading ? (
              <tr>
                <td colSpan={5} className="py-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : transactionsData?.pages?.[0]?.data?.length ? (
              transactionsData.pages.map((page) =>
                page.data.map((entry, index) => (
                  <tr key={entry.id || index}>
                    <td className="hidden px-4 py-3 font-semibold text-white-1000 first:rounded-l-md last:rounded-r-md md:table-cell">
                      <div className="hidden items-center gap-2 whitespace-nowrap md:flex">
                        <span className="h-9 w-9 overflow-hidden rounded-full">
                          <Image
                            src={entry?.profileUrl ?? Player1}
                            height={1000}
                            width={1000}
                            className="h-full w-full object-cover"
                          />
                        </span>
                        {entry.username}
                      </div>
                    </td>
                    <td className="gap-2 whitespace-nowrap px-4 py-3 first:rounded-l-md last:rounded-r-md">
                      <div className="flex items-center gap-2">
                        <Image
                          src={entry?.gameIconUrl}
                          alt={entry.game}
                          width={30}
                          height={24}
                          className="rounded"
                        />
                        <div className="flex flex-col gap-1">
                          <div>{entry.gameName?.EN || entry?.gameName}</div>
                          <div className="text-xs text-[#C3C4C7] md:hidden">
                            {entry.username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="hidden whitespace-nowrap px-4 py-3 first:rounded-l-md last:rounded-r-md md:table-cell ">
                      <div className="hidden items-center justify-end gap-1 text-white-1000 md:flex">
                        {entry.totalbetamount}
                        <span>
                          <Image
                            src={FansbetCoin}
                            alt={entry.gameName}
                            width={24}
                            height={24}
                            className="rounded"
                          />
                        </span>
                      </div>
                    </td>
                    <td className="hidden whitespace-nowrap px-4 py-3 text-end first:rounded-l-md last:rounded-r-md md:table-cell">
                      <div className="hidden justify-end md:flex">
                        {entry.multiplier}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 first:rounded-l-md last:rounded-r-md">
                      <div className="flex items-center justify-end gap-1">
                        {entry.payout}
                        <span>
                          <Image
                            src={FansbetCoin}
                            alt={entry.gameName}
                            width={24}
                            height={24}
                            className="rounded"
                          />
                        </span>
                      </div>
                    </td>
                  </tr>
                )),
              )
            ) : (
              <tr>
                <td colSpan={5} className="py-4 text-center">
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>

        {/* {hasNextPage && (
          <div className="flex justify-center mt-4">
            <button
              onClick={() => fetchNextPage()}
              className="px-6 py-2 bg-primaryBorder rounded-md font-semibold"
            >
              Load More
            </button>
          </div>
        )} */}
      </div>
    </div>
  );
};

export default LiveTable;
