'use client';

import { useLogOutMutation } from '@/reactQuery/authQuery';
import useAuthStore from '@/store/useAuthStore';
import { removeAccessToken } from '@/utils/helper';

export default function useHelperHook() {
  const user = useAuthStore((state) => state);

  const mutation = useLogOutMutation({
    onSuccess: () => {
      user.logout();
      removeAccessToken()
      localStorage.clear();
    },
    onError: (error) => {
      console.log('error', error);
    },
  });

  const logout = () => {
    mutation.mutate();
  };

  const clearUserAuth = () => {
    user.logout();
    localStorage.clear();
  };

  return {
    logout,
    clearUserAuth,
    isLoading: mutation?.isPending,
  };
}
