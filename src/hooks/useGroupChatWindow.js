import { useEffect } from 'react';
import useGroupChatStore from '@/store/useGroupChatStore';
import { getAccessToken } from '@/utils/helper';
import { io } from 'socket.io-client';
import usePrivateChatStore from '@/store/usePrivateChatStore';

const useGroupChatWindow = ({ isGroupChat = false, isGroupPage = false, isPrivatePage = false, isPrivateChat = false }) => {
  const {
    appendGroupChat,
    groupName,

    appendGroupChatForPage,
  } = useGroupChatStore((state) => state);

  const { appendPrivateChat, isPrivateChatOpen, userId, appendPrivateChatForPage } = usePrivateChatStore(
    (state) => state,
  );
  const accessToken = getAccessToken();
  useEffect(() => {
    const handleNewMessage = (newMessage) => {
      if (
        !!!useGroupChatStore
          .getState()
          .groupChatForPage?.find((msg) => msg.id === newMessage?.data?.id)
      ) {
        appendGroupChatForPage(newMessage?.data);
      }
      if (
        !!!useGroupChatStore
          .getState()
          .groupChat?.find((msg) => msg.id === newMessage?.data?.id)
      ) {
        appendGroupChat(newMessage?.data);
      }
    };

    let socket = null;
    if (isGroupChat || isGroupPage) {
      socket = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/group`, {
        query: { groupName },
        transports: ['websocket'],
        withCredentials: true,
        path: '/api/socket',
        auth: {
          token: accessToken,
        },
      });
      if (!socket?.connected) socket.connect();
      socket.on('GROUP_CHAT', handleNewMessage);
    }
    return () => {
      if (socket) {
        socket.off('GROUP_CHAT', handleNewMessage);
        socket.disconnect();
      }
    };
  }, [groupName, accessToken, isGroupChat, isGroupPage, appendGroupChat, appendGroupChatForPage]);

  useEffect(() => {
    console.log("🔄 Private chat effect ran", { isPrivateChatOpen, userId, accessToken });
    const handleNewMessage = (newMessage) => {
      console.log("📩 New private message:", newMessage);

      // Center screen chat (privateChatForPage)
      if (
        !!!usePrivateChatStore
          .getState()
          .privateChatForPage?.find((msg) => msg.id === newMessage?.id)
      ) {
        appendPrivateChatForPage(newMessage);
      }
      console.log(
        "✅ After append, privateChatForPage:",
        usePrivateChatStore.getState().privateChatForPage
      );

      // Right side recent items (privateChat)
      if (
        !!!usePrivateChatStore
          .getState()
          .privateChat?.find((msg) => msg.id === newMessage?.id)
      ) {
        appendPrivateChat(newMessage);
      }
    };

    let socket = null;
    if ((isPrivateChat || isPrivatePage)
      // || (isPrivateChatOpen && userId && accessToken)
    ) {
      // If user changes, disconnect old socket
      // if (socket.current && socket.current.userId !== userId) {
      //   socket.current.disconnect();
      //   socket.current = null;
      // }

      // If no socket, create new one
      // if (!socket.current) {
      socket = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/private`, {
        query: { userId: String(userId).trim() },
        path: '/api/socket',
        transports: ['websocket'],
        withCredentials: true,
        auth: { token: accessToken },
      });

      // attach userId so we can check later
      // socket.userId = userId;
      if (!socket?.connected) socket.connect();
      socket.on('connect', () => {
        console.log('✅ Private chat socket connected:', userId);
      });

      socket.on('private_chat', handleNewMessage);

      socket.on('disconnect', () => {
        console.log('❌ Private chat socket disconnected:', userId);
      });

      // socket.current = socket;
      // }
    }

    return () => {
      if (socket) {
        socket.off('private_chat', handleNewMessage);
        socket.disconnect();
        // socket.current.off('private_chat', handleNewMessage);
      }
    };
  },
    // [userId, accessToken, isPrivateChatOpen, appendPrivateChat, appendPrivateChatForPage, isPrivateChat, isPrivatePage]
    [userId, accessToken, appendPrivateChat, appendPrivateChatForPage, isPrivateChat, isPrivatePage]
  );

};

export default useGroupChatWindow;
