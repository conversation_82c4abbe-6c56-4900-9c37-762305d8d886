// hooks/useLivePlayers.js
import { useEffect, useState } from 'react';
import { livePlayerSocket } from '@/utils/socket';
import usePublicChatsStore from '@/store/usePublicChatStore';
import useAuthStore from '@/store/useAuthStore';
import usePlayerStore from '@/store/usePlayerStore';
import { useGetAllLivePlayersQuery } from '@/reactQuery/generalQueries';
import {
  useActiveGroupMyGroups,
  useActiveGroupPublic,
  useActivePlayerMyFriends,
  useActivePlayerPublic,
} from '@/reactQuery/gamesQuery';
import { useQueryClient } from '@tanstack/react-query';
import useActiveGroupStore from '@/store/useActiveGroupStore';

const useActiveGroups = () => {
  const queryClient = useQueryClient();


  const { isAuthenticated } = useAuthStore((state) => state);
  const {
    setMyGroups,
    setMyGroupsLoading,
    setPublicGroups,
    setPublicGroupsLoading,
    setActiveTab,
  } = useActiveGroupStore((state) => state);

  const { data: myGroups, isLoading: myPlayerLoading, refetch: refetchMyGroups } =
    useActiveGroupMyGroups({enabled:isAuthenticated});

  const {
    data: publicUsers,
    isLoading: publicUsersLoading,
    refetch: refetchPublicGroups,
  } = useActiveGroupPublic({enabled:true});

  useEffect(() => {
    if (myGroups) {
      setMyGroups(myGroups);
      setMyGroupsLoading(myPlayerLoading);
      
    }
  }, [myGroups]);

  useEffect(() => {
    if (publicUsers) {
      setPublicGroups(publicUsers);
      setPublicGroupsLoading(publicUsersLoading);
    }
  }, [publicUsers]);

  return {
    myPlayerLoading,
    publicUsersLoading,
    refetchPublicGroups,
    refetchMyGroups
  };
};

export default useActiveGroups;
