'use client';

import { toast } from 'react-hot-toast';
import { useState } from 'react';

import useAuthStore from '@/store/useAuthStore';
import { useSignInMutation } from '@/reactQuery/authQuery';
import { setAccessToken, setLoginToken } from '@/utils/helper';
import useModalStore from '@/store/useModalStore';
import { useRouter } from 'next/navigation';

const useSignIn = () => {
  const [error, setError] = useState(null);
  const { setIsAuthenticated, setUserDetails, setUserWallet } = useAuthStore(
    (state) => state,
  );
  const { clearModals } = useModalStore((state) => state);
  const router = useRouter();
  const mutation = useSignInMutation({
    onSuccess: (response) => {
      if (!response?.data?.authEnable) {
        setIsAuthenticated(true);
        clearModals();
        localStorage.setItem('isAuthenticated', true);
        setUserDetails(response?.data?.user);
        setUserWallet(
          response?.data?.user?.wallets.find((wallet) => wallet.default),
        );
        if (response?.data?.accessToken) {
          setAccessToken(response.data.accessToken);
          router.push('/');
          // setLoginToken(response.data.accessToken);
        }
        // toast.success("Signed in successfully!");
        if (response?.data?.user?.chestReceived) {
          toast.success('Congratulations!! You have recieved Chest.');
        }
      } else {
        setUserDetails(response?.data);
      }
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign in';
      setError(message);
      toast.error(message);
    },
  });

  const signIn = (userName, password) => {
    mutation.mutate({ userName, password: btoa(password) });
  };

  return { signIn, error, isLoading: mutation.isPending };
};

export default useSignIn;
