import {
    useGetSubCategoryLobbyGamesQuery,
  } from '@/reactQuery/gamesQuery';
  import useAuthStore from '@/store/useAuthStore';
  
  export default function useLobbyGames({ search, userId }) {
    const { isAuthenticated } = useAuthStore((state) => state);
    const {
      data: lobbyGames,
      isLoading: isLobbyGamesLoading,
      isSuccess,
    } = useGetSubCategoryLobbyGamesQuery({
      enabled: isAuthenticated,
      search,
      userId,
    });
    return {
      lobbyGames,
      isLobbyGamesLoading,
      isSuccess,
    };
  }
  