import { useGamesListQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';

function useCategory({ limit, categoryName, pageNo, gameName, enabled, providers, sortBy }) {
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);

  const queryResult = useGamesListQuery({
    enabled,
    limit,
    categoryName,
    pageNo,
    gameName,
    providers,
    sortBy,
    ...(isAuthenticated ? { userId: userDetails?.id } : {}),
  });

  const {
    data,
    isLoading: gamesLoading,
    refetch,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    dataUpdatedAt,
  } = queryResult;

  return {
    casinoGames: data,
    gamesLoading,
    refetch,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    dataUpdatedAt
  };
}

export default useCategory;
