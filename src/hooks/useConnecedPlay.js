import useActiveGroupStore from '@/store/useActiveGroupStore';
import { getConnectedPlay } from '@/utils/apiCalls';
import { useQueryClient } from '@tanstack/react-query';

const useConnectedPlay = () => {
  const queryClient = useQueryClient();
  const { setConnectedPlay } = useActiveGroupStore();

  const handleConnectedPlay = async (newParams) => {
    // separate API params vs extra params
    const { scroll, ...apiParams } = newParams;

    const result = await queryClient.fetchQuery({
      queryKey: ['GET_CONNECTED_PLAY', apiParams], // ✅ only pass real api params
      queryFn: () => getConnectedPlay({ ...apiParams }),
      select: (data) => data?.data?.response,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      staleTime: 0,
    });
    setConnectedPlay({ ...result?.data?.response,scroll });

    return result;
  };

  return {
    handleConnectedPlay,
  };
};

export default useConnectedPlay;
