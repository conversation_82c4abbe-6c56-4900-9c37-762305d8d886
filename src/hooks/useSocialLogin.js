import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useGoogleLogin } from '@react-oauth/google';
import useSignUp from './useSignUp';
import { useFacebookLoginMutation } from '@/reactQuery/authQuery';
import useModalStore from '@/store/useModalStore';
import ForcedEmailModal from '@/components/Models/ForcedEmailModal';

const useSocialLogin = () => {
  const [isLoading, setIsLoading] = useState({
    google: false,
    facebook: false,
    discord: false,
    twitch: false,
  });
  const { openModal } = useModalStore((state) => state);
  const { googleMutation } = useSignUp();

  const setProviderLoading = (provider, loading) => {
    setIsLoading(prev => ({
      ...prev,
      [provider]: loading,
    }));
  };

  // FACEBOOK SDK INIT
  useEffect(() => {
    window.fbAsyncInit = function () {
      FB.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v11.0',
      });
      FB.AppEvents.logPageView();
    };

    (function (d, s, id) {
      if (d.getElementById(id)) return;
      const js = d.createElement(s);
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      d.getElementsByTagName(s)[0].parentNode.insertBefore(js, d.getElementsByTagName(s)[0]);
    })(document, 'script', 'facebook-jssdk');
  }, []);

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      if (tokenResponse) {
        const userData = {
          credential: tokenResponse.access_token,
          isSignup: true,
          isTermsAccepted: true,
        };
        googleMutation.mutate(userData);
      }
    },
    onError: (errorResponse) => {
      console.error('Google login error:', errorResponse);
      toast.error('Google login failed');
    },
  });

  const facebookMutation = useFacebookLoginMutation({
    onSuccess: (response) => {
      console.log(response, ':::::::::facebook response');
    },
    onError: (error) => {
      console.log(error, '::::::::::::facebook error');
    },
  });

  const handleFacebookLogin = async () => {
    setProviderLoading('facebook', true);
    try {
      FB.login(
        function (response) {
          if (response && response.authResponse && response.authResponse.userID) {
            FB.api(
              `/${response.authResponse.userID}`,
              { fields: ['first_name', 'last_name', 'email'] },
              function (_response) {
                const userData = {
                  firstName: _response.first_name,
                  lastName: _response.last_name,
                  userId: _response.id,
                  email: _response.email,
                  isSignup: true,
                  isForceEmail: false,
                };

                if (_response?.email) {
                  facebookMutation.mutate(userData);
                } else {
                  openModal(<ForcedEmailModal userData={userData} />);
                }
              },
            );
          }
        },
        { scope: 'public_profile,email' }
      );
    } catch (error) {
      console.error('Facebook login error:', error);
      toast.error('Facebook login failed');
    } finally {
      setProviderLoading('facebook', false);
    }
  };

  const handleDiscordLogin = () => {
    try {
      setProviderLoading('discord', true);
      const discordAuthUrl = `https://discord.com/oauth2/authorize?client_id=${process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(process.env.NEXT_PUBLIC_DISCORD_REDIRECT_URI)}&scope=${process.env.NEXT_PUBLIC_DISCORD_SCOPE}`;
      const width = 500;
      const height = 600;
      const left = (window.innerWidth - width) / 2;
      const top = (window.innerHeight - height) / 2;
  
      window.open(
        discordAuthUrl,
        '_self',
        `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,status=yes`,
      );
  
      // window.open(discordAuthUrl, '_self');
    } catch (error) {
      console.error('Discord login error:', error);
      toast.error('Discord login failed');
      setProviderLoading('discord', false);
    }
  };

  const handleTwitchLogin = () => {
    try {
      setProviderLoading('twitch', true);
      const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
      const redirectUri = process.env.NEXT_PUBLIC_TWITCH_REDIRECT_URI;
      const scope = 'openid user:read:email user:read:follows';

      const authUrl = `https://id.twitch.tv/oauth2/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}`;
      window.location.href = authUrl;
    } catch (error) {
      console.error('Twitch login error:', error);
      toast.error('Twitch login failed');
      setProviderLoading('twitch', false);
    }
  };

  return {
    isLoading,
    handleGoogleLogin,
    handleFacebookLogin,
    handleDiscordLogin,
    handleTwitchLogin,
  };
};

export default useSocialLogin;
