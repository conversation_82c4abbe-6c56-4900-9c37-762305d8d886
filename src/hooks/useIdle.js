"use client";
import { useEffect, useState } from "react";

export function useIdle(timeout = 300000) { // default 5 min
  const [isIdle, setIsIdle] = useState(false);

  useEffect(() => {
    let idleTimer;

    const resetTimer = () => {
      setIsIdle(false);
      clearTimeout(idleTimer);
      idleTimer = setTimeout(() => setIsIdle(true), timeout);
    };

    const events = ["mousemove", "keydown", "mousedown", "scroll", "touchstart"];
    events.forEach((evt) => window.addEventListener(evt, resetTimer));

    resetTimer();

    return () => {
      events.forEach((evt) => window.removeEventListener(evt, resetTimer));
      clearTimeout(idleTimer);
    };
  }, [timeout]);

  // Reload page when idle
  useEffect(() => {
    if (isIdle) {
      window.location.reload();
    }
  }, [isIdle]);

  return isIdle;
}
