import { useQuery } from '@tanstack/react-query';
import { getPlayerDetails } from '@/utils/apiCalls';

const useFriendDetails = ({ username, enabled = true } = {}) => {

  const { data, error, isLoading, refetch } = useQuery({
    queryKey: ['friendDetails', username],
    queryFn: () =>
      getPlayerDetails({ username }),
    enabled: (!!username) && enabled,
    select: (response) => response?.data?.userData || {},
    retry:false
  });


  return { data, error, isLoading, refetch };
};

export default useFriendDetails;
