import CallPopup from '@/components/Models/CallPopup';
import GroupCallPopup from '@/components/Models/GroupCallPopup';
import { rtc as privateCallRTC } from '@/hooks/usePrivateCall';
import useAuthStore from '@/store/useAuthStore';
import useCallModalStore from '@/store/useCallModalStore';
import useCallStore from '@/store/useCallStore';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { getAccessToken, removeAccessToken } from '@/utils/helper';
import {
  chatRoomSocket,
  livePlayerSocket,
  playerActivitySocket,
  voiceChatRoom,
  walletSocket,
} from '@/utils/socket';
import useAudioPlayer from '@/utils/useAudioPlayer';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';
import toast from 'react-hot-toast';
import useActiveGroups from './useActiveGroup';
import useActivePlayers from './useActivePlayer';
import useConnectedPlay from './useConnecedPlay';
import useHelperHook from './useHelperHook';
import { useQueryClient } from '@tanstack/react-query';
import { useGreenBonusClaim } from '@/reactQuery/gamesQuery';
import { PartyPopper } from 'lucide-react';
import useActiveGroupStore from '@/store/useActiveGroupStore';

export const useSocketManager = () => {
  const queryClient = useQueryClient();
  const accessToken = getAccessToken();
  const { setCallStartTime, setCallDuration, setToggleMuted } = useCallStore();
  const { audio, playAudio } = useAudioPlayer();
  const {
    closeModal: closeCallModal,
    openModal: openCallModal,
    setIsMinimized,
  } = useCallModalStore((state) => state);
  const { handleConnectedPlay } = useConnectedPlay();
  const { setVoiceCall, voiceCall, updateGroupCallMembers } = useVoiceCallStore(
    (state) => state,
  );
  const {
    coin,
    setCoin,
    isAuthenticated,
    setUserDetails,
    setUserWallet,
    userWallet,
    userDetails,
  } = useAuthStore((state) => state);
  const router = useRouter();
  const { setIsCallActive } = usePrivateChatStore((state) => state);
  const { refetchPublicUsers, refetchMyFriends } = useActivePlayers();
  const { refetchPublicGroups, refetchMyGroups } = useActiveGroups();
  const { setGreenBonusData } = useGreenBonusStore((state) => state);
  const { clearUserAuth } = useHelperHook();
  const { clearModals } = useModalStore((state) => state);

  const onCoinUpdate = (data) => {
    setUserWallet(data?.data);
  };
  const onChatRoomCall = (data) => {
    const { isCallActive } = useGroupChatStore.getState();
    const { isCallActive: isPrivateCallActive } =
      usePrivateChatStore.getState();

    if (!isCallActive && !isPrivateCallActive) {
      setIsMinimized(false);
      // openModal(<CallPopup />)
      setCallStartTime(null), setCallDuration(0);
      setToggleMuted(false);
      playAudio();
      openCallModal(<CallPopup />);
      setVoiceCall({
        channelName: data?.data?.channelName,
        role: data?.data?.role,
        callLogId: data?.data?.callLogId,
        userId: data?.data?.userId,
        username: data?.data?.username,
        profileImage: data?.data?.profileImage,
        isOneToOneCall: true,
      });
      return;
    } else {
      toast(`${data?.data?.username} is Calling you`, {
        icon: '📞', // or 📞 or 🟢
        duration: 3000,
        style: {
          background: 'yellow', // info-blue
          color: 'black',
          fontWeight: '500',
        },
      });
      return;
    }
  };
  const onFriendCallAccepted = (data) => {
    console.log('Friend call accepted', data);
    handleConnectedPlay({
      callLogId: data?.data?.callLogId,
      isOneToOneCall: true,
      scroll:true
    });
  };
  const onGroupCallMembers = (data) => {
    const voiceCall = useVoiceCallStore.getState().voiceCall;
    console.log('voice call', voiceCall, data?.data);
    if (voiceCall != null) {
      updateGroupCallMembers([...data?.data]);
    }
  };
  const onDeclineCall = async () => {
    const isCallActive = usePrivateChatStore.getState().isCallActive;
    const { isCallActive: isGroupCallActive } = useGroupChatStore.getState();
    if (!isGroupCallActive) {
      try {
        audio.pause();
        audio.currentTime = 0;

        if (!isCallActive) {
          closeCallModal();
          setVoiceCall(null);
          closeCallModal();
          useActiveGroupStore.setState({ connectedPlay: {} });
          return;
        }
        setVoiceCall(null);
        useActiveGroupStore.setState({ connectedPlay: {} });

        privateCallRTC.localAudioTrack?.close();
        await privateCallRTC.client.leave();
      } catch (error) {
        console.error('Error disconnecting call:', error);
      } finally {
        usePrivateChatStore.getState().setIsCallActive(false);
        closeCallModal();
        useActiveGroupStore.setState({ connectedPlay: {} });
      }
    }
  };

  const onNotAttendedVoiceCall = async () => {
    const { isCallActive } = usePrivateChatStore.getState();
    const { isCallActive: isGroupCallActive } = useGroupChatStore.getState();
    if (!isGroupCallActive) {
      try {
        if (!isCallActive) {
          closeCallModal();
          audio.pause();
          audio.currentTime = 0;
          return;
        }
        privateCallRTC.localAudioTrack?.close();
        await privateCallRTC.client.leave();

        setVoiceCall(null);
        setIsCallActive(false);
      } catch (error) {
        console.log(error, 'Error in not attended voice call');
      } finally {
        audio.pause();
        audio.currentTime = 0;
        // audio.pause();
        // audio.currentTime = 0;
      }
    }
  };

  const onHandleActivePlayer = (data) => {
    refetchPublicUsers(), refetchMyFriends();
  };
  const onHandleActiveGroup = (data) => {
    const voiceCall = useVoiceCallStore.getState().voiceCall;
    const isGroupCallActive = useGroupChatStore.getState().isCallActive;
    setTimeout(() => {
      // if (data?.data?.games?.length != 0) {
      refetchPublicGroups();
      refetchMyGroups();
      // }

      if (isGroupCallActive && voiceCall?.groupId && voiceCall?.callLogId) {
        handleConnectedPlay({
          groupId: voiceCall?.groupId,
          callLogId: voiceCall?.callLogId,
          scroll:false
        });
      }
    }, 3000);
  };

  const onRestrictUser = () => {
    setUserDetails({ ...userDetails, isRestrict: true });
    toast.error('You are restricted, Please contact administrator');
    router.push('/');
  };
  const onUnRestrictUser = () => {
    setUserDetails({ ...userDetails, isRestrict: false });
    toast.error('You are now unrestricted');
    router.push('/');
  };

  const onLogout = () => {
    // logout();
    window.dispatchEvent(new Event('logout'));
    // window.location.reload();
    // router.push('/');
    clearUserAuth();
    localStorage.clear();
    clearModals();
    router.push('/');
  };

  const onRemainingthresold = (data) => {
    setGreenBonusData(data.data);
  };

  const handleWalletUpdate = useCallback(
    (data) => {
      if (data?.inActive) {
        toast.error(data?.message);
        removeAccessToken();
        localStorage.clear();
        queryClient.removeQueries();
        router.push('/');
      }
      setUserWallet(data);
    },
    [setUserWallet],
  );

  const safelyConnectSocket = useCallback((socket) => {
    if (socket && !socket.connected) {
      socket.connect();
    }
  }, []);
  const cliamGreenBonusMutation = useGreenBonusClaim({
    onSuccess: (response) => {
      toast('Bonus Claimed Successfully!', {
        icon: <PartyPopper size={20} />,
      });
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Failed to claim green bonus';
      setError(message);
      toast.error(message);
    },
  });

  const isClaimGreenBonusCalled = (data) => {
    cliamGreenBonusMutation.mutate(data);
  };
  useEffect(() => {
    const handleActiveGroup = (data) => onHandleActiveGroup(data);
    const handleActivePlayer = (data) => onHandleActivePlayer(data);
    const handleIncomingMsgData = () =>{
      queryClient.invalidateQueries({ queryKey: ['GET_INCOMING_MESSAGE_LIST'] });
    }

    if (isAuthenticated) {
      // Set socket authentication
      walletSocket.auth = { token: accessToken };
      chatRoomSocket.auth = { token: accessToken };
      playerActivitySocket.auth = { token: accessToken };
      // Connect sockets
      //   walletSocket.connect();
      //   livePlayerSocket.connect();
      //   chatRoomSocket.connect();
      //   playerActivitySocket.connect();

      safelyConnectSocket(walletSocket);
      safelyConnectSocket(livePlayerSocket);
      safelyConnectSocket(chatRoomSocket);
      safelyConnectSocket(playerActivitySocket);
      const handleRestrictUser = (data) => onRestrictUser(data);
      const handleRemainingThreshold = (data) => onRemainingthresold(data);
      const handleUnrestrictUser = (data) => onUnRestrictUser(data);
      const handleCoinUpdate = (data) => onCoinUpdate(data);
      const handleLogout = (data) => onLogout(data);
      const handleChatRoomCall = (data) => onChatRoomCall(data);
      //   const handleGroupChatCall = (data) => onGroupChatCall(data);
      const handleDeclinePersonalCall = (data) => onDeclineCall(data);
      const handleNotAttendedVoiceCall = (data) => onNotAttendedVoiceCall(data);
      const handleGroupGroupMembers = (data) => onGroupCallMembers(data);
      const handleClaimGreenBonus = (data) => isClaimGreenBonusCalled(data);
      const handleCallAccepted = (data) => onFriendCallAccepted(data);
      walletSocket.on('USER_RESTRICT', handleRestrictUser);
      walletSocket.on('REMAINING_THRESHOLD', handleRemainingThreshold);
      walletSocket.on('CLAIM_GREEN_BONUS', handleClaimGreenBonus);
      walletSocket.on('MESSAGE_REQUEST', handleIncomingMsgData);


      //   walletSocket.on('CLAIM_GREEN_BONUS', () => {}); // No handler was passed before
      walletSocket.on('USER_UNRESTRICT', handleUnrestrictUser);
      walletSocket.on('USER_WALLET_BALANCE', handleCoinUpdate);
      walletSocket.on('wallet', handleWalletUpdate);

      walletSocket.on('USER_BAN', handleLogout);
      walletSocket.on('PLAYER_LOGOUT', handleLogout);

      chatRoomSocket.on('PERSONAL_VOICE_CHAT_CHANNEL', handleChatRoomCall);
      //   chatRoomSocket.on('GROUP_VOICE_CHAT_CHANNEL', handleGroupChatCall);
      chatRoomSocket.on(
        'DECLINE_PERSONAL_VOICE_CHAT_CHANNEL',
        handleDeclinePersonalCall,
      );
      chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', handleNotAttendedVoiceCall);
      chatRoomSocket.on('GROUP_VOICE_CHAT_MEMBER', handleGroupGroupMembers);

      playerActivitySocket.on('MY_FRIENDS_PLAYER_ACTIVITY', handleActivePlayer);
      playerActivitySocket.on('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);

      chatRoomSocket.on('ATTENDED_VOICE_CALL', handleCallAccepted);
      // ✅ Cleanup
      return () => {
        walletSocket.off('USER_RESTRICT', handleRestrictUser);
        walletSocket.off('REMAINING_THRESHOLD', handleRemainingThreshold);
        walletSocket.off('CLAIM_GREEN_BONUS'); // no handler to remove
        walletSocket.off('USER_UNRESTRICT', handleUnrestrictUser);
        walletSocket.off('USER_WALLET_BALANCE', handleCoinUpdate);
        walletSocket.off('USER_BAN', handleLogout);
        walletSocket.off('PLAYER_LOGOUT', handleLogout);

        chatRoomSocket.off('PERSONAL_VOICE_CHAT_CHANNEL', handleChatRoomCall);
        // chatRoomSocket.off('GROUP_VOICE_CHAT_CHANNEL', handleGroupChatCall);
        chatRoomSocket.off(
          'DECLINE_PERSONAL_VOICE_CHAT_CHANNEL',
          handleDeclinePersonalCall,
        );
        chatRoomSocket.off(
          'NOT_ATTENDED_VOICE_CALL',
          handleNotAttendedVoiceCall,
        );
        chatRoomSocket.off('GROUP_VOICE_CHAT_MEMBER', handleGroupGroupMembers);

        playerActivitySocket.off(
          'MY_FRIENDS_PLAYER_ACTIVITY',
          handleActivePlayer,
        );
        playerActivitySocket.off('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);
        chatRoomSocket.off('ATTENDED_VOICE_CALL', handleCallAccepted);
        walletSocket.off('MESSAGE_REQUEST', handleIncomingMsgData);

        // Disconnect all
        walletSocket.disconnect();
        livePlayerSocket.disconnect();
        chatRoomSocket.disconnect();
        playerActivitySocket.disconnect();
      };
    } else {
      safelyConnectSocket(playerActivitySocket);
      playerActivitySocket.on('MY_FRIENDS_PLAYER_ACTIVITY', handleActivePlayer);

      playerActivitySocket.on('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);
      return () => {
        playerActivitySocket.off(
          'MY_FRIENDS_PLAYER_ACTIVITY',
          handleActivePlayer,
        );
        playerActivitySocket.off('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);
      };
    }
  }, [isAuthenticated, accessToken]);
};
