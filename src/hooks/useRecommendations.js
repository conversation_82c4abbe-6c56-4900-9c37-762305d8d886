import { useRecommendationsQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from "@/store/useAuthStore";
import useGameStore from '@/store/useGameStore';

function useRecommendations({ limit, enabled }) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const searchQuery = useGameStore((state) => state.searchQuery);

  const queryResult = useRecommendationsQuery({
    enabled: isAuthenticated && enabled,
    limit,
    gameName: searchQuery
  });

  const {
    data,
    isLoading: gamesLoading,
    refetch,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = queryResult;

  const games =
    data?.pages?.flatMap((page) => page?.data?.games || []) || [];

  return {
    data: games,
    gamesLoading,
    refetch,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  };
}
export default useRecommendations
