'use client';

import axios from 'axios';
import { toast } from 'react-hot-toast';
import { getAccessToken, removeAccessToken, setAccessToken } from './helper';

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  withCredentials: true,
});

// axiosInstance.interceptors.request.use(
//   (config) => config,
//   (error) => Promise.reject(error),
// );
axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken();
    if (accessToken) {
      config.headers['authorization'] = accessToken;
      // config.cancelToken = cancelSource.token;
    }
    // Add the cancel token to each request
    return config;
  },
  (error) => Promise.reject(error),
);
// axiosInstance.interceptors.response.use(
//   (res) => res.data,
//   (error) => {
//     const status = error.response?.status;
//     const errorCode = error?.response?.data?.errors?.[0]?.errorCode;

//     if (status === 403 || status === 401) {
//       toast.error('Your session has expired. Please log in again.');
//       window.dispatchEvent(new Event('logout'));
//       localStorage.clear();
//       removeAccessToken();
//       window.location.reload();
//     }

//     return Promise.reject(error);
//   },
// );

axiosInstance.interceptors.response.use(
  (res) => {
    const accessToken = res.headers.get("accessToken") || res.headers.get("Accesstoken");
    if (accessToken) {
      setAccessToken(accessToken);
    }
    return res.data;
  },
  (error) => {
    const status = error.response?.status;
    const errorCode = error?.response?.data?.errors?.[0]?.errorCode;

    if (status === 403 || status === 401) {
      // toast.error('Your session has expired. Please log in again.');
      window.dispatchEvent(new Event('logout'));
      localStorage.clear();
      removeAccessToken();
      window.location.reload();
    }

    // If the error was due to cancellation, rethrow it as is
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    return Promise.reject(error);
  },
);
const makeRequest = async (
  url,
  method,
  data = {},
  params = {},
  headers = { 'Content-Type': 'application/json' },
) => {
  return axiosInstance({ url, method, data, params, headers });
};

const requestHelper = (method) => (url, data, params, headers) =>
  makeRequest(url, method, data, params, headers);

export const getRequest = requestHelper('GET');
export const postRequest = requestHelper('POST');
export const putRequest = requestHelper('PUT');
export const deleteRequest = requestHelper('DELETE');