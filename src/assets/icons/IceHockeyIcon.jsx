import React from 'react';

const IceHockeyIcon = () => {
  return (
    <svg
      width="24"
      height="14"
      viewBox="0 0 24 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.619851 2.58633C0.136746 3.03051 0 3.3967 0 3.66667C0 3.93663 0.136746 4.30282 0.619851 4.74701C1.10381 5.19197 1.8593 5.63866 2.87716 6.03449C4.90653 6.82369 7.77805 7.33333 11 7.33333C14.222 7.33333 17.0935 6.82369 19.1228 6.03449C20.1407 5.63866 20.8962 5.19197 21.3801 4.74701C21.8633 4.30282 22 3.93663 22 3.66667C22 3.3967 21.8633 3.03051 21.3801 2.58633C20.8962 2.14136 20.1407 1.69467 19.1228 1.29884C17.0935 0.509641 14.222 0 11 0C7.77805 0 4.90653 0.509641 2.87716 1.29884C1.8593 1.69467 1.10381 2.14136 0.619851 2.58633ZM22 6.79772C21.3835 7.21886 20.6526 7.58551 19.8477 7.8985C17.5339 8.7983 14.4055 9.33333 11 9.33333C7.59453 9.33333 4.46605 8.7983 2.15227 7.8985C1.34742 7.58551 0.616476 7.21886 0 6.79771V10.3333C0 10.6033 0.136746 10.9695 0.619851 11.4137C1.10381 11.8586 1.8593 12.3053 2.87716 12.7012C4.90653 13.4904 7.77805 14 11 14C14.222 14 17.0935 13.4904 19.1228 12.7012C20.1407 12.3053 20.8962 11.8586 21.3801 11.4137C21.8633 10.9695 22 10.6033 22 10.3333V6.79772Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
};

export default IceHockeyIcon;
