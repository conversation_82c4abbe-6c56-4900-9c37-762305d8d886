import React from 'react'

const TableGmesIcon = () => {
  return (
<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4 18C1.79086 18 0 16.2091 0 14V4C0 1.79086 1.79086 0 4 0H14C16.2091 0 18 1.79086 18 4V14C18 16.2091 16.2091 18 14 18H4ZM6.5 5C6.5 5.82843 5.82843 6.5 5 6.5C4.17157 6.5 3.5 5.82843 3.5 5C3.5 4.17157 4.17157 3.5 5 3.5C5.82843 3.5 6.5 4.17157 6.5 5ZM5 10.5C5.82843 10.5 6.5 9.8284 6.5 9C6.5 8.1716 5.82843 7.5 5 7.5C4.17157 7.5 3.5 8.1716 3.5 9C3.5 9.8284 4.17157 10.5 5 10.5ZM5 14.5C5.82843 14.5 6.5 13.8284 6.5 13C6.5 12.1716 5.82843 11.5 5 11.5C4.17157 11.5 3.5 12.1716 3.5 13C3.5 13.8284 4.17157 14.5 5 14.5ZM13 10.5C13.8284 10.5 14.5 9.8284 14.5 9C14.5 8.1716 13.8284 7.5 13 7.5C12.1716 7.5 11.5 8.1716 11.5 9C11.5 9.8284 12.1716 10.5 13 10.5ZM14.5 13C14.5 13.8284 13.8284 14.5 13 14.5C12.1716 14.5 11.5 13.8284 11.5 13C11.5 12.1716 12.1716 11.5 13 11.5C13.8284 11.5 14.5 12.1716 14.5 13ZM13 6.5C13.8284 6.5 14.5 5.82843 14.5 5C14.5 4.17157 13.8284 3.5 13 3.5C12.1716 3.5 11.5 4.17157 11.5 5C11.5 5.82843 12.1716 6.5 13 6.5Z" fill="#C3C4C7" 
// style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
/>
</svg>

  )
}

export default TableGmesIcon