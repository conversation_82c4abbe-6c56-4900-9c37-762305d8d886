import React from 'react';

const EnhancedRTP = () => {
  return (
    <svg
      width="20"
      height="24"
      viewBox="0 0 20 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M12 7.57617C9.73373 7.57617 7.64843 7.8118 6.10061 8.21048C5.33439 8.40785 4.64339 8.65924 4.11985 8.97807C3.66326 9.25613 3 9.79346 3 10.6671C3 11.5408 3.66326 12.0781 4.11985 12.3562C4.37659 12.5126 4.6736 12.6527 5 12.7782V18.9097C5 19.6452 5.43495 20.182 5.85254 20.521C6.2814 20.8693 6.84 21.1378 7.44113 21.3443C8.65384 21.7608 10.2656 22.0007 12 22.0007C13.7344 22.0007 15.3462 21.7608 16.5589 21.3443C17.16 21.1378 17.7186 20.8693 18.1475 20.521C18.5651 20.182 19 19.6452 19 18.9097V12.7782C19.3264 12.6527 19.6234 12.5126 19.8801 12.3562C20.3367 12.0781 21 11.5408 21 10.6671C21 9.79346 20.3367 9.25613 19.8801 8.97807C19.3566 8.65924 18.6656 8.40785 17.8994 8.21048C16.3516 7.8118 14.2663 7.57617 12 7.57617ZM6.58568 10.2096C6.12194 10.3291 5.75124 10.4564 5.47154 10.5805C5.3958 10.6141 5.3958 10.7202 5.47154 10.7538C5.75124 10.8779 6.12194 11.0052 6.58568 11.1247C7.93329 11.4718 9.848 11.6975 12 11.6975C14.152 11.6975 16.0667 11.4718 17.4143 11.1247C17.8781 11.0052 18.2488 10.8779 18.5285 10.7538C18.6042 10.7202 18.6042 10.6141 18.5285 10.5805C18.2488 10.4564 17.8781 10.3291 17.4143 10.2096C16.0667 9.86248 14.152 9.63682 12 9.63682C9.848 9.63682 7.93329 9.86248 6.58568 10.2096Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M13.5529 1.28477C13.7372 0.905075 14.2631 0.905075 14.4473 1.28477C14.943 2.30623 15.1143 2.48269 16.1057 2.99342C16.4742 3.18327 16.4742 3.72512 16.1057 3.91497C15.1143 4.4257 14.943 4.60215 14.4473 5.62361C14.2631 6.00331 13.7372 6.00331 13.5529 5.62361C13.0572 4.60215 12.886 4.4257 11.8946 3.91497C11.526 3.72512 11.526 3.18327 11.8946 2.99342C12.886 2.48269 13.0572 2.30623 13.5529 1.28477Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M8.55292 3.34542C8.73718 2.96572 9.26308 2.96572 9.44734 3.34542C9.62162 3.70454 9.75715 3.84418 10.1057 4.02374C10.4742 4.21359 10.4742 4.75544 10.1057 4.94529C9.75715 5.12485 9.62162 5.26449 9.44734 5.62361C9.26308 6.00331 8.73718 6.00331 8.55292 5.62361C8.37864 5.26449 8.24311 5.12485 7.89456 4.94529C7.52603 4.75544 7.52603 4.21359 7.89456 4.02374C8.24311 3.84418 8.37864 3.70454 8.55292 3.34542Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
};

export default EnhancedRTP;
