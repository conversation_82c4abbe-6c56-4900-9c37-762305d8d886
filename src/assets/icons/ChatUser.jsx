// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ChatUser(props) {
  return (
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.01127 9.53201C7.91432 8.8731 7.58441 8.27082 7.08138 7.83437C6.57836 7.39792 5.93556 7.15628 5.26961 7.1532H4.64894C3.98298 7.15628 3.34019 7.39792 2.83718 7.83437C2.33416 8.27082 2.00424 8.8731 1.90727 9.53201L1.60977 11.6121C1.60027 11.6794 1.60655 11.7479 1.62811 11.8123C1.64968 11.8767 1.68594 11.9352 1.73402 11.9832C1.85069 12.0998 2.56644 12.6948 4.95986 12.6948C7.35327 12.6948 8.06727 12.1022 8.18571 11.9832C8.2338 11.9352 8.27006 11.8767 8.29162 11.8123C8.31318 11.7479 8.31948 11.6794 8.30996 11.6121L8.01127 9.53201Z"
        fill="#C3C4C7"
        style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M4.95703 6.86148C6.16515 6.86148 7.14453 5.88211 7.14453 4.67398C7.14453 3.46586 6.16515 2.48648 4.95703 2.48648C3.74891 2.48648 2.76953 3.46586 2.76953 4.67398C2.76953 5.88211 3.74891 6.86148 4.95703 6.86148Z"
        fill="#C3C4C7"
        style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M10.9171 0.700602C9.23248 0.667154 7.8322 2.02994 7.82814 3.71413C7.82709 4.12561 7.90745 4.52457 8.06684 4.89996C8.06915 4.90543 8.07132 4.91095 8.07328 4.91656C8.29161 5.53909 8.25255 6.21142 7.97087 6.79832C8.40858 6.9341 8.90096 6.82563 9.2385 6.49965C9.32166 6.41931 9.44612 6.39908 9.55049 6.44895C9.9735 6.65099 10.4279 6.75042 10.9009 6.74283C12.5723 6.71801 13.8816 5.39091 13.8815 3.72156C13.8816 2.08865 12.5517 0.73346 10.9171 0.700602ZM9.24032 4.17283C8.99833 4.17283 8.80212 3.97695 8.80212 3.73533C8.80212 3.49371 8.99833 3.29783 9.24032 3.29783C9.48238 3.29783 9.67859 3.49371 9.67859 3.73533C9.67859 3.97695 9.48238 4.17283 9.24032 4.17283ZM10.8549 4.17283C10.6128 4.17283 10.4166 3.97695 10.4166 3.73533C10.4166 3.49371 10.6128 3.29783 10.8549 3.29783C11.0969 3.29783 11.2931 3.49371 11.2931 3.73533C11.2931 3.97695 11.0969 4.17283 10.8549 4.17283ZM12.4693 4.17283C12.2274 4.17283 12.0311 3.97695 12.0311 3.73533C12.0311 3.49371 12.2274 3.29783 12.4693 3.29783C12.7114 3.29783 12.9076 3.49371 12.9076 3.73533C12.9076 3.97695 12.7114 4.17283 12.4693 4.17283Z"
        fill="#C3C4C7"
        style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default ChatUser;
