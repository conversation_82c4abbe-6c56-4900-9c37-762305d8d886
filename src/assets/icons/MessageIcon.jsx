// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function MessageIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_10832_32888)">
        <path
          d="M15.0026 0.833984H5.0026C3.89794 0.835308 2.8389 1.27472 2.05779 2.05583C1.27667 2.83695 0.837261 3.89599 0.835938 5.00065V11.6673C0.83715 12.6275 1.16935 13.5579 1.77654 14.3017C2.38373 15.0455 3.22878 15.5572 4.16927 15.7507V18.334C4.16925 18.4849 4.21018 18.6329 4.28771 18.7623C4.36523 18.8918 4.47644 18.9977 4.60947 19.0689C4.74249 19.1401 4.89234 19.1738 5.04304 19.1665C5.19373 19.1591 5.3396 19.111 5.4651 19.0273L10.2526 15.834H15.0026C16.1073 15.8327 17.1663 15.3932 17.9474 14.6121C18.7285 13.831 19.1679 12.772 19.1693 11.6673V5.00065C19.1679 3.89599 18.7285 2.83695 17.9474 2.05583C17.1663 1.27472 16.1073 0.835308 15.0026 0.833984ZM13.3359 10.834H6.66927C6.44826 10.834 6.2363 10.7462 6.08002 10.5899C5.92374 10.4336 5.83594 10.2217 5.83594 10.0007C5.83594 9.77964 5.92374 9.56768 6.08002 9.4114C6.2363 9.25512 6.44826 9.16732 6.66927 9.16732H13.3359C13.557 9.16732 13.7689 9.25512 13.9252 9.4114C14.0815 9.56768 14.1693 9.77964 14.1693 10.0007C14.1693 10.2217 14.0815 10.4336 13.9252 10.5899C13.7689 10.7462 13.557 10.834 13.3359 10.834ZM15.0026 7.50065H5.0026C4.78159 7.50065 4.56963 7.41285 4.41335 7.25657C4.25707 7.10029 4.16927 6.88833 4.16927 6.66732C4.16927 6.4463 4.25707 6.23434 4.41335 6.07806C4.56963 5.92178 4.78159 5.83398 5.0026 5.83398H15.0026C15.2236 5.83398 15.4356 5.92178 15.5919 6.07806C15.7481 6.23434 15.8359 6.4463 15.8359 6.66732C15.8359 6.88833 15.7481 7.10029 15.5919 7.25657C15.4356 7.41285 15.2236 7.50065 15.0026 7.50065Z"
          fill="#C3C4C7"
          style={{
            fill: '#C3C4C7',
            fillOpacity: 1,
          }}
        />
      </g>
      <defs>
        <clipPath id="clip0_10832_32888">
          <rect
            width="20"
            height="20"
            fill="white"
            style={{ fill: 'white', fillOpacity: 1 }}
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default MessageIcon;
