// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function SlotsIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M5.58221 1.43347C6.0952 0.687302 7.01628 0 8.5 0C9.98372 0 10.9048 0.687302 11.4178 1.43347C11.5521 1.62888 11.655 1.82253 11.7337 2H14C15.6569 2 17 3.34315 17 5V13H18.5C19.0523 13 19.5 12.5523 19.5 12V9.79198C18.617 9.4062 18 8.52516 18 7.5C18 6.11929 19.1193 5 20.5 5C21.8807 5 23 6.11929 23 7.5C23 8.52516 22.383 9.4062 21.5 9.79198V12C21.5 13.6569 20.1569 15 18.5 15H17V17C17 18.6569 15.6569 20 14 20H3C1.34315 20 0 18.6569 0 17V5C0 3.34315 1.34315 2 3 2H5.26631C5.34498 1.82253 5.44786 1.62888 5.58221 1.43347ZM15 8H12V14H15V8ZM2 14H5V8H2V14ZM7 8V14H10V8H7Z"
        fill="#C3C4C7"
        //  style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M2.75 10.75C2.75 10.3358 3.08579 10 3.5 10C3.91421 10 4.25 10.3358 4.25 10.75V11.25C4.25 11.6642 3.91421 12 3.5 12C3.08579 12 2.75 11.6642 2.75 11.25V10.75Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M7.75 10.75C7.75 10.3358 8.08579 10 8.5 10C8.91421 10 9.25 10.3358 9.25 10.75V11.25C9.25 11.6642 8.91421 12 8.5 12C8.08579 12 7.75 11.6642 7.75 11.25V10.75Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M12.75 10.75C12.75 10.3358 13.0858 10 13.5 10C13.9142 10 14.25 10.3358 14.25 10.75V11.25C14.25 11.6642 13.9142 12 13.5 12C13.0858 12 12.75 11.6642 12.75 11.25V10.75Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default SlotsIcon;
