const Counterstrike = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M10.4926 0.0585005C10.8721 -0.0734995 11.2868 0.0367505 11.6348 0.20925C11.8778 0.36675 12.1208 0.525 12.3931 0.6285C12.4141 0.6675 12.4568 0.7455 12.4778 0.78375C12.4673 0.81 12.4463 0.86325 12.4351 0.8895C12.6293 1.33275 12.7531 1.85775 12.5648 2.3235C12.4366 2.481 12.2138 2.505 12.0331 2.56575C12.0323 2.79075 12.0683 3.0135 12.0871 3.23625C12.4733 3.29925 12.8003 3.0285 13.1821 3.0555C13.2338 2.84025 13.2743 2.622 13.3426 2.41125C13.4213 2.55075 13.4251 2.71125 13.4258 2.86875C13.5256 2.89725 13.6276 2.92275 13.7296 2.94825C13.7303 2.9865 13.7311 3.06375 13.7311 3.102C15.4058 3.11175 17.0791 3.0945 18.7538 3.111C18.7493 3.16875 18.7418 3.28425 18.7381 3.342C18.7816 3.342 18.8693 3.34275 18.9136 3.34275C18.9121 3.03675 18.9136 2.73 18.9128 2.424C18.9556 2.4225 19.0388 2.4195 19.0801 2.41875C19.0808 2.6595 19.0816 2.90025 19.0846 3.141C19.1213 3.186 19.1941 3.27675 19.2308 3.32175C19.2586 3.28875 19.3133 3.225 19.3403 3.192C19.4678 3.312 19.3951 3.4755 19.3898 3.6225C19.8586 3.61725 20.3318 3.6495 20.7991 3.6105C20.8343 3.58425 20.9041 3.53325 20.9386 3.50775C21.0031 3.5325 21.0676 3.558 21.1313 3.5835C21.3421 3.57825 21.5536 3.5775 21.7651 3.57675C21.7606 3.68625 21.7576 3.79575 21.7531 3.90525C21.4808 3.90075 21.2078 3.8955 20.9401 3.942C20.7668 3.79275 20.5351 3.84 20.3258 3.834C20.0048 3.849 19.6733 3.792 19.3621 3.89475C19.1768 3.9465 18.9743 3.9345 18.8108 3.82875C18.7718 3.92625 18.8318 4.146 18.6676 4.137C18.0218 4.14525 17.3768 4.131 16.7318 4.1415C16.6433 4.188 16.5548 4.233 16.4663 4.27575C16.3733 4.55475 16.1063 4.7415 15.8783 4.91175C15.8498 5.34075 16.0193 5.74125 16.0846 6.1575C15.9571 6.19425 15.8288 6.23175 15.7036 6.27375C15.5228 6.702 15.6338 7.28625 15.2161 7.58475C14.8958 7.77225 14.6836 8.1795 14.2516 8.115C14.4713 8.29875 14.1916 8.241 14.2208 8.094C13.4393 8.02425 12.7808 7.56375 12.1021 7.21275C12.0706 7.40475 12.0203 7.59375 11.9686 7.782C12.2266 7.8315 12.4396 8.022 12.4853 8.28825C12.5678 8.868 12.4028 9.44325 12.4051 10.0245C12.4231 10.3657 12.2776 10.6785 12.0961 10.9567C11.9371 11.1375 11.6866 10.9515 11.4961 10.9253C11.4526 11.01 11.4106 11.094 11.3686 11.1795C11.4226 11.4067 11.4458 11.6437 11.5216 11.8657C11.9468 12.3382 12.3338 12.8497 12.6308 13.4145C12.9368 14.0535 13.2091 14.7083 13.4476 15.3765C13.4708 15.5063 13.5923 15.5527 13.6988 15.6C13.7131 15.7335 13.7273 15.8677 13.7401 16.0028C13.7813 16.0635 13.8241 16.125 13.8676 16.1865C13.8931 17.0107 13.7926 17.832 13.6651 18.6458C13.5863 19.0133 13.8001 19.3717 13.7131 19.7415C13.7228 19.9305 13.5931 20.0737 13.5323 20.241C13.5203 20.6318 13.5016 21.027 13.5443 21.417C13.5743 21.7027 13.8151 21.8872 13.9778 22.1002C14.1556 22.29 14.3048 22.545 14.5748 22.6125C14.8283 22.6882 15.0706 22.7992 15.2948 22.944C15.3631 23.1135 15.3503 23.2935 15.3181 23.4697C14.8306 23.5597 14.3326 23.5147 13.8398 23.5222C13.4603 23.5425 13.1213 23.337 12.7523 23.295C12.3953 23.286 11.9603 23.4637 11.6858 23.1427C11.8021 22.6973 11.8118 22.2187 12.0241 21.8032C12.0923 21.3322 11.9626 20.8552 11.9896 20.3797C11.9491 20.3452 11.8673 20.277 11.8268 20.2425C11.8561 19.932 11.7038 19.656 11.6528 19.3597C11.6198 18.9262 11.5801 18.4935 11.5306 18.0615C11.4916 17.8515 11.5996 17.6595 11.7473 17.52C11.7608 17.2778 11.7931 17.0378 11.8163 16.797C11.7803 16.6402 11.6566 16.5247 11.5763 16.3905C11.3093 16.5037 10.9486 16.4497 10.7896 16.1873C10.4123 15.624 10.0366 15.06 9.65484 14.4998C9.50484 14.49 9.34659 14.5057 9.20709 14.4457C9.04584 14.2792 8.96184 14.058 8.85834 13.8555C8.71059 14.022 8.72184 14.277 8.54709 14.4285C8.17734 14.7847 8.07909 15.3083 7.88634 15.765C7.73859 16.0613 7.82184 16.4182 7.65684 16.7062C7.56834 16.8615 7.34709 16.893 7.28859 17.07C7.21209 17.2612 7.12209 17.4592 6.94284 17.5763C6.74859 17.7037 6.69759 17.9467 6.61059 18.147C6.53184 18.3322 6.64884 18.5257 6.62109 18.7155C6.49809 19.3185 6.11559 19.8225 5.95509 20.4135C5.88159 20.6798 5.77659 20.979 5.46909 21.0352C5.11884 21.6637 4.92534 22.4332 5.12409 23.1412C5.24559 23.364 5.23884 23.6137 5.20134 23.859C4.64484 23.9025 4.07334 23.9775 3.52659 23.8215C3.39834 23.5665 3.41709 23.2665 3.44334 22.9905C3.61209 22.2652 3.83634 21.5527 3.97359 20.8207C3.80184 20.6677 3.84234 20.4338 3.84534 20.229C3.83934 19.8465 4.09809 19.5405 4.27809 19.227C4.34559 18.9052 4.31409 18.5587 4.47984 18.264C4.67484 17.8942 4.83909 17.4945 5.13984 17.196C5.19684 16.9327 5.39034 16.734 5.46834 16.4797C5.55159 16.2555 5.40609 16.0282 5.43534 15.7995C5.50659 15.1042 5.63259 14.4165 5.74134 13.7265C5.53509 13.5533 5.60184 13.278 5.53584 13.0447C5.56809 12.9525 5.60934 12.8648 5.65809 12.78C5.60634 12.732 5.55609 12.684 5.50434 12.636C5.55459 12.3907 5.54034 11.952 5.91459 12.0315C5.93784 11.2192 5.65734 10.4332 5.75109 9.62025C5.80134 9.67875 5.85009 9.738 5.90034 9.79725L5.87409 9.522C6.03084 9.43875 6.19734 9.37425 6.37209 9.34125C6.08709 9.30375 5.81034 9.231 5.53134 9.16575C5.46384 8.98725 5.50134 8.7975 5.52609 8.61525C5.63409 7.9995 5.67459 7.37625 5.69709 6.75225C6.01284 6.642 6.36009 6.73575 6.66834 6.588C6.57309 5.808 6.54834 4.9935 6.82509 4.245C7.04709 3.6075 7.47159 2.99325 8.13459 2.769C8.44734 2.694 8.84109 2.60925 9.06234 2.9145C9.12309 2.85825 9.18384 2.8005 9.24459 2.745C8.73909 1.737 9.34284 0.26475 10.4888 0.0570005L10.4926 0.0585005ZM14.5351 4.43025C14.4016 4.512 14.2711 4.60575 14.4113 4.755C14.3431 4.79625 14.2748 4.83525 14.2058 4.87275C14.0978 4.95675 13.8548 5.004 13.8826 5.17275C14.0041 5.3685 14.1091 5.57475 14.1953 5.78925C14.4608 5.57025 14.6723 5.2755 14.8156 4.96425C14.8456 4.9335 14.9063 4.87125 14.9363 4.84125C14.9228 4.698 14.8913 4.5585 14.8358 4.4265C14.7361 4.428 14.6356 4.42875 14.5351 4.43025Z" fill="#C3C4C7"
                />
            </svg>
        </>
    )
}
export default Counterstrike