import React from 'react';

function BitCoin(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_3039_8454)">
        <path
          d="M20 10C20 11.9778 19.4135 13.9112 18.3147 15.5557C17.2159 17.2002 15.6541 18.4819 13.8268 19.2388C11.9996 19.9957 9.98891 20.1937 8.0491 19.8079C6.10929 19.422 4.32746 18.4696 2.92894 17.0711C1.53041 15.6725 0.578004 13.8907 0.192152 11.9509C-0.1937 10.0111 0.0043328 8.00043 0.761209 6.17317C1.51809 4.3459 2.79981 2.78412 4.4443 1.6853C6.08879 0.58649 8.02219 0 10 0C12.6522 0 15.1957 1.05357 17.0711 2.92893C18.9464 4.8043 20 7.34783 20 10Z"
          fill="#F7931A"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.1076 4.56636L9.40578 5.18182L9.91941 3.26727L11.0685 3.57909L10.5749 5.41455L11.5121 5.66636L12.0067 3.81091L13.1758 4.12364L12.6721 5.98909C12.6721 5.98909 14.5812 6.41182 15.0303 7.96455C15.4794 9.51727 14.0431 10.3327 13.5994 10.3636C13.5994 10.3636 15.2721 11.2809 14.6976 13.0855C14.1231 14.89 12.3594 15.2127 10.504 14.7991L10.0003 16.7336L8.83123 16.4209L9.34487 14.5164L8.4176 14.2636L7.90396 16.1818L6.74396 15.87L7.2585 13.9609L4.89941 13.3245L5.49396 12.0045C5.49396 12.0045 6.15941 12.1864 6.41123 12.2464C6.66305 12.3064 6.82487 12.0445 6.89578 11.7827C6.96669 11.5209 8.03487 7.18182 8.13578 6.82455C8.23669 6.46727 8.19578 6.18818 7.77214 6.07818C7.34851 5.96818 6.77214 5.79636 6.77214 5.79636L7.1076 4.56636ZM9.42578 10.3127L8.78941 12.8427C8.78941 12.8427 11.9449 13.9818 12.3485 12.3791C12.7521 10.7764 9.42578 10.3127 9.42578 10.3127ZM9.71851 9.11273L10.3431 6.79455C10.3431 6.79455 13.0449 7.27818 12.7121 8.56818C12.3794 9.85818 10.7867 9.36364 9.71851 9.11273Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3039_8454">
          <rect width="20" height="20" rx="10" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default BitCoin;
