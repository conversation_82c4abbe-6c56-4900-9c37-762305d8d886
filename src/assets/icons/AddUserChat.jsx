// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function AddChatGroupIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_10023_18980)">
        <path
          d="M11.5352 8.98217C11.9917 9.4114 12.3892 9.90236 12.7258 10.4542C13.5765 9.92117 14.5814 9.61243 15.6573 9.61243C16.113 9.61243 16.5558 9.66817 16.9798 9.77244C16.2982 8.8802 15.477 8.2196 14.4386 7.73047C13.66 8.44084 12.6493 8.90083 11.5352 8.98217Z"
          fill={props?.fill || '#C3C4C7'}
        />
        <path
          d="M10.1181 15.149C10.1181 13.6035 10.7549 12.204 11.7796 11.1986C11.2023 10.1981 10.4146 9.44442 9.39122 8.91406C8.548 9.49455 7.52731 9.83493 6.42846 9.83493C5.33279 9.83493 4.31481 9.49654 3.47301 8.91911C1.34091 10.0722 0 12.4719 0 15.2429V19.4937H12.2263C10.943 18.4787 10.1181 16.9084 10.1181 15.149Z"
          fill={props?.fill || '#C3C4C7'}
        />
        <path
          d="M11.6653 4.59836C11.6653 5.72035 11.3106 6.76095 10.7076 7.6142L10.738 7.77743C10.883 7.79476 11.0295 7.80347 11.1762 7.80347C13.1876 7.80347 14.824 6.16707 14.824 4.15566C14.824 2.14422 13.1876 0.507812 11.1762 0.507812C10.7522 0.507812 10.337 0.580563 9.94531 0.720855C11.0013 1.67985 11.6653 3.06302 11.6653 4.59836Z"
          fill={props?.fill || '#C3C4C7'}
        />
        <path
          d="M7.35953 8.53166C9.53341 8.01779 10.8791 5.83895 10.3653 3.66509C9.8514 1.49123 7.67255 0.145536 5.49867 0.659404C3.32478 1.17327 1.97907 3.35211 2.49293 5.52597C3.00679 7.69983 5.18564 9.04553 7.35953 8.53166Z"
          fill={props?.fill || '#C3C4C7'}
        />
        <path
          d="M20.0017 15.1493C20.0017 12.7537 18.0527 10.8047 15.6571 10.8047C13.2615 10.8047 11.3125 12.7537 11.3125 15.1493C11.3125 17.5449 13.2615 19.4939 15.6571 19.4939C18.0527 19.494 20.0017 17.545 20.0017 15.1493ZM13.1266 15.7456V14.553H15.0608V12.6188H16.2534V14.553H18.1876V15.7456H16.2534V17.6799H15.0608V15.7456H13.1266Z"
          fill={props?.fill || '#C3C4C7'}
        />
      </g>
      <defs>
        <clipPath id="clip0_10023_18980">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default AddChatGroupIcon;
