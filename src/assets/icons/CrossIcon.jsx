// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CrossIcon(props) {
  return (
    <svg
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_9168_47991)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.7317 0.560347C11.5273 -0.235266 12.7362 0.973463 11.9404 1.76908L7.70946 6L11.9404 10.2309C12.736 11.0265 11.5273 12.2353 10.7317 11.4397L6.50073 7.20873L2.26981 11.4397C1.4742 12.2353 0.265466 11.0265 1.06108 10.2309L5.292 6L1.06108 1.76908C0.265467 0.973463 1.4742 -0.235266 2.26981 0.560347L6.50073 4.79127L10.7317 0.560347Z"
          fill="#C3C4C7"
        />
      </g>
      <defs>
        <clipPath id="clip0_9168_47991">
          <rect width="12" height="12" fill="white" transform="translate(0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default CrossIcon;
