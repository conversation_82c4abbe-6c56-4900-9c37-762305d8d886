// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function BaccaratIcon(props) {
  return (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 4.16667C0 1.86549 1.79082 0 4 0H8.8C10.316 0 11.6351 0.878499 12.3135 2.17344C11.7451 1.85031 11.0932 1.66667 10.4 1.66667H5.6C3.39082 1.66667 1.6 3.53216 1.6 5.83333V14.1667C1.6 14.8887 1.77627 15.5678 2.08652 16.1599C0.843359 15.4531 0 14.0792 0 12.5V4.16667Z" fill="#C3C4C7" 
// style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
/>
<path d="M10.9632 10.3175C10.9632 9.54834 10.3809 8.94168 9.64238 8.94168C8.904 8.94168 8.31123 9.54834 8.31123 10.3175C8.31123 11.0867 8.904 11.7042 9.64238 11.7042C10.3809 11.7042 10.9632 11.0867 10.9632 10.3175Z" fill="#C3C4C7" 
// style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.2 3.33333C4.99082 3.33333 3.2 5.19882 3.2 7.5V15.8333C3.2 18.1345 4.99082 20 7.2 20H12C14.2092 20 16 18.1345 16 15.8333V7.5C16 5.19882 14.2092 3.33333 12 3.33333H7.2ZM9.54883 13.1017L8.15527 15.4958H9.71523L11.9305 11.7692C12.1801 11.3467 12.3152 10.8375 12.3152 10.3175C12.3152 8.77917 11.1192 7.53334 9.64238 7.53334C8.16563 7.53334 6.96963 8.77917 6.96963 10.3175C6.96963 11.8233 8.11367 13.0475 9.54883 13.1017Z" fill="#C3C4C7" 
// style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
/>
</svg>

  );
}

export default BaccaratIcon;
