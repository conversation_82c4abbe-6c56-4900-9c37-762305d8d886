// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function SportsIcon(props) {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      {props?.activeMenu && (
        <defs>
          <linearGradient
            id="myRadialGradient"
            cx="50%"
            cy="50%"
            r="50%"
            fx="50%"
            fy="50%"
          >
            <stop offset="0%" stop-color="#E2BD68" />
            <stop offset="50%" stop-color="#ECD782" />
            <stop offset="100%" stop-color="#B57F44" />
          </linearGradient>
        </defs>
      )}
      <path
        d="M15.2109 6.39893C14.5999 7.01878 13.7447 7.46107 12.6817 7.69864L8.67525 8.59414C7.08605 8.94937 5.63423 9.74889 4.47685 10.9063C3.67682 11.7063 3.0536 12.6358 2.62183 13.6702C2.92956 15.5212 3.75028 17.2401 5.01356 18.6573L16.3472 7.32355C15.9837 6.99149 15.6043 6.68309 15.2109 6.39893Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M15.9059 15.8248L16.8014 11.8183C17.039 10.7553 17.4812 9.90016 18.1011 9.28914C17.8169 8.89579 17.5085 8.51635 17.1765 8.15283L5.84277 19.4865C7.25996 20.7498 8.97879 21.5705 10.8299 21.8782C11.8643 21.4465 12.7937 20.8232 13.5938 20.0232C14.7511 18.8658 15.5507 17.414 15.9059 15.8248Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M19.1418 4.52896C18.2386 3.72385 17.2131 3.09802 16.1086 2.67065C16.3627 3.64305 16.3016 4.56391 15.921 5.38005C15.9112 5.40108 15.9008 5.42172 15.8906 5.44248C16.3374 5.76461 16.7666 6.11511 17.1773 6.49341L19.1418 4.52896Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M22.253 9.78244C21.5429 9.46637 20.5972 9.2315 19.7087 9.60089C20.2178 10.4553 20.6325 11.3655 20.9497 12.3262C21.35 13.5387 21.5794 14.8128 21.6331 16.0943C22.201 14.8254 22.5 13.4396 22.5 12.0077C22.5 11.2496 22.4161 10.5043 22.253 9.78244Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M14.8992 4.79126C15.2685 3.90272 15.0337 2.95707 14.7176 2.24707C13.9958 2.08393 13.2505 2 12.4923 2C11.0605 2 9.67467 2.29902 8.40576 2.86688C9.68726 2.92063 10.9614 3.14995 12.1739 3.55026C13.1346 3.86746 14.0448 4.28223 14.8992 4.79126Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M19.0575 8.60934C19.0783 8.59917 19.099 8.58873 19.12 8.57892C19.9361 8.19839 20.857 8.13721 21.8294 8.39132C21.402 7.28683 20.7762 6.26135 19.9711 5.35815L18.0066 7.32264C18.3849 7.73339 18.7354 8.16255 19.0575 8.60934Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M8.41937 7.44963L12.4259 6.55418C12.9867 6.42881 13.6643 6.19034 14.1975 5.73835C11.8535 4.36596 9.11702 3.78493 6.33855 4.11378C6.01713 4.36538 5.70892 4.63801 5.41577 4.93117C3.65652 6.69045 2.63338 8.98945 2.5 11.4548C2.83714 10.9649 3.21997 10.5045 3.64749 10.077C4.96253 8.76197 6.61263 7.85346 8.41937 7.44963Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
      <path
        d="M17.9458 12.0741L17.0503 16.0806C16.6465 17.8874 15.7379 19.5375 14.4229 20.8524C13.9954 21.28 13.535 21.6628 13.0452 22C15.5105 21.8666 17.8094 20.8435 19.5688 19.0842C19.8619 18.791 20.1345 18.4828 20.3861 18.1614C20.7151 15.3829 20.134 12.6464 18.7616 10.3024C18.3096 10.8357 18.0711 11.5132 17.9458 12.0741Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
    </svg>
  );
}

export default SportsIcon;
