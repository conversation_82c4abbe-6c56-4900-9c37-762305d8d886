// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function AddUserModal(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_10516_6217)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.1167 6.79167C14.1167 8.88574 12.4191 10.5833 10.325 10.5833C8.23092 10.5833 6.53333 8.88574 6.53333 6.79167C6.53333 4.69759 8.23092 3 10.325 3C12.4191 3 14.1167 4.69759 14.1167 6.79167ZM8.80833 12.1C5.87662 12.1 3.5 14.4766 3.5 17.4083C3.5 18.6648 4.51856 19.6833 5.775 19.6833H11.3937C11.7215 19.6833 11.9076 19.283 11.7379 19.0026C11.3224 18.3161 11.0833 17.511 11.0833 16.65C11.0833 15.1075 11.8509 13.7442 13.025 12.9213C13.3005 12.7283 13.277 12.2852 12.9478 12.2154C12.5911 12.1398 12.221 12.1 11.8417 12.1H8.80833ZM15.6333 13.6167C16.0522 13.6167 16.3917 13.9562 16.3917 14.375V15.8917H17.9083C18.3272 15.8917 18.6667 16.2312 18.6667 16.65C18.6667 17.0688 18.3272 17.4083 17.9083 17.4083H16.3917V18.925C16.3917 19.3437 16.0522 19.6833 15.6333 19.6833C15.2145 19.6833 14.875 19.3437 14.875 18.925V17.4083H13.3583C12.9395 17.4083 12.6 17.0688 12.6 16.65C12.6 16.2312 12.9395 15.8917 13.3583 15.8917H14.875V14.375C14.875 13.9562 15.2145 13.6167 15.6333 13.6167Z"
          fill={props?.fill ||"white"}
        />
      </g>
      <defs>
        <clipPath id="clip0_10516_6217">
          <rect width="20" height="20" fill={props?.fill ||"white"} />
        </clipPath>
      </defs>
    </svg>
  );
}

export default AddUserModal;
