const Waterpolo = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M2 11.6025C2.08207 9.41645 2.88368 7.31829 4.28087 5.63249L5.20458 8.63249C5.25669 8.801 5.25387 8.98167 5.19654 9.14849C5.13921 9.31531 5.03032 9.45968 4.88554 9.56082L2 11.6025ZM11.5127 4.34249C11.6552 4.44563 11.8268 4.50116 12.0029 4.50116C12.179 4.50116 12.3506 4.44563 12.4932 4.34249L15.0313 2.46915C13.0531 1.84078 10.9277 1.8437 8.95118 2.47748L11.5127 4.34249ZM19.1128 9.55582L22 11.6025C21.9179 9.41645 21.1163 7.31829 19.7191 5.63249L18.8004 8.62749C18.7481 8.79575 18.7505 8.97623 18.8074 9.14303C18.8642 9.30982 18.9726 9.45434 19.117 9.55582H19.1128ZM7.57565 17.5692C7.52109 17.4022 7.41509 17.2567 7.27275 17.1535C7.13042 17.0502 6.95904 16.9945 6.78306 16.9942L3.32626 16.9883C4.54884 19.0974 6.49808 20.691 8.81087 21.4725L7.57565 17.5692ZM17.2144 16.9942C17.0382 16.9949 16.8667 17.0512 16.7245 17.1551C16.5822 17.259 16.4766 17.4051 16.4227 17.5725L15.149 21.4892C17.4792 20.7119 19.4441 19.1115 20.6737 16.9892L17.2144 16.9942ZM13.3868 13.3783L13.9973 11.5008C14.0246 11.4172 14.0247 11.3271 13.9976 11.2434C13.9704 11.1597 13.9174 11.0868 13.8462 11.035L12.2476 9.87499C12.1763 9.82323 12.0903 9.79535 12.0021 9.79535C11.9139 9.79535 11.8279 9.82323 11.7565 9.87499L10.1563 11.035C10.0848 11.0867 10.0315 11.1598 10.0042 11.2436C9.97688 11.3275 9.97693 11.4178 10.0043 11.5017L10.6157 13.3783C10.6428 13.462 10.6959 13.535 10.7671 13.5868C10.8384 13.6386 10.9242 13.6665 11.0124 13.6667H12.9918C13.0801 13.6667 13.1661 13.6388 13.2376 13.587C13.309 13.5352 13.3621 13.4622 13.3893 13.3783H13.3868ZM13.2549 21.9167C12.8395 21.9709 12.421 21.9987 12.0021 22C11.5673 21.9969 11.1332 21.9655 10.7026 21.9058L9.16916 17.0633C9.0082 16.5597 8.69097 16.1201 8.26327 15.8081C7.83558 15.4961 7.31956 15.3278 6.78974 15.3275L2.55957 15.32C2.35824 14.7497 2.20952 14.1623 2.11525 13.565L5.85017 10.9167C6.28305 10.6135 6.60831 10.181 6.77911 9.68156C6.94991 9.18211 6.95744 8.64147 6.8006 8.13749L5.61799 4.29832C6.10133 3.89924 6.62114 3.54631 7.17058 3.24415L10.5288 5.68915C10.9575 6.00087 11.4742 6.16882 12.0046 6.16882C12.535 6.16882 13.0517 6.00087 13.4804 5.68915L16.8102 3.23415C17.3683 3.53964 17.896 3.89736 18.3862 4.30249L17.2036 8.13666C17.0463 8.64049 17.0533 9.18114 17.2237 9.68073C17.394 10.1803 17.7189 10.6131 18.1515 10.9167L21.8889 13.565C21.7948 14.162 21.646 14.7492 21.4446 15.3192L17.2128 15.3275C16.6837 15.328 16.1684 15.4961 15.7412 15.8076C15.314 16.1192 14.997 16.558 14.8358 17.0608L13.2549 21.9167ZM12.9918 15.3333C13.4328 15.3334 13.8625 15.194 14.2193 14.9354C14.5761 14.6767 14.8416 14.3119 14.9778 13.8933L15.5883 12.0158C15.7257 11.5975 15.7262 11.1465 15.5899 10.7278C15.4536 10.3092 15.1875 9.94459 14.83 9.68666L13.229 8.52666C12.8728 8.26713 12.4431 8.12728 12.0021 8.12728C11.561 8.12728 11.1314 8.26713 10.7752 8.52666L9.17418 9.68666C8.81594 9.94493 8.54936 10.3102 8.41303 10.7297C8.2767 11.1492 8.27768 11.6011 8.41583 12.02L9.02718 13.8975C9.16338 14.3156 9.42892 14.6798 9.78563 14.9377C10.1423 15.1957 10.5718 15.3342 11.0124 15.3333H12.9918Z" fill="#C3C4C7"
                />
            </svg>
        </>
    )
}
export default Waterpolo