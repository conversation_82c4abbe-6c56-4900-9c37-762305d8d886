// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function RouletteIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.7363 16C18.3815 16.0002 18.8574 16.6026 18.709 17.2305L18.4131 18.4854C18.2025 19.377 17.4477 19.9998 16.5791 20H3.4209C2.55232 19.9998 1.79749 19.377 1.58691 18.4854L1.29102 17.2305C1.1426 16.6026 1.61853 16.0002 2.26367 16H17.7363ZM19 12C19.5523 12 20 12.4477 20 13V14C20 14.5523 19.5523 15 19 15H1C0.447715 15 0 14.5523 0 14V13C0 12.4477 0.447715 12 1 12H19ZM10 0C10.9023 1.11455e-05 11.6367 0.816852 11.6367 1.82031C11.6366 2.53995 11.2584 3.16308 10.7119 3.45801L10.8506 4.85254H12.8213C13.0465 4.14685 13.6525 3.63982 14.3633 3.63965C15.2656 3.63965 16 4.45649 16 5.45996C15.9998 6.46324 15.2654 7.2793 14.3633 7.2793C13.6524 7.27913 13.0465 6.77217 12.8213 6.06641H10.9707L11.3506 9.90137C11.4089 10.4897 10.9466 10.9999 10.3555 11H9.64453C9.05335 10.9999 8.59111 10.4897 8.64941 9.90137L9.0293 6.06641H7.17871C6.95352 6.77217 6.34756 7.27913 5.63672 7.2793C4.73455 7.2793 4.0002 6.46324 4 5.45996C4 4.45649 4.73443 3.63965 5.63672 3.63965C6.34751 3.63982 6.95348 4.14685 7.17871 4.85254H9.14941L9.28809 3.45801C8.74159 3.16309 8.36344 2.53996 8.36328 1.82031C8.36328 0.816846 9.09771 0 10 0Z"
        fill="#C3C4C7"
        //  style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default RouletteIcon;
