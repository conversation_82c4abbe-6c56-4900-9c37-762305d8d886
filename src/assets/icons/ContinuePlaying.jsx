// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from "react";

function ContinuePlayingIcon(props) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props} // ✅ allows external className, size overrides
    >
      {props?.activeMenu && (
        <defs>
          <linearGradient
            id="playCircleGradient"
            x1="12"
            y1="2"
            x2="12"
            y2="22"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0%" stopColor="#E2BD68" />
            <stop offset="50%" stopColor="#ECD782" />
            <stop offset="100%" stopColor="#B57F44" />
          </linearGradient>
        </defs>
      )}
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 22C17.5228 22 22 17.5228 22 12C22 
           6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 
           2 12C2 17.5228 6.47715 22 12 22ZM13 8C13 
           7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 
           11 8V12C11 12.2652 11.1054 12.5196 11.2929 
           12.7071L13.7929 15.2071C14.1834 15.5976 
           14.8166 15.5976 15.2071 15.2071C15.5976 
           14.8166 15.5976 14.1834 15.2071 
           13.7929L13 11.5858V8Z"
        fill={props?.activeMenu ? "url(#playCircleGradient)" : "#C3C4C7"}
      />
    </svg>
  );
}

export default ContinuePlayingIcon;
