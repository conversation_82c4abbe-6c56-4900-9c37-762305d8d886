// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function OriginalsIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.15756 0.878716C8.74197 0.0438443 9.91475 -0.257951 10.8306 0.371807C11.7823 1.02616 13.428 2.29085 14.8444 4.06625C16.2602 5.84076 17.5 8.19412 17.5 10.9999C17.5 15.9973 13.7478 19.9999 9 19.9999C4.25223 19.9999 0.5 15.9973 0.5 10.9999C0.5 8.96453 1.37408 6.36394 3.07753 4.2877C3.82386 3.37804 5.11166 3.4323 5.86407 4.15513L8.15756 0.878716ZM9 17.9999C10.4497 17.9999 11.625 16.6584 11.625 15.0035C11.625 13.2278 10.2438 11.9216 9.48519 11.3381C9.19556 11.1153 8.80444 11.1153 8.51481 11.3381C7.75618 11.9216 6.375 13.2278 6.375 15.0035C6.375 16.6584 7.55025 17.9999 9 17.9999Z" fill="#C3C4C7" 

// style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
/>
    </svg>
  );
}

export default OriginalsIcon;
