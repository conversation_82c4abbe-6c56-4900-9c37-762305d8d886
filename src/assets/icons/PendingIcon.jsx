import React from 'react';

function PendingIcon(props) {
  return (
    <svg
      width="14"
      height="16"
      viewBox="0 0 14 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.99984 13.8337C10.2215 13.8337 12.8332 11.222 12.8332 8.00033C12.8332 4.77866 10.2215 2.16699 6.99984 2.16699C3.77818 2.16699 1.1665 4.77866 1.1665 8.00033C1.1665 11.222 3.77818 13.8337 6.99984 13.8337ZM7.58317 5.08366C7.58317 4.76149 7.322 4.50033 6.99984 4.50033C6.67767 4.50033 6.4165 4.76149 6.4165 5.08366V7.41699H5.24984C4.92767 7.41699 4.6665 7.67816 4.6665 8.00033C4.6665 8.32249 4.92767 8.58366 5.24984 8.58366H6.99984C7.322 8.58366 7.58317 8.32249 7.58317 8.00033V5.08366Z"
        fill="#C3C4C7"
      />
    </svg>
  );
}

export default PendingIcon;
