// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function EyeOpenIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="#ffffff"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M19.74,9.298C19.564,9.091,15.367,4.232,10,4.232S0.436,9.091,0.26,9.299c-0.346,0.405-0.346,0.995,0,1.402c0.176,0.207,4.373,5.066,9.74,5.066s9.564-4.859,9.741-5.066C20.086,10.294,20.086,9.704,19.74,9.298z M11.641,7.151C12,6.954,12.504,7.181,12.764,7.655c0.26,0.477,0.178,1.021-0.184,1.219c-0.359,0.197-0.864-0.028-1.124-0.504C11.195,7.895,11.277,7.35,11.641,7.151z M10,14.008c-3.749,0-6.904-2.879-7.996-4.009C2.742,9.237,4.42,7.68,6.588,6.745C6.164,7.39,5.917,8.157,5.917,8.983c0,2.257,1.827,4.085,4.083,4.085c2.255,0,4.083-1.828,4.083-4.085c0-0.826-0.247-1.595-0.67-2.238c2.167,0.935,3.845,2.492,4.583,3.254C16.904,11.129,13.749,14.008,10,14.008z" />
    </svg>
  );
}

export default EyeOpenIcon;
