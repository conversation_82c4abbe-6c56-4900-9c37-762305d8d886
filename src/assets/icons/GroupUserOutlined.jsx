// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function GroupUser({ color = '#C3C4C7', ...props }) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.3409 5.95169C12.3409 8.31754 10.423 10.2354 8.05716 10.2354C5.69133 10.2354 3.77344 8.31754 3.77344 5.95169C3.77344 3.58586 5.69133 1.66797 8.05716 1.66797C10.423 1.66797 12.3409 3.58586 12.3409 5.95169Z"
        style={{ fill: color, fillOpacity: 1 }}
      />
      <path
        d="M1.66797 15.8346C1.66797 13.5335 3.53345 11.668 5.83464 11.668H10.283C12.5841 11.668 14.4496 13.5335 14.4496 15.8346V16.668C14.4496 17.5885 13.7034 18.3346 12.783 18.3346H3.33464C2.41416 18.3346 1.66797 17.5885 1.66797 16.668V15.8346Z"
        style={{ fill: color, fillOpacity: 1 }}
      />
      <path
        d="M12.2705 9.53866C12.0849 9.7565 12.1352 10.0997 12.408 10.1862C12.7352 10.2901 13.0837 10.3461 13.4453 10.3461C15.338 10.3461 16.8723 8.81175 16.8723 6.91913C16.8723 5.02648 15.338 3.49219 13.4453 3.49219C13.2402 3.49219 13.1212 3.71016 13.1972 3.90065C13.4506 4.53505 13.5899 5.22731 13.5899 5.95213C13.5899 7.32045 13.0933 8.57275 12.2705 9.53866Z"
        style={{ fill: color, fillOpacity: 1 }}
      />
      <path
        d="M15.7004 16.0926C15.7004 16.3227 15.8869 16.5092 16.117 16.5092H17.5021C17.9624 16.5092 18.3354 16.1362 18.3354 15.6759V14.4069C18.3354 12.7961 17.0296 11.4902 15.4188 11.4902H14.7759C14.375 11.4902 14.1737 12.0426 14.4316 12.3494C15.2234 13.2912 15.7004 14.5064 15.7004 15.8331V16.0926Z"
        style={{ fill: color, fillOpacity: 1 }}
      />
    </svg>
  );
}

export default GroupUser;
