// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function HomeIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.492 0.791919C11.1691 0.709985 10.8309 0.709985 10.5081 0.791919C10.1337 0.886919 9.81304 1.11858 9.55745 1.30322L9.48653 1.3543L0.9179 7.48872C0.468834 7.81021 0.365416 8.43488 0.68691 8.88394C1.0084 9.33301 1.63307 9.43643 2.08213 9.11493L3.00003 8.45779L3.00003 14.2419C3.00002 15.0469 3.00001 15.7112 3.04422 16.2524C3.09015 16.8145 3.18871 17.3312 3.436 17.8166C3.8195 18.5692 4.43142 19.1811 5.18407 19.5646C5.6694 19.8119 6.18611 19.9105 6.7482 19.9564C7.28939 20.0006 7.95375 20.0006 8.75871 20.0006H13.2414C14.0463 20.0006 14.7107 20.0006 15.2519 19.9564C15.814 19.9105 16.3307 19.8119 16.816 19.5646C17.5686 19.1811 18.1806 18.5692 18.5641 17.8166C18.8113 17.3312 18.9099 16.8145 18.9558 16.2524C19.0001 15.7112 19 15.0469 19 14.2419V8.45779L19.9179 9.11493C20.367 9.43643 20.9917 9.33301 21.3131 8.88394C21.6346 8.43488 21.5312 7.81021 21.0822 7.48872L12.5135 1.3543L12.4426 1.30322C12.187 1.11858 11.8663 0.886919 11.492 0.791919Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default HomeIcon;
