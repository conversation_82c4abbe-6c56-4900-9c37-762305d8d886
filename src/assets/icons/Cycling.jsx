const Cycling = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M5.58333 22C4.67684 22 3.7907 21.7334 3.03697 21.234C2.28325 20.7345 1.69579 20.0246 1.34889 19.194C1.00199 18.3634 0.911221 17.4495 1.08807 16.5678C1.26492 15.686 1.70144 14.8761 2.34243 14.2404C2.98342 13.6047 3.80009 13.1718 4.68917 12.9964C5.57825 12.821 6.49981 12.9111 7.3373 13.2551C8.1748 13.5991 8.89061 14.1817 9.39424 14.9292C9.89786 15.6767 10.1667 16.5555 10.1667 17.4545C10.1652 18.6596 9.68186 19.8149 8.82263 20.6671C7.96341 21.5192 6.79846 21.9986 5.58333 22ZM18.4167 22C17.5102 22 16.624 21.7334 15.8703 21.234C15.1166 20.7345 14.5291 20.0246 14.1822 19.194C13.8353 18.3634 13.7446 17.4495 13.9214 16.5678C14.0982 15.686 14.5348 14.8761 15.1758 14.2404C15.8168 13.6047 16.6334 13.1718 17.5225 12.9964C18.4116 12.821 19.3331 12.9111 20.1706 13.2551C21.0081 13.5991 21.7239 14.1817 22.2276 14.9292C22.7312 15.6767 23 16.5555 23 17.4545C22.9985 18.6596 22.5152 19.8149 21.656 20.6671C20.7967 21.5192 19.6318 21.9986 18.4167 22ZM12.9167 17.4545V13.8182C12.9166 13.6873 12.8881 13.558 12.8331 13.4391C12.778 13.3202 12.6977 13.2144 12.5977 13.1291L10.5013 11.3427C10.4022 11.2594 10.3224 11.1558 10.2674 11.0391C10.2123 10.9223 10.1833 10.7952 10.1823 10.6664C10.178 10.5479 10.1981 10.4298 10.2411 10.3192C10.2841 10.2086 10.3492 10.1077 10.4325 10.0227L12.2328 8.60182C12.4138 8.4399 12.6519 8.35546 12.8954 8.36686C13.1389 8.37827 13.3679 8.48461 13.5327 8.66273L15.916 10.8445C16.0854 11 16.3076 11.0863 16.5384 11.0864H19.3333C19.5764 11.0864 19.8096 10.9906 19.9815 10.8201C20.1534 10.6496 20.25 10.4184 20.25 10.1773C20.25 9.93617 20.1534 9.70494 19.9815 9.53445C19.8096 9.36396 19.5764 9.26818 19.3333 9.26818H16.895L14.8077 7.35727C14.3191 6.85835 13.6553 6.56547 12.9544 6.53956C12.2535 6.51366 11.5696 6.75672 11.0448 7.21818L9.2445 8.63636C8.95712 8.89661 8.7287 9.21438 8.57435 9.56868C8.42 9.92298 8.34324 10.3057 8.34913 10.6916C8.35502 11.0775 8.44344 11.4578 8.60853 11.8073C8.77362 12.1568 9.01164 12.4676 9.30683 12.7191L11.0833 14.2355V17.4545C11.0833 17.6957 11.1799 17.9269 11.3518 18.0974C11.5237 18.2679 11.7569 18.3636 12 18.3636C12.2431 18.3636 12.4763 18.2679 12.6482 18.0974C12.8201 17.9269 12.9167 17.6957 12.9167 17.4545ZM16.125 2C15.6718 2 15.2287 2.13329 14.8518 2.38302C14.475 2.63275 14.1812 2.98771 14.0078 3.40299C13.8343 3.81828 13.7889 4.27525 13.8774 4.71611C13.9658 5.15698 14.1841 5.56194 14.5045 5.87979C14.825 6.19763 15.2334 6.41409 15.6779 6.50179C16.1225 6.58948 16.5832 6.54447 17.002 6.37245C17.4207 6.20044 17.7786 5.90914 18.0305 5.53539C18.2823 5.16164 18.4167 4.72223 18.4167 4.27273C18.4167 3.66996 18.1752 3.09189 17.7455 2.66567C17.3157 2.23945 16.7328 2 16.125 2Z" fill="#C3C4C7"
                />
            </svg>
        </>
    )
}
export default Cycling