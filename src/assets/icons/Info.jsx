// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function InfoIcon(props) {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.3637 2.63579C11.8491 -0.8784 6.15075 -0.878791 2.63579 2.63579C-0.878791 6.15036 -0.8784 11.8487 2.63579 15.3637C6.15036 18.8775 11.8487 18.8779 15.3637 15.3637C18.8779 11.8487 18.8775 6.15075 15.3637 2.63579ZM10.1732 12.9127C10.1732 13.561 9.64772 14.0865 8.99935 14.0865C8.35098 14.0865 7.82548 13.561 7.82548 12.9127V8.21716C7.82548 7.56879 8.35098 7.04329 8.99935 7.04329C9.64772 7.04329 10.1732 7.56879 10.1732 8.21716V12.9127ZM8.97862 6.21062C8.30246 6.21062 7.8517 5.73168 7.86578 5.14044C7.8517 4.52064 8.30247 4.05617 8.99231 4.05617C9.68255 4.05617 10.1192 4.52103 10.1337 5.14044C10.1333 5.73168 9.68294 6.21062 8.97862 6.21062Z"
        fill="#C3C4C7"

        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default InfoIcon;
