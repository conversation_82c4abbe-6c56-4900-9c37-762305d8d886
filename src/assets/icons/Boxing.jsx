const Boxing = () => {
    return (
        <>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M3.00027 7.8975C2.99292 7.12635 3.13558 6.36134 3.42002 5.64664C3.70446 4.93194 4.12504 4.28168 4.65749 3.73339C5.18995 3.1851 5.82373 2.74964 6.52228 2.45213C7.22082 2.15462 7.9703 2.00095 8.72746 2L12.8183 2C14.055 2.00202 15.258 2.41116 16.2478 3.1664C17.2376 3.92164 17.9612 4.98252 18.3107 6.19083C17.2705 6.25033 16.292 6.71234 15.5752 7.48237C14.8584 8.25239 14.4576 9.27224 14.4546 10.3333V13.6667C14.4546 13.8877 14.5408 14.0996 14.6943 14.2559C14.8477 14.4122 15.0558 14.5 15.2728 14.5C15.4898 14.5 15.6979 14.4122 15.8513 14.2559C16.0048 14.0996 16.091 13.8877 16.091 13.6667V10.475C16.0768 9.81669 16.3089 9.17752 16.7402 8.68701C17.1715 8.1965 17.7698 7.89133 18.4138 7.83333C18.747 7.81509 19.0804 7.86626 19.3936 7.9837C19.7067 8.10114 19.9931 8.2824 20.2352 8.51639C20.4772 8.75038 20.6699 9.03218 20.8014 9.34459C20.9329 9.65699 21.0005 9.99343 21 10.3333V12.8958C20.9685 13.8735 20.7115 14.8299 20.2499 15.6876C19.7882 16.5454 19.1348 17.2804 18.3426 17.8333H4.49507L4.18662 17.3842C3.41431 16.2632 3.00005 14.9269 3.00027 13.5575V10.7033C3.49555 11.0033 4.06041 11.1632 4.63661 11.1667H12.0001C12.2171 11.1667 12.4252 11.0789 12.5787 10.9226C12.7321 10.7663 12.8183 10.5543 12.8183 10.3333C12.8183 10.1123 12.7321 9.90036 12.5787 9.74408C12.4252 9.5878 12.2171 9.5 12.0001 9.5H4.63661C4.21228 9.5039 3.80335 9.33833 3.49746 9.03877C3.19158 8.73921 3.01309 8.32951 3.00027 7.8975ZM4.63661 19.5V20.3333C4.63661 20.7754 4.80901 21.1993 5.11588 21.5118C5.42275 21.8244 5.83896 22 6.27295 22H16.091C16.525 22 16.9412 21.8244 17.248 21.5118C17.5549 21.1993 17.7273 20.7754 17.7273 20.3333V19.5H4.63661Z"
                    style={{ fill: "#C3C4C7", fillOpacity: 1 }}
                />
            </svg>
        </>
    )
}
export default Boxing