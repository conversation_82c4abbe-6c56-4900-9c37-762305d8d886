import React from 'react';

const SoccerIcon = () => {
  return (
    <svg
      width="24"
      height="20"
      viewBox="0 0 24 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M13.3145 2.7165L11.7634 3.84346C10.7119 4.60739 9.28811 4.60739 8.23664 3.84346L6.68552 2.7165C5.70018 3.16572 4.82112 3.80955 4.09797 4.59935L4.68969 6.42047C5.09131 7.65654 4.65134 9.01064 3.59988 9.77457L2.05004 10.9006C2.17282 11.9979 2.51741 13.0274 3.03811 13.9443H4.95469C6.25437 13.9443 7.40624 14.7812 7.80786 16.0172L8.40018 17.8402C8.91642 17.9449 9.45133 18 10 18C10.5487 18 11.0836 17.9449 11.5998 17.8402L12.1921 16.0172C12.5938 14.7812 13.7456 13.9443 15.0453 13.9443H16.9619C17.4826 13.0274 17.8272 11.9979 17.95 10.9006L16.4001 9.77458C15.3487 9.01064 14.9087 7.65654 15.3103 6.42047L15.902 4.59935C15.1789 3.80955 14.2998 3.16572 13.3145 2.7165ZM6.46638 0.642529C7.56638 0.226949 8.75784 0 10 0C11.2422 0 12.4336 0.226949 13.5336 0.642529C15.2253 1.28166 16.6968 2.3653 17.8066 3.74987C19.1786 5.46163 20 7.63626 20 10C20 10.1562 19.9964 10.3117 19.9893 10.4664C19.9042 12.3176 19.3152 14.039 18.3566 15.4943C17.0376 17.4968 15.017 18.9982 12.6411 19.6473C11.7986 19.8774 10.9128 20 10 20C9.08716 20 8.20136 19.8774 7.3589 19.6473C4.98303 18.9982 2.96244 17.4968 1.64343 15.4943C0.684843 14.039 0.0958272 12.3176 0.0107015 10.4664C0.00359112 10.3117 0 10.1562 0 10C0 7.63626 0.821363 5.46163 2.19344 3.74987C3.30325 2.3653 4.77466 1.28166 6.46638 0.642529ZM8.23664 7.04508C9.28811 6.28115 10.7119 6.28115 11.7634 7.04508L12.2654 7.40983C13.3168 8.17376 13.7568 9.52786 13.3552 10.7639L13.1634 11.3541C12.7618 12.5902 11.6099 13.4271 10.3103 13.4271H9.68973C8.39005 13.4271 7.23818 12.5902 6.83656 11.3541L6.6448 10.7639C6.24318 9.52786 6.68315 8.17376 7.73462 7.40983L8.23664 7.04508Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
};

export default SoccerIcon;
