// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function LiveCasinoIcon(props) {
  return (
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20ZM8.78167 6.78296C8.44976 6.55666 8 6.79436 8 7.19607V12.8039C8 13.2056 8.44976 13.4433 8.78167 13.217L12.8941 10.4131C13.1852 10.2146 13.1852 9.78537 12.8941 9.58689L8.78167 6.78296Z" fill="#C3C4C7"
//  style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
 />
</svg>

  );
}

export default LiveCasinoIcon;
