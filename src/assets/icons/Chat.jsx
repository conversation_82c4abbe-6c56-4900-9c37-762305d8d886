// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ChatIcon(props) {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      {props?.activeMenu && (
        <defs>
          <linearGradient
            id="myRadialGradient"
            cx="50%"
            cy="50%"
            r="50%"
            fx="50%"
            fy="50%"
          >
            <stop offset="0%" stop-color="#E2BD68" />
            <stop offset="50%" stop-color="#ECD782" />
            <stop offset="100%" stop-color="#B57F44" />
          </linearGradient>
        </defs>
      )}
      <path
        d="M12.5 2C6.97715 2 2.5 6.47715 2.5 12C2.5 13.5031 2.83228 14.9313 3.42835 16.2129C3.44023 16.2384 3.43961 16.2529 3.43944 16.2552L2.66111 19.4449C2.30913 20.8874 3.59629 22.197 5.04464 21.8699L8.37217 21.1185C8.37431 21.1184 8.38803 21.1179 8.41234 21.1288C9.66153 21.689 11.0458 22 12.5 22C18.0228 22 22.5 17.5228 22.5 12C22.5 6.47715 18.0228 2 12.5 2Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
      />
    </svg>
  );
}

export default ChatIcon;
