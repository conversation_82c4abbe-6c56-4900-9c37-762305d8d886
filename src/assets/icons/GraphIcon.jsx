// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function GraphIcon(props) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.3398 5.00098C16.3398 3.89641 17.2353 3.00098 18.3398 3.00098H19.0098C20.1144 3.00098 21.0098 3.89641 21.0098 5.00098V19.001C21.0098 20.1055 20.1144 21.001 19.0098 21.001H18.3398C17.2353 21.001 16.3398 20.1055 16.3398 19.001V5.00098Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M11.6699 8.50098C10.5654 8.50098 9.66992 9.39641 9.66992 10.501V19.001C9.66992 20.1055 10.5654 21.001 11.6699 21.001H12.3399C13.4445 21.001 14.3399 20.1055 14.3399 19.001V10.501C14.3399 9.39641 13.4445 8.50098 12.3399 8.50098H11.6699Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M5 14.001C3.89543 14.001 3 14.8964 3 16.001V19.001C3 20.1055 3.89543 21.001 5 21.001H5.67C6.77457 21.001 7.67 20.1055 7.67 19.001V16.001C7.67 14.8964 6.77457 14.001 5.67 14.001H5Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default GraphIcon;
