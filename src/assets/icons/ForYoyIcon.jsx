// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ForYouIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
       {props?.activeMenu && (
        <defs>
          <linearGradient
            id="myRadialGradient"
            cx="50%"
            cy="50%"
            r="50%"
            fx="50%"
            fy="50%"
          >
            <stop offset="0%" stop-color="#E2BD68" />
            <stop offset="50%" stop-color="#ECD782" />
            <stop offset="100%" stop-color="#B57F44" />
          </linearGradient>
        </defs>
      )}
      <path
        d="M8.24219 0C6.88457 0 5.89844 1.13313 5.89844 2.51242C5.89844 4.00881 7.08122 5.01572 8.73795 6.42611C9.01764 6.66421 9.31084 6.91382 9.61414 7.17922C9.83512 7.37254 10.1649 7.3725 10.3859 7.17922C10.689 6.91396 10.982 6.66447 11.2616 6.42647C12.9186 5.01573 14.1016 4.00864 14.1016 2.51246C14.1016 1.13305 13.1154 0 11.7578 0C11.0649 0 10.4602 0.304492 10 0.882813C9.53977 0.304492 8.93512 0 8.24219 0Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M10.4323 11.4405C10.1979 11.1843 9.79094 11.1865 9.56008 11.4489L6.22555 15.2379C6.03083 15.4593 6.03083 15.7908 6.22555 16.0121L9.56008 19.8012C9.79024 20.0627 10.1969 20.0669 10.4323 19.8096L13.8986 16.0205C14.1034 15.7966 14.1034 15.4534 13.8986 15.2295L10.4323 11.4405Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M5.85938 11.875C7.15172 11.875 8.20312 10.8236 8.20312 9.53125C8.20312 8.44067 7.45441 7.52168 6.44402 7.26133C6.49031 5.95949 5.43598 4.84375 4.10156 4.84375C2.76719 4.84375 1.71281 5.95949 1.7591 7.26133C0.748711 7.52168 0 8.44067 0 9.53125C0 10.8236 1.05141 11.875 2.34375 11.875C2.77047 11.875 3.17062 11.7599 3.51562 11.5598V13.0469H2.92969C2.60609 13.0469 2.34375 13.3092 2.34375 13.6328C2.34375 13.9564 2.60609 14.2188 2.92969 14.2188H5.27344C5.59703 14.2188 5.85938 13.9564 5.85938 13.6328C5.85938 13.3092 5.59703 13.0469 5.27344 13.0469H4.6875V11.5598C5.0325 11.7599 5.43266 11.875 5.85938 11.875Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M16.2843 4.84228C16.0634 4.649 15.7335 4.649 15.5126 4.84228C15.2095 5.10752 14.9164 5.35699 14.6369 5.59497C12.9798 7.00572 11.7969 8.01286 11.7969 9.50908C11.7969 10.8885 12.7831 12.0215 14.1406 12.0215C14.5696 12.0215 14.9645 11.9045 15.3125 11.6785V13.0469H14.7266C14.403 13.0469 14.1406 13.3092 14.1406 13.6328C14.1406 13.9564 14.403 14.2188 14.7266 14.2188H17.0703C17.3939 14.2188 17.6562 13.9564 17.6562 13.6328C17.6562 13.3092 17.3939 13.0469 17.0703 13.0469H16.4844V11.6785C16.8323 11.9045 17.2273 12.0215 17.6562 12.0215C19.0139 12.0215 20 10.8884 20 9.50908C20 8.0127 18.8173 7.00585 17.1606 5.59546C16.8808 5.35733 16.5876 5.10771 16.2843 4.84228Z"
        fill={props?.activeMenu ? 'url(#myRadialGradient)' : '#C3C4C7'}
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default ForYouIcon;
