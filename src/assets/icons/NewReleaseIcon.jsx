// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function NewReleaseIcon(props) {
  return (
    <svg
      width="18"
      height="16"
      viewBox="0 0 18 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 14.3759V10H3L3.77381 14.6429C3.90433 15.426 4.58191 16 5.37586 16C6.27285 16 7 15.2728 7 14.3759Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M0 6.9988C0 5.34194 1.34315 3.9988 3 3.9988H6L8 3.99919L14.2375 0.349856C15.9042 -0.625217 18 0.576775 18 2.50768V11.4964C18 13.4281 15.9026 14.63 14.236 13.6533L8 9.9988H6H3C1.34315 9.9988 0 8.65565 0 6.9988Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
      <path
        d="M8 3.99919L14.2375 0.349856C15.9042 -0.625217 18 0.576775 18 2.50768V11.4964C18 13.4281 15.9026 14.63 14.236 13.6533L8 9.9988V3.99919Z"
        fill="#C3C4C7"
        // style="fill:#C3C4C7;fill:color(display-p3 0.7647 0.7686 0.7804);fill-opacity:1;"
      />
    </svg>
  );
}

export default NewReleaseIcon;
